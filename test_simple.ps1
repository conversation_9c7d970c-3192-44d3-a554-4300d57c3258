# Simple ticket detail test

$baseUrl = "http://localhost:8081/api"

Write-Host "=== Ticket Detail Test ===" -ForegroundColor Green

# Admin login
Write-Host "1. Admin login..." -ForegroundColor Yellow
$loginResult = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body '{"username":"admin","password":"admin123456"}'
$adminToken = $loginResult.data.token
$adminHeaders = @{"Authorization" = "Bearer $adminToken"}
Write-Host "Admin login success" -ForegroundColor Green

# Create test ticket
Write-Host "2. Create test ticket..." -ForegroundColor Yellow
$ticketData = '{"title":"Test Ticket for Detail Page","description":"This is a test ticket for detail page","type":"requirement","priority":"high"}'
$ticket = Invoke-RestMethod -Uri "$baseUrl/tickets" -Method POST -Headers $adminHeaders -ContentType "application/json" -Body $ticketData
$ticketId = $ticket.data.id
Write-Host "Ticket created with ID: $ticketId" -ForegroundColor Green

# Get ticket detail
Write-Host "3. Get ticket detail..." -ForegroundColor Yellow
$ticketDetail = Invoke-RestMethod -Uri "$baseUrl/tickets/$ticketId" -Method GET -Headers $adminHeaders
Write-Host "Ticket detail retrieved successfully" -ForegroundColor Green

# Add comment
Write-Host "4. Add comment..." -ForegroundColor Yellow
$commentData = "{`"ticket_id`":$ticketId,`"content`":`"Admin comment`"}"
$comment = Invoke-RestMethod -Uri "$baseUrl/comments" -Method POST -Headers $adminHeaders -ContentType "application/json" -Body $commentData
Write-Host "Comment added successfully" -ForegroundColor Green

# Get comments
Write-Host "5. Get comments..." -ForegroundColor Yellow
$comments = Invoke-RestMethod -Uri "$baseUrl/comments/ticket/$ticketId" -Method GET -Headers $adminHeaders
Write-Host "Comments retrieved: $($comments.data.Count) comments" -ForegroundColor Green

Write-Host "=== Test Complete ===" -ForegroundColor Green
Write-Host "Visit: http://localhost:5173/tickets/$ticketId" -ForegroundColor Cyan
