<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary" @click="showCreateDialog = true" :icon="Plus">
        添加用户
      </el-button>
    </div>

    <el-card>
      <el-table :data="users" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="name" label="姓名" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" />
        <el-table-column prop="team_name" label="团队" min-width="120">
          <template #default="{ row }">
            <el-tag v-if="row.team_name" type="info">
              {{ row.team_name }}
            </el-tag>
            <el-tag v-else type="warning">
              未分配
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="role" label="角色" width="120">
          <template #default="{ row }">
            <el-tag :type="getRoleType(row.role)">
              {{ getRoleName(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="editUser(row)"
              :icon="Edit"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteUserAction(row)"
              :icon="Delete"
              :disabled="row.id === currentUserId"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑用户' : '添加用户'"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="large"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
            maxlength="50"
            :disabled="isEditing"
          />
        </el-form-item>

        <el-form-item label="姓名" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入姓名"
            maxlength="50"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            maxlength="100"
            show-password
          />
          <div v-if="isEditing" class="form-tip">
            <el-text type="info" size="small">
              留空则不修改密码
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-radio-group v-model="form.role">
            <el-radio value="admin">管理员</el-radio>
            <el-radio value="developer">开发者</el-radio>
            <el-radio value="user">普通用户</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="团队" prop="team_id">
          <el-select v-model="form.team_id" placeholder="请选择团队" style="width: 100%">
            <el-option
              v-for="team in teams"
              :key="team.id"
              :label="team.name"
              :value="team.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getUsers, createUser, updateUser, deleteUser } from '@/api/auth'

// 用户接口类型定义
interface User {
  id: number
  username: string
  name: string
  email: string
  role: string
  team_id: number
  team_name: string
  created_at: string
}

interface CreateUserRequest {
  username: string
  name: string
  email: string
  password: string
  role: string
  team_id: number
}

interface UpdateUserRequest {
  name: string
  email: string
  password?: string
  role: string
  team_id: number
}

const userStore = useUserStore()
const users = ref<User[]>([])
const teams = ref<any[]>([])
const loading = ref(false)

// 创建/编辑用户对话框
const showCreateDialog = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const isEditing = ref(false)
const editingUserId = ref<number | null>(null)

// 表单数据
const form = ref<CreateUserRequest>({
  username: '',
  name: '',
  email: '',
  password: '',
  role: 'user',
  team_id: 0
})

// 当前用户ID
const currentUserId = computed(() => userStore.user?.id)

// 表单验证规则
const rules = computed<FormRules>(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度应在 3 到 50 个字符之间', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度应在 2 到 50 个字符之间', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: isEditing.value 
    ? [{ min: 6, message: '密码至少需要 6 个字符', trigger: 'blur' }]
    : [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码至少需要 6 个字符', trigger: 'blur' }
      ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  team_id: [
    { required: true, message: '请选择团队', trigger: 'change' }
  ]
}))

onMounted(() => {
  loadUsers()
  loadTeams()
})

const loadUsers = async () => {
  loading.value = true
  try {
    const response = await getUsers()
    if (response.code === 200) {
      users.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error: any) {
    console.error('加载用户列表失败:', error)
    ElMessage.error(error.response?.data?.message || '获取用户列表失败')
  } finally {
    loading.value = false
  }
}

const loadTeams = async () => {
  try {
    // 导入团队API
    const { getTeams } = await import('@/api/team')
    const response = await getTeams()
    teams.value = response.data || response || []
  } catch (error: any) {
    console.error('加载团队列表失败:', error)
    ElMessage.error('获取团队列表失败')
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getRoleName = (role: string) => {
  const names = {
    admin: '管理员',
    developer: '开发者',
    user: '普通用户'
  }
  return names[role as keyof typeof names] || role
}

const getRoleType = (role: string) => {
  const types = {
    admin: 'danger',
    developer: 'warning',
    user: 'primary'
  }
  return types[role as keyof typeof types] || 'primary'
}

// 编辑用户
const editUser = (user: User) => {
  isEditing.value = true
  editingUserId.value = user.id
  form.value = {
    username: user.username,
    name: user.name,
    email: user.email,
    password: '',
    role: user.role,
    team_id: user.team_id || 0
  }
  showCreateDialog.value = true
}

// 删除用户
const deleteUserAction = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteUser(user.id)
    if (response.code === 200) {
      ElMessage.success('用户删除成功')
      await loadUsers()
    } else {
      ElMessage.error(response.message || '删除用户失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error(error.response?.data?.message || '删除用户失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    if (isEditing.value) {
      // 更新用户
      const updateData: any = {
        name: form.value.name,
        email: form.value.email,
        role: form.value.role
      }
      if (form.value.password) {
        updateData.password = form.value.password
      }
      const response = await updateUser(editingUserId.value!, updateData)

      if (response.code === 200) {
        ElMessage.success('用户更新成功')
      } else {
        ElMessage.error(response.message || '更新用户失败')
        return
      }
    } else {
      // 创建用户
      const response = await createUser(form.value)

      if (response.code === 200) {
        ElMessage.success('用户创建成功')
      } else {
        ElMessage.error(response.message || '创建用户失败')
        return
      }
    }

    showCreateDialog.value = false
    resetForm()
    await loadUsers()
  } catch (error: any) {
    console.error('操作失败:', error)
    ElMessage.error(error.response?.data?.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  isEditing.value = false
  editingUserId.value = null
  form.value = {
    username: '',
    name: '',
    email: '',
    password: '',
    role: 'user',
    team_id: 0
  }
  formRef.value?.clearValidate()
}

// 处理对话框关闭
const handleCloseDialog = async () => {
  if (form.value.username || form.value.name || form.value.email || form.value.password) {
    try {
      await ElMessageBox.confirm(
        '确定要关闭吗？未保存的内容将会丢失。',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }
  
  showCreateDialog.value = false
  resetForm()
}
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.form-tip {
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
