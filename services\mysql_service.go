package services

import (
	"database/sql"
	"fmt"
	"sync"
	"time"

	"ReqFlow/models"
	_ "github.com/go-sql-driver/mysql"
)

type MySQLService struct {
	connections map[uint]*sql.DB
	mutex       sync.RWMutex
}

var mysqlService *MySQLService
var mysqlOnce sync.Once

// GetMySQLService 获取MySQL服务实例
func GetMySQLService() *MySQLService {
	mysqlOnce.Do(func() {
		mysqlService = &MySQLService{
			connections: make(map[uint]*sql.DB),
		}
	})
	return mysqlService
}

// GetConnection 获取MySQL连接
func (s *MySQLService) GetConnection(connID uint, config models.MySQLConnection) (*sql.DB, error) {
	s.mutex.RLock()
	if db, exists := s.connections[connID]; exists {
		// 检查连接是否有效
		if err := db.<PERSON>(); err == nil {
			s.mutex.RUnlock()
			return db, nil
		}
		// 连接无效，删除它
		s.mutex.RUnlock()
		s.CloseConnection(connID)
	} else {
		s.mutex.RUnlock()
	}

	// 创建新连接
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.Charset,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接MySQL失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("MySQL连接测试失败: %v", err)
	}

	// 保存连接
	s.mutex.Lock()
	s.connections[connID] = db
	s.mutex.Unlock()

	return db, nil
}

// CloseConnection 关闭连接
func (s *MySQLService) CloseConnection(connID uint) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if db, exists := s.connections[connID]; exists {
		db.Close()
		delete(s.connections, connID)
	}
}

// TestConnection 测试连接
func (s *MySQLService) TestConnection(config models.MySQLConnection) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=true&loc=Local",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.Charset,
	)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("连接MySQL失败: %v", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		return fmt.Errorf("MySQL连接测试失败: %v", err)
	}

	return nil
}

// GetDatabases 获取数据库列表
func (s *MySQLService) GetDatabases(connID uint, config models.MySQLConnection) ([]models.MySQLDatabase, error) {
	db, err := s.GetConnection(connID, config)
	if err != nil {
		return nil, err
	}

	query := `
		SELECT 
			SCHEMA_NAME as name,
			DEFAULT_CHARACTER_SET_NAME as charset,
			DEFAULT_COLLATION_NAME as collation
		FROM information_schema.SCHEMATA 
		WHERE SCHEMA_NAME NOT IN ('information_schema', 'performance_schema', 'mysql', 'sys')
		ORDER BY SCHEMA_NAME
	`

	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询数据库列表失败: %v", err)
	}
	defer rows.Close()

	var databases []models.MySQLDatabase
	for rows.Next() {
		var db models.MySQLDatabase
		if err := rows.Scan(&db.Name, &db.Charset, &db.Collation); err != nil {
			return nil, fmt.Errorf("扫描数据库信息失败: %v", err)
		}
		databases = append(databases, db)
	}

	return databases, nil
}

// GetTables 获取表列表
func (s *MySQLService) GetTables(connID uint, config models.MySQLConnection, database string) ([]models.MySQLTable, error) {
	db, err := s.GetConnection(connID, config)
	if err != nil {
		return nil, err
	}

	query := `
		SELECT 
			TABLE_NAME as name,
			ENGINE as engine,
			TABLE_ROWS as rows,
			DATA_LENGTH as data_length,
			TABLE_COMMENT as comment
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? AND TABLE_TYPE = 'BASE TABLE'
		ORDER BY TABLE_NAME
	`

	rows, err := db.Query(query, database)
	if err != nil {
		return nil, fmt.Errorf("查询表列表失败: %v", err)
	}
	defer rows.Close()

	var tables []models.MySQLTable
	for rows.Next() {
		var table models.MySQLTable
		var engine, comment sql.NullString
		if err := rows.Scan(&table.Name, &engine, &table.Rows, &table.DataLength, &comment); err != nil {
			return nil, fmt.Errorf("扫描表信息失败: %v", err)
		}
		table.Engine = engine.String
		table.Comment = comment.String
		tables = append(tables, table)
	}

	return tables, nil
}

// GetColumns 获取表结构
func (s *MySQLService) GetColumns(connID uint, config models.MySQLConnection, database, table string) ([]models.MySQLColumn, error) {
	db, err := s.GetConnection(connID, config)
	if err != nil {
		return nil, err
	}

	query := `
		SELECT 
			COLUMN_NAME as field,
			COLUMN_TYPE as type,
			IS_NULLABLE as null_flag,
			COLUMN_KEY as key_flag,
			COLUMN_DEFAULT as default_value,
			EXTRA as extra,
			COLUMN_COMMENT as comment
		FROM information_schema.COLUMNS 
		WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
		ORDER BY ORDINAL_POSITION
	`

	rows, err := db.Query(query, database, table)
	if err != nil {
		return nil, fmt.Errorf("查询表结构失败: %v", err)
	}
	defer rows.Close()

	var columns []models.MySQLColumn
	for rows.Next() {
		var col models.MySQLColumn
		var defaultValue, comment sql.NullString
		if err := rows.Scan(&col.Field, &col.Type, &col.Null, &col.Key, &defaultValue, &col.Extra, &comment); err != nil {
			return nil, fmt.Errorf("扫描列信息失败: %v", err)
		}
		col.Default = defaultValue.String
		col.Comment = comment.String
		columns = append(columns, col)
	}

	return columns, nil
}

// ExecuteQuery 执行查询
func (s *MySQLService) ExecuteQuery(connID uint, config models.MySQLConnection, query string) (*models.MySQLQueryResult, error) {
	db, err := s.GetConnection(connID, config)
	if err != nil {
		return nil, err
	}

	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("执行查询失败: %v", err)
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("获取列名失败: %v", err)
	}

	// 读取数据
	var data [][]interface{}
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("扫描数据失败: %v", err)
		}

		// 转换数据类型
		row := make([]interface{}, len(columns))
		for i, val := range values {
			if val == nil {
				row[i] = nil
			} else if b, ok := val.([]byte); ok {
				row[i] = string(b)
			} else {
				row[i] = val
			}
		}
		data = append(data, row)
	}

	return &models.MySQLQueryResult{
		Columns: columns,
		Rows:    data,
		Total:   int64(len(data)),
	}, nil
}
