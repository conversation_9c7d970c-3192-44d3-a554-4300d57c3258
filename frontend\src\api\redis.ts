import api from './index'

// Redis连接接口
export interface RedisConnection {
  id?: number
  name: string
  host: string
  port: number
  password?: string
  database: number
  description?: string
  is_active?: boolean
  user_id?: number
  created_at?: string
  updated_at?: string
}

// Redis键接口
export interface RedisKey {
  key: string
  type: string
  ttl: number
  size: number
  value?: any
}

// Redis信息接口
export interface RedisInfo {
  version: string
  mode: string
  role: string
  connected_clients: number
  used_memory: string
  used_memory_human: string
  total_keys: number
  uptime: number
  stats: Record<string, string>
}

// Redis数据库接口
export interface RedisDatabase {
  index: number
  keys: number
}

// 创建Redis连接
export const createRedisConnection = (data: RedisConnection) => {
  return api.post('/redis/connections', data)
}

// 获取Redis连接列表
export const getRedisConnections = () => {
  return api.get('/redis/connections')
}

// 更新Redis连接
export const updateRedisConnection = (id: number, data: RedisConnection) => {
  return api.put(`/redis/connections/${id}`, data)
}

// 删除Redis连接
export const deleteRedisConnection = (id: number) => {
  return api.delete(`/redis/connections/${id}`)
}

// 测试Redis连接
export const testRedisConnection = (data: RedisConnection) => {
  return api.post('/redis/connections/test', data)
}

// 获取Redis服务器信息
export const getRedisInfo = (id: number) => {
  return api.get(`/redis/connections/${id}/info`)
}

// 获取Redis数据库列表
export const getRedisDatabases = (id: number) => {
  return api.get(`/redis/connections/${id}/databases`)
}

// 获取Redis键列表
export const getRedisKeys = (id: number, params?: {
  pattern?: string
  cursor?: number
  count?: number
}) => {
  return api.get(`/redis/connections/${id}/keys`, { params })
}

// 获取Redis键值
export const getRedisValue = (id: number, key: string) => {
  return api.get(`/redis/connections/${id}/keys/${encodeURIComponent(key)}`)
}

// 设置Redis键值
export const setRedisValue = (id: number, data: {
  key: string
  type: string
  value: any
  ttl?: number
}) => {
  return api.post(`/redis/connections/${id}/keys`, data)
}

// 删除Redis键
export const deleteRedisKey = (id: number, key: string) => {
  return api.delete(`/redis/connections/${id}/keys/${encodeURIComponent(key)}`)
}

// 执行Redis命令
export const executeRedisCommand = (id: number, command: any[]) => {
  return api.post(`/redis/connections/${id}/execute`, { command })
}

// 切换Redis数据库
export const switchRedisDatabase = (id: number, database: number) => {
  return api.put(`/redis/connections/${id}/database`, { database })
}
