<template>
  <div class="redis-container">
    <!-- 左侧连接列表 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <h3>Redis 连接</h3>
        <div class="header-actions">
          <el-button size="small" type="primary" @click="showConnectionDialog = true">
            <el-icon><Plus /></el-icon>
            新建
          </el-button>
          <el-button size="small" text @click="loadConnections">
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="connections-list">
        <div
          v-for="connection in connections"
          :key="connection.id"
          :class="['connection-item', { active: activeConnection?.id === connection.id }]"
          @click="selectConnection(connection)"
        >
          <div class="connection-info">
            <div class="connection-name">{{ connection.name }}</div>
            <div class="connection-details">
              {{ connection.host }}:{{ connection.port }}/{{ connection.database }}
            </div>
          </div>
          <div class="connection-actions">
            <el-button size="small" text @click.stop="editConnection(connection)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button size="small" text type="danger" @click.stop="deleteConnection(connection)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>

        <el-empty v-if="connections.length === 0" description="暂无Redis连接" />
      </div>
    </div>

    <!-- 右侧主内容区 -->
    <div class="main-content">
      <div v-if="!activeConnection" class="welcome-screen">
        <el-empty description="请选择一个Redis连接">
          <el-button type="primary" @click="showConnectionDialog = true">
            创建新连接
          </el-button>
        </el-empty>
      </div>

      <div v-else class="redis-client">
        <!-- 连接信息栏 -->
        <div class="connection-bar">
          <div class="connection-info">
            <h3>{{ activeConnection.name }}</h3>
            <span class="connection-status">
              <el-icon><Connection /></el-icon>
              已连接到 {{ activeConnection.host }}:{{ activeConnection.port }}/{{ activeConnection.database }}
            </span>
          </div>
          <div class="connection-actions">
            <el-button size="small" @click="refreshInfo">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button size="small" @click="showCommandDialog = true">
              <el-icon><Monitor /></el-icon>
              命令行
            </el-button>
          </div>
        </div>

        <!-- 标签页 -->
        <el-tabs v-model="activeTab" type="card" @tab-change="onTabChange">
          <!-- 概览标签页 -->
          <el-tab-pane label="概览" name="overview">
            <div class="overview-content">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-card title="服务器信息">
                    <div v-if="redisInfo" class="info-grid">
                      <div class="info-item">
                        <span class="label">版本:</span>
                        <span class="value">{{ redisInfo.version }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">模式:</span>
                        <span class="value">{{ redisInfo.mode }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">角色:</span>
                        <span class="value">{{ redisInfo.role }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">连接数:</span>
                        <span class="value">{{ redisInfo.connected_clients }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">内存使用:</span>
                        <span class="value">{{ redisInfo.used_memory_human }}</span>
                      </div>
                      <div class="info-item">
                        <span class="label">总键数:</span>
                        <span class="value">{{ redisInfo.total_keys }}</span>
                      </div>
                    </div>
                    <el-skeleton v-else :rows="6" animated />
                  </el-card>
                </el-col>
                <el-col :span="12">
                  <el-card title="数据库">
                    <div v-if="databases" class="databases-list">
                      <div
                        v-for="db in databases"
                        :key="db.index"
                        :class="['database-item', { active: db.index === activeConnection.database }]"
                        @click="switchDatabase(db.index)"
                      >
                        <span class="db-name">DB{{ db.index }}</span>
                        <span class="db-keys">{{ db.keys }} 键</span>
                      </div>
                    </div>
                    <el-skeleton v-else :rows="4" animated />
                  </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>

          <!-- 键浏览器标签页 -->
          <el-tab-pane label="键浏览器" name="keys">
            <div class="keys-browser">
              <!-- 搜索栏 -->
              <div class="search-bar">
                <el-input
                  v-model="searchPattern"
                  placeholder="搜索键 (支持通配符 *)"
                  @keydown.enter="searchKeys"
                >
                  <template #append>
                    <el-button @click="searchKeys">
                      <el-icon><Search /></el-icon>
                    </el-button>
                  </template>
                </el-input>
                <el-button type="primary" @click="showEditKeyDialog = true">
                  <el-icon><Plus /></el-icon>
                  添加键
                </el-button>
                <el-button @click="addTestData">
                  添加测试数据
                </el-button>
              </div>

              <!-- 键列表 -->
              <div class="keys-content">
                <div class="keys-list">
                  <el-table
                    :data="keys"
                    v-loading="keysLoading"
                    @row-click="selectKey"
                    highlight-current-row
                  >
                    <el-table-column prop="key" label="键名" min-width="200" />
                    <el-table-column prop="type" label="类型" width="80" />
                    <el-table-column prop="size" label="大小" width="80" />
                    <el-table-column prop="ttl" label="TTL" width="80">
                      <template #default="scope">
                        {{ scope.row.ttl === -1 ? '永久' : scope.row.ttl + 's' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                      <template #default="scope">
                        <el-button size="small" text @click.stop="editKey(scope.row)">
                          <el-icon><Edit /></el-icon>
                        </el-button>
                        <el-button size="small" text type="danger" @click.stop="deleteKey(scope.row)">
                          <el-icon><Delete /></el-icon>
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                  <!-- 分页 -->
                  <div class="pagination" v-if="keys.length > 0">
                    <el-button @click="loadMoreKeys" :loading="keysLoading">
                      加载更多
                    </el-button>
                  </div>
                </div>

                <!-- 键值详情 -->
                <div class="key-detail" v-if="selectedKey">
                  <div class="detail-header">
                    <h4>{{ selectedKey.key }}</h4>
                    <div class="detail-actions">
                      <el-button size="small" @click="refreshKeyValue">
                        <el-icon><Refresh /></el-icon>
                      </el-button>
                      <el-button size="small" type="primary" @click="editSelectedKey">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-button>
                    </div>
                  </div>
                  <div class="detail-content">
                    <div class="key-meta">
                      <span>类型: {{ selectedKey.type }}</span>
                      <span>大小: {{ selectedKey.size }}</span>
                      <span>TTL: {{ selectedKey.ttl === -1 ? '永久' : selectedKey.ttl + 's' }}</span>
                    </div>
                    <div class="key-value">
                      <pre>{{ formatValue(selectedKey.value) }}</pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 新建/编辑连接对话框 -->
    <el-dialog
      v-model="showConnectionDialog"
      :title="editingConnection ? '编辑Redis连接' : '新建Redis连接'"
      width="600px"
      @close="resetConnectionForm"
    >
      <el-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        label-width="100px"
      >
        <el-form-item label="连接模式">
          <el-radio-group v-model="connectionMode" @change="onConnectionModeChange">
            <el-radio value="server">服务器模式</el-radio>
            <el-radio value="local">本地模式</el-radio>
          </el-radio-group>
          <div class="mode-description">
            <p v-if="connectionMode === 'server'" class="text-sm text-gray-600">
              通过ReqFlow服务器连接到Redis服务器（适用于远程Redis）
            </p>
            <p v-if="connectionMode === 'local'" class="text-sm text-gray-600">
              直接从您的浏览器连接到本地Redis服务器（适用于本地Redis）
            </p>
          </div>
        </el-form-item>

        <el-form-item label="连接名称" prop="name">
          <el-input v-model="connectionForm.name" placeholder="请输入连接名称" />
        </el-form-item>

        <el-form-item label="主机地址" prop="host">
          <el-input
            v-model="connectionForm.host"
            :placeholder="connectionMode === 'local' ? '本地模式请输入 localhost 或 127.0.0.1' : '请输入主机地址或IP'"
          />
        </el-form-item>

        <el-form-item label="端口" prop="port">
          <el-input-number v-model="connectionForm.port" :min="1" :max="65535" />
        </el-form-item>

        <el-form-item label="密码">
          <el-input
            v-model="connectionForm.password"
            type="password"
            placeholder="请输入密码（可选）"
            show-password
          />
        </el-form-item>

        <el-form-item label="数据库" prop="database">
          <el-input-number v-model="connectionForm.database" :min="0" :max="15" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="connectionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showConnectionDialog = false">取消</el-button>
        <el-button type="primary" @click="testConnection" :loading="testing">
          测试连接
        </el-button>
        <el-button type="primary" @click="saveConnection" :loading="saving">
          {{ editingConnection ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑键值对话框 -->
    <el-dialog
      v-model="showEditKeyDialog"
      :title="editingKey ? '编辑键值' : '新建键值'"
      width="600px"
      @close="resetKeyForm"
    >
      <el-form
        ref="keyFormRef"
        :model="keyForm"
        :rules="keyRules"
        label-width="80px"
      >
        <el-form-item label="键名" prop="key">
          <el-input
            v-model="keyForm.key"
            placeholder="请输入键名"
            :disabled="!!editingKey"
          />
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select v-model="keyForm.type" placeholder="请选择类型" :disabled="!!editingKey">
            <el-option label="字符串 (String)" value="string" />
            <el-option label="哈希 (Hash)" value="hash" />
            <el-option label="列表 (List)" value="list" />
            <el-option label="集合 (Set)" value="set" />
            <el-option label="有序集合 (ZSet)" value="zset" />
          </el-select>
        </el-form-item>

        <el-form-item label="值" prop="value">
          <el-input
            v-model="keyForm.value"
            type="textarea"
            :rows="8"
            placeholder="请输入键值"
          />
        </el-form-item>

        <el-form-item label="TTL(秒)">
          <el-input-number
            v-model="keyForm.ttl"
            :min="-1"
            placeholder="过期时间，-1表示永不过期"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditKeyDialog = false">取消</el-button>
        <el-button type="primary" @click="saveKey" :loading="saving">
          {{ editingKey ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Edit,
  Delete,
  Connection,
  Monitor,
  Search
} from '@element-plus/icons-vue'
import {
  getRedisConnections,
  createRedisConnection,
  updateRedisConnection,
  deleteRedisConnection as deleteRedisConnectionAPI,
  testRedisConnection,
  getRedisInfo,
  getRedisDatabases,
  getRedisKeys,
  getRedisValue,
  setRedisValue,
  deleteRedisKey,
  switchRedisDatabase,
  type RedisConnection,
  type RedisKey,
  type RedisInfo,
  type RedisDatabase
} from '@/api/redis'

// 响应式数据
const connections = ref<RedisConnection[]>([])
const activeConnection = ref<RedisConnection | null>(null)
const redisInfo = ref<RedisInfo | null>(null)
const databases = ref<RedisDatabase[]>([])
const keys = ref<RedisKey[]>([])
const selectedKey = ref<RedisKey | null>(null)
const keysLoading = ref(false)
const searchPattern = ref('*')
const keysCursor = ref(0)
const activeTab = ref('overview')

// 对话框状态
const showConnectionDialog = ref(false)
const showCommandDialog = ref(false)
const showAddKeyDialog = ref(false)
const showEditKeyDialog = ref(false)
const editingConnection = ref<RedisConnection | null>(null)
const editingKey = ref<RedisKey | null>(null)
const saving = ref(false)
const testing = ref(false)
const connectionFormRef = ref()
const keyFormRef = ref()

// 连接表单
const connectionForm = reactive<RedisConnection>({
  name: '',
  host: '',
  port: 6379,
  password: '',
  database: 0,
  description: ''
})

// 键值表单
const keyForm = reactive({
  key: '',
  type: 'string',
  value: '',
  ttl: -1
})

// 表单验证规则
const connectionRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  database: [
    { required: true, message: '请选择数据库', trigger: 'blur' },
    { type: 'number', min: 0, max: 15, message: '数据库索引必须在0-15之间', trigger: 'blur' }
  ]
}

const keyRules = {
  key: [
    { required: true, message: '请输入键名', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入键值', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  loadConnections()
})

// 方法
const loadConnections = async () => {
  try {
    const response = await getRedisConnections()
    connections.value = response.data
  } catch (error) {
    ElMessage.error('获取连接列表失败')
  }
}

const selectConnection = async (connection: RedisConnection) => {
  activeConnection.value = connection
  await refreshInfo()
}

const refreshInfo = async () => {
  if (!activeConnection.value) return

  try {
    // 获取服务器信息
    const infoResponse = await getRedisInfo(activeConnection.value.id!)
    redisInfo.value = infoResponse.data

    // 获取数据库列表
    const dbResponse = await getRedisDatabases(activeConnection.value.id!)
    databases.value = dbResponse.data

    // 如果在键浏览器标签页，加载键列表
    if (activeTab.value === 'keys') {
      await searchKeys()
    }
  } catch (error) {
    ElMessage.error('获取Redis信息失败')
  }
}

const searchKeys = async () => {
  if (!activeConnection.value) return

  keysLoading.value = true
  keysCursor.value = 0
  keys.value = []

  try {
    const response = await getRedisKeys(activeConnection.value.id!, {
      pattern: searchPattern.value,
      cursor: keysCursor.value,
      count: 100
    })
    keys.value = response.data.keys
    keysCursor.value = response.data.cursor
  } catch (error) {
    ElMessage.error('搜索键失败')
  } finally {
    keysLoading.value = false
  }
}

const loadMoreKeys = async () => {
  if (!activeConnection.value || keysCursor.value === 0) return

  keysLoading.value = true
  try {
    const response = await getRedisKeys(activeConnection.value.id!, {
      pattern: searchPattern.value,
      cursor: keysCursor.value,
      count: 100
    })
    keys.value.push(...response.data.keys)
    keysCursor.value = response.data.cursor
  } catch (error) {
    ElMessage.error('加载更多键失败')
  } finally {
    keysLoading.value = false
  }
}

const selectKey = async (key: RedisKey) => {
  if (!activeConnection.value) return

  try {
    const response = await getRedisValue(activeConnection.value.id!, key.key)
    selectedKey.value = response.data
  } catch (error) {
    ElMessage.error('获取键值失败')
  }
}

const formatValue = (value: any) => {
  if (typeof value === 'string') {
    return value
  }
  return JSON.stringify(value, null, 2)
}

// 连接管理方法
const saveConnection = async () => {
  if (!connectionFormRef.value) return

  try {
    await connectionFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (editingConnection.value) {
      await updateRedisConnection(editingConnection.value.id!, connectionForm)
      ElMessage.success('连接更新成功')
    } else {
      await createRedisConnection(connectionForm)
      ElMessage.success('连接创建成功')
    }

    showConnectionDialog.value = false
    await loadConnections()
  } catch (error: any) {
    ElMessage.error('保存连接失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  if (!connectionFormRef.value) return

  try {
    await connectionFormRef.value.validate()
  } catch (error) {
    return
  }

  testing.value = true
  try {
    await testRedisConnection(connectionForm)
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message))
  } finally {
    testing.value = false
  }
}

const resetConnectionForm = () => {
  editingConnection.value = null
  Object.assign(connectionForm, {
    name: '',
    host: '',
    port: 6379,
    password: '',
    database: 0,
    description: ''
  })
  if (connectionFormRef.value) {
    connectionFormRef.value.clearValidate()
  }
}

// 占位方法
const editConnection = (connection: RedisConnection) => {
  ElMessage.info('编辑连接功能开发中...')
}

const deleteConnection = (connection: RedisConnection) => {
  ElMessage.info('删除连接功能开发中...')
}

const switchDatabase = async (dbIndex: number) => {
  if (!activeConnection.value) return

  try {
    await switchRedisDatabase(activeConnection.value.id!, dbIndex)

    // 更新当前连接的数据库索引
    activeConnection.value.database = dbIndex

    // 重新获取信息和数据
    await refreshInfo()

    // 如果当前在键浏览器标签页，刷新键列表
    if (activeTab.value === 'keys') {
      keys.value = [] // 清空当前键列表
      selectedKey.value = null // 清空选中的键
      await searchKeys() // 重新搜索键
    }

    ElMessage.success(`已切换到数据库 DB${dbIndex}`)
  } catch (error: any) {
    ElMessage.error('切换数据库失败: ' + (error.response?.data?.message || error.message))
  }
}

const editKey = (key: RedisKey) => {
  editingKey.value = key
  keyForm.key = key.key
  keyForm.type = key.type
  keyForm.value = formatValue(key.value)
  keyForm.ttl = key.ttl
  showEditKeyDialog.value = true
}

const deleteKey = async (key: RedisKey) => {
  if (!activeConnection.value) return

  try {
    await ElMessageBox.confirm(
      `确定要删除键 "${key.key}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteRedisKey(activeConnection.value.id!, key.key)
    ElMessage.success('键删除成功')

    // 如果删除的是当前选中的键，清空详情
    if (selectedKey.value?.key === key.key) {
      selectedKey.value = null
    }

    // 刷新键列表
    await searchKeys()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除键失败: ' + (error.response?.data?.message || error.message))
    }
  }
}

const refreshKeyValue = () => {
  if (selectedKey.value) {
    selectKey(selectedKey.value)
  }
}

const editSelectedKey = () => {
  if (selectedKey.value) {
    editKey(selectedKey.value)
  }
}

const addTestData = async () => {
  if (!activeConnection.value) return

  try {
    // 添加一些示例数据
    const testData = [
      { key: 'user:1001', type: 'string', value: JSON.stringify({id: 1001, name: '张三', email: '<EMAIL>', age: 28}) },
      { key: 'user:1002', type: 'string', value: JSON.stringify({id: 1002, name: '李四', email: '<EMAIL>', age: 32}) },
      { key: 'user:1003', type: 'string', value: JSON.stringify({id: 1003, name: '王五', email: '<EMAIL>', age: 25}) },
      { key: 'counter:visits', type: 'string', value: '12345' },
      { key: 'counter:users', type: 'string', value: '1024' },
      { key: 'config:app_name', type: 'string', value: 'ReqFlow系统' },
      { key: 'config:version', type: 'string', value: '1.0.0' },
      { key: 'session:abc123', type: 'string', value: JSON.stringify({userId: 1001, loginTime: new Date().toISOString()}) },
      { key: 'cache:product:100', type: 'string', value: JSON.stringify({id: 100, name: '笔记本电脑', price: 5999}) },
      { key: 'log:error:2024', type: 'string', value: '系统错误日志记录' }
    ]

    for (const data of testData) {
      await setRedisValue(activeConnection.value.id!, {
        key: data.key,
        type: data.type,
        value: data.value
      })
    }

    ElMessage.success(`已添加 ${testData.length} 条测试数据`)
    await searchKeys() // 刷新键列表
  } catch (error: any) {
    ElMessage.error('添加测试数据失败: ' + (error.response?.data?.message || error.message))
  }
}

const onTabChange = async (tabName: string) => {
  if (tabName === 'keys' && activeConnection.value) {
    // 切换到键浏览器时，刷新键列表
    await searchKeys()
  }
}

const saveKey = async () => {
  if (!activeConnection.value || !keyFormRef.value) return

  try {
    await keyFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    await setRedisValue(activeConnection.value.id!, {
      key: keyForm.key,
      type: keyForm.type,
      value: keyForm.value,
      ttl: keyForm.ttl === -1 ? undefined : keyForm.ttl
    })

    ElMessage.success(editingKey.value ? '键值更新成功' : '键值创建成功')
    showEditKeyDialog.value = false

    // 刷新键列表
    await searchKeys()

    // 如果是编辑现有键，重新选中它
    if (editingKey.value) {
      const updatedKey = keys.value.find(k => k.key === keyForm.key)
      if (updatedKey) {
        await selectKey(updatedKey)
      }
    }
  } catch (error: any) {
    ElMessage.error('保存键值失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const resetKeyForm = () => {
  editingKey.value = null
  Object.assign(keyForm, {
    key: '',
    type: 'string',
    value: '',
    ttl: -1
  })
  if (keyFormRef.value) {
    keyFormRef.value.clearValidate()
  }
}
</script>

<style scoped>
.redis-container {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.connections-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.connection-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.connection-item.active {
  border-color: #409eff;
  background: #e1f3ff;
}

.connection-info {
  flex: 1;
}

.connection-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.connection-details {
  font-size: 12px;
  color: #909399;
}

.connection-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.connection-item:hover .connection-actions {
  opacity: 1;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.redis-client {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.connection-bar {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.connection-bar .connection-info h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.connection-status {
  font-size: 14px;
  color: #67c23a;
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-actions {
  display: flex;
  gap: 8px;
}

.overview-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item .label {
  color: #909399;
  font-weight: 500;
}

.info-item .value {
  color: #303133;
}

.databases-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.database-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.database-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.database-item.active {
  border-color: #409eff;
  background: #e1f3ff;
}

.db-name {
  font-weight: 500;
  color: #303133;
}

.db-keys {
  font-size: 12px;
  color: #909399;
}

.keys-browser {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.search-bar .el-input {
  flex: 1;
}

.keys-content {
  flex: 1;
  display: flex;
  gap: 20px;
}

.keys-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pagination {
  margin-top: 16px;
  text-align: center;
}

.key-detail {
  width: 400px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}

.detail-header h4 {
  margin: 0;
  color: #303133;
  word-break: break-all;
}

.detail-actions {
  display: flex;
  gap: 8px;
}

.detail-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.key-meta {
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.key-value {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.key-value pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
