# Scripts 目录

这个目录包含了用于维护和管理 ReqFlow 系统的各种脚本工具。

## 优先级管理脚本

### 1. fix-priority - 修复优先级数据
修复数据库中无效的优先级数据，将空值或无效值更新为 P7。

```bash
cd scripts/fix-priority
go run main.go
```

### 2. update-priority - 更新优先级格式
将 P7 优先级的工单更新为 'default' 优先级。

```bash
cd scripts/update-priority
go run main.go
```

### 3. verify-priority - 验证优先级数据
验证数据库中所有工单的优先级数据是否正确。

```bash
cd scripts/verify-priority
go run main.go
```

## 部署脚本

### deploy.sh - 部署脚本
用于部署应用的 shell 脚本。

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

## 注意事项

1. 运行这些脚本前，请确保：
   - 数据库连接配置正确
   - 有足够的权限访问数据库
   - 建议先在测试环境运行

2. 每个脚本都是独立的 Go 模块，可以单独运行

3. 运行前建议备份数据库

## 脚本执行顺序建议

如果需要完整的优先级数据修复，建议按以下顺序执行：

1. `verify-priority` - 检查当前状态
2. `fix-priority` - 修复无效数据
3. `update-priority` - 更新格式（如需要）
4. `verify-priority` - 验证修复结果
