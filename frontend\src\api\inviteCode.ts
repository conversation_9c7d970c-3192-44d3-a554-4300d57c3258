import api from './index'

// 邀请码接口类型定义
export interface InviteCode {
  id: number
  code: string
  role_type: string
  team_name: string
  max_uses: number
  used_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CreateInviteCodeRequest {
  code: string
  role_type: string
  team_name: string
  max_uses: number
}

export interface UpdateInviteCodeRequest {
  code: string
  role_type: string
  team_name: string
  max_uses: number
}

// 获取邀请码列表
export const getInviteCodes = () => {
  return api.get('/invite-codes')
}

// 创建邀请码
export const createInviteCode = (data: CreateInviteCodeRequest) => {
  return api.post('/invite-codes', data)
}

// 更新邀请码
export const updateInviteCode = (id: number, data: UpdateInviteCodeRequest) => {
  return api.put(`/invite-codes/${id}`, data)
}

// 删除邀请码
export const deleteInviteCode = (id: number) => {
  return api.delete(`/invite-codes/${id}`)
}

// 切换邀请码状态
export const toggleInviteCodeStatus = (id: number) => {
  return api.put(`/invite-codes/${id}/toggle`)
}
