-- Quick Database Rebuild Commands
-- Execute these commands in MySQL command line or MySQL Workbench

-- Step 1: Drop existing database
DROP DATABASE IF EXISTS req_flow;

-- Step 2: Create new database with proper charset
CREATE DATABASE req_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- Step 3: Use the database
USE req_flow;

-- Step 4: Execute the export_innodb.sql file
-- In MySQL command line: source export_innodb.sql;
-- In MySQL Workbench: File -> Run SQL Script -> select export_innodb.sql

-- Step 5: Verify the tables are using InnoDB
SELECT 
    TABLE_NAME as 'Table Name',
    ENGINE as 'Engine',
    TABLE_ROWS as 'Rows',
    DATA_LENGTH as 'Data Size',
    INDEX_LENGTH as 'Index Size'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'req_flow' 
ORDER BY TABLE_NAME;
