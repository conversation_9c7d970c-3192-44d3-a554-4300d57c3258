package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TeamHandler struct {
	db *gorm.DB
}

func NewTeamHandler(db *gorm.DB) *TeamHandler {
	return &TeamHandler{db: db}
}

// CreateTeamRequest 创建团队请求结构
type CreateTeamRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
}

// UpdateTeamRequest 更新团队请求结构
type UpdateTeamRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
}

// GetTeams 获取团队列表
func (h *TeamHandler) GetTeams(c *gin.Context) {
	var teams []models.Team

	// 预加载创建者信息和用户数量
	if err := h.db.Preload("Creator").Find(&teams).Error; err != nil {
		utils.InternalServerError(c, "获取团队列表失败")
		return
	}

	// 为每个团队添加用户数量
	var result []gin.H
	for _, team := range teams {
		var userCount int64
		h.db.Model(&models.User{}).Where("team_id = ? AND deleted_at IS NULL", team.ID).Count(&userCount)

		result = append(result, gin.H{
			"id":          team.ID,
			"name":        team.Name,
			"description": team.Description,
			"is_active":   team.IsActive,
			"created_by":  team.CreatedBy,
			"creator":     team.Creator,
			"user_count":  userCount,
			"created_at":  team.CreatedAt,
			"updated_at":  team.UpdatedAt,
		})
	}

	utils.Success(c, result)
}

// GetTeam 获取单个团队详情
func (h *TeamHandler) GetTeam(c *gin.Context) {
	teamID := c.Param("id")

	var team models.Team
	if err := h.db.Preload("Creator").Preload("Users").First(&team, teamID).Error; err != nil {
		utils.NotFound(c, "Team not found")
		return
	}

	utils.Success(c, team)
}

// CreateTeam 创建团队
func (h *TeamHandler) CreateTeam(c *gin.Context) {
	var req CreateTeamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	userID, _ := c.Get("user_id")

	team := models.Team{
		Name:        req.Name,
		Description: req.Description,
		IsActive:    true,
		CreatedBy:   userID.(uint),
	}

	if err := h.db.Create(&team).Error; err != nil {
		utils.InternalServerError(c, "创建团队失败")
		return
	}

	// 预加载创建者信息
	h.db.Preload("Creator").First(&team, team.ID)

	utils.SuccessWithMessage(c, "团队创建成功", team)
}

// UpdateTeam 更新团队
func (h *TeamHandler) UpdateTeam(c *gin.Context) {
	teamID := c.Param("id")
	var req UpdateTeamRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	var team models.Team
	if err := h.db.First(&team, teamID).Error; err != nil {
		utils.NotFound(c, "团队不存在")
		return
	}

	// 更新团队信息
	team.Name = req.Name
	team.Description = req.Description

	if err := h.db.Save(&team).Error; err != nil {
		utils.InternalServerError(c, "更新团队失败")
		return
	}

	utils.SuccessWithMessage(c, "团队更新成功", team)
}

// DeleteTeam 删除团队
func (h *TeamHandler) DeleteTeam(c *gin.Context) {
	teamID := c.Param("id")

	var team models.Team
	if err := h.db.First(&team, teamID).Error; err != nil {
		utils.NotFound(c, "团队不存在")
		return
	}

	// 检查团队是否还有用户
	var userCount int64
	h.db.Model(&models.User{}).Where("team_id = ? AND deleted_at IS NULL", team.ID).Count(&userCount)
	if userCount > 0 {
		utils.BadRequest(c, "无法删除还有用户的团队")
		return
	}

	if err := h.db.Delete(&team).Error; err != nil {
		utils.InternalServerError(c, "删除团队失败")
		return
	}

	utils.SuccessWithMessage(c, "团队删除成功", nil)
}

// ToggleTeamStatus 切换团队状态
func (h *TeamHandler) ToggleTeamStatus(c *gin.Context) {
	teamID := c.Param("id")

	var team models.Team
	if err := h.db.First(&team, teamID).Error; err != nil {
		utils.NotFound(c, "团队不存在")
		return
	}

	team.IsActive = !team.IsActive

	if err := h.db.Save(&team).Error; err != nil {
		utils.InternalServerError(c, "更新团队状态失败")
		return
	}

	status := "启用"
	if !team.IsActive {
		status = "禁用"
	}

	utils.SuccessWithMessage(c, "团队"+status+"成功", team)
}
