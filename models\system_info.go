package models

import (
	"runtime"
	"time"
)

// SystemInfo 系统信息结构体
type SystemInfo struct {
	// 服务器基本信息
	ServerInfo ServerInfo `json:"server_info"`
	// 硬件信息
	Hardware HardwareInfo `json:"hardware"`
	// 软件信息
	Software SoftwareInfo `json:"software"`
	// 运行时信息
	Runtime RuntimeInfo `json:"runtime"`
	// 数据库信息
	Database DatabaseInfo `json:"database"`
}

// ServerInfo 服务器基本信息
type ServerInfo struct {
	Hostname    string    `json:"hostname"`
	Platform    string    `json:"platform"`
	OS          string    `json:"os"`
	Arch        string    `json:"arch"`
	Uptime      int64     `json:"uptime"`
	BootTime    time.Time `json:"boot_time"`
	CurrentTime time.Time `json:"current_time"`
}

// HardwareInfo 硬件信息
type HardwareInfo struct {
	CPU    CPUInfo    `json:"cpu"`
	Memory MemoryInfo `json:"memory"`
	Disk   DiskInfo   `json:"disk"`
}

// CPUInfo CPU信息
type CPUInfo struct {
	ModelName string  `json:"model_name"`
	Cores     int     `json:"cores"`
	Usage     float64 `json:"usage"`
	LoadAvg   []float64 `json:"load_avg"`
}

// MemoryInfo 内存信息
type MemoryInfo struct {
	Total       uint64  `json:"total"`
	Available   uint64  `json:"available"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
	Free        uint64  `json:"free"`
	Buffers     uint64  `json:"buffers"`
	Cached      uint64  `json:"cached"`
}

// DiskInfo 磁盘信息
type DiskInfo struct {
	Total       uint64  `json:"total"`
	Free        uint64  `json:"free"`
	Used        uint64  `json:"used"`
	UsedPercent float64 `json:"used_percent"`
	Path        string  `json:"path"`
}

// SoftwareInfo 软件信息
type SoftwareInfo struct {
	GoVersion     string `json:"go_version"`
	GinVersion    string `json:"gin_version"`
	GormVersion   string `json:"gorm_version"`
	AppVersion    string `json:"app_version"`
	BuildTime     string `json:"build_time"`
	GitCommit     string `json:"git_commit"`
}

// RuntimeInfo 运行时信息
type RuntimeInfo struct {
	Goroutines   int           `json:"goroutines"`
	MemStats     MemStats      `json:"mem_stats"`
	GCStats      GCStats       `json:"gc_stats"`
	StartTime    time.Time     `json:"start_time"`
	Uptime       time.Duration `json:"uptime"`
}

// MemStats Go运行时内存统计
type MemStats struct {
	Alloc        uint64 `json:"alloc"`
	TotalAlloc   uint64 `json:"total_alloc"`
	Sys          uint64 `json:"sys"`
	Lookups      uint64 `json:"lookups"`
	Mallocs      uint64 `json:"mallocs"`
	Frees        uint64 `json:"frees"`
	HeapAlloc    uint64 `json:"heap_alloc"`
	HeapSys      uint64 `json:"heap_sys"`
	HeapIdle     uint64 `json:"heap_idle"`
	HeapInuse    uint64 `json:"heap_inuse"`
	HeapReleased uint64 `json:"heap_released"`
	HeapObjects  uint64 `json:"heap_objects"`
	StackInuse   uint64 `json:"stack_inuse"`
	StackSys     uint64 `json:"stack_sys"`
	MSpanInuse   uint64 `json:"mspan_inuse"`
	MSpanSys     uint64 `json:"mspan_sys"`
	MCacheInuse  uint64 `json:"mcache_inuse"`
	MCacheSys    uint64 `json:"mcache_sys"`
	NextGC       uint64 `json:"next_gc"`
	LastGC       uint64 `json:"last_gc"`
}

// GCStats 垃圾回收统计
type GCStats struct {
	NumGC        uint32  `json:"num_gc"`
	PauseTotal   uint64  `json:"pause_total"`
	PauseNs      []uint64 `json:"pause_ns"`
	PauseEnd     []uint64 `json:"pause_end"`
	NumForcedGC  uint32  `json:"num_forced_gc"`
	GCCPUFraction float64 `json:"gc_cpu_fraction"`
}

// DatabaseInfo 数据库信息
type DatabaseInfo struct {
	Type            string `json:"type"`
	Version         string `json:"version"`
	Host            string `json:"host"`
	Port            string `json:"port"`
	Database        string `json:"database"`
	MaxConnections  int    `json:"max_connections"`
	OpenConnections int    `json:"open_connections"`
	InUse           int    `json:"in_use"`
	Idle            int    `json:"idle"`
}

// 全局变量存储应用启动时间
var AppStartTime = time.Now()

// GetRuntimeInfo 获取运行时信息
func GetRuntimeInfo() RuntimeInfo {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return RuntimeInfo{
		Goroutines: runtime.NumGoroutine(),
		MemStats: MemStats{
			Alloc:        m.Alloc,
			TotalAlloc:   m.TotalAlloc,
			Sys:          m.Sys,
			Lookups:      m.Lookups,
			Mallocs:      m.Mallocs,
			Frees:        m.Frees,
			HeapAlloc:    m.HeapAlloc,
			HeapSys:      m.HeapSys,
			HeapIdle:     m.HeapIdle,
			HeapInuse:    m.HeapInuse,
			HeapReleased: m.HeapReleased,
			HeapObjects:  m.HeapObjects,
			StackInuse:   m.StackInuse,
			StackSys:     m.StackSys,
			MSpanInuse:   m.MSpanInuse,
			MSpanSys:     m.MSpanSys,
			MCacheInuse:  m.MCacheInuse,
			MCacheSys:    m.MCacheSys,
			NextGC:       m.NextGC,
			LastGC:       m.LastGC,
		},
		GCStats: GCStats{
			NumGC:         m.NumGC,
			PauseTotal:    m.PauseTotalNs,
			PauseNs:       []uint64{}, // 简化处理
			PauseEnd:     []uint64{}, // 简化处理
			NumForcedGC:   m.NumForcedGC,
			GCCPUFraction: m.GCCPUFraction,
		},
		StartTime: AppStartTime,
		Uptime:    time.Since(AppStartTime),
	}
}
