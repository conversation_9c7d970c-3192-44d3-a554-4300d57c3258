package main

import (
	"ReqFlow/config"
	"ReqFlow/models"
	"log"
)

func main() {
	// 连接数据库
	db := config.InitDatabase()
	if db == nil {
		log.Fatal("数据库连接失败")
	}

	log.Println("开始修复优先级数据...")

	// 查找所有需要更新的工单
	var tickets []models.Ticket
	result := db.Where("priority = '' OR priority IS NULL OR priority NOT IN ('P0', 'P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7')").Find(&tickets)

	if result.Error != nil {
		log.Fatal("查询工单失败:", result.Error)
	}

	if len(tickets) == 0 {
		log.Println("未找到需要更新优先级的工单")
		return
	}

	log.Printf("找到 %d 个需要更新优先级的工单", len(tickets))

	// 更新每个工单的优先级
	successCount := 0
	errorCount := 0

	for _, ticket := range tickets {
		oldPriority := string(ticket.Priority)
		if oldPriority == "" {
			oldPriority = "empty"
		}

		// 更新为P7
		if err := db.Model(&ticket).Update("priority", models.PriorityP7).Error; err != nil {
			log.Printf("❌ 更新工单 %d 失败 (原优先级: %s): %v", ticket.ID, oldPriority, err)
			errorCount++
		} else {
			log.Printf("✅ 更新工单 %d: '%s' -> 'P7'", ticket.ID, oldPriority)
			successCount++
		}
	}

	log.Printf("优先级修复完成:")
	log.Printf("  ✅ 成功更新: %d 个工单", successCount)
	log.Printf("  ❌ 更新失败: %d 个工单", errorCount)
	log.Printf("  📊 总计处理: %d 个工单", len(tickets))

	if errorCount == 0 {
		log.Println("🎉 所有工单已成功更新为P7优先级!")
	}
}
