import api from './index'

// 团队相关接口类型定义
export interface Team {
  id: number
  name: string
  description: string
  is_active: boolean
  created_by: number
  creator?: {
    id: number
    name: string
    username: string
  }
  user_count: number
  users?: User[]
  created_at: string
  updated_at: string
}

export interface User {
  id: number
  name: string
  username: string
  email: string
  role: string
  is_active: boolean
}

export interface CreateTeamRequest {
  name: string
  description: string
}

export interface UpdateTeamRequest {
  name: string
  description: string
}

// 获取团队列表
export const getTeams = () => {
  return api.get('/teams')
}

// 获取单个团队详情
export const getTeam = (id: number) => {
  return api.get(`/teams/${id}`)
}

// 创建团队
export const createTeam = (data: CreateTeamRequest) => {
  return api.post('/teams', data)
}

// 更新团队
export const updateTeam = (id: number, data: UpdateTeamRequest) => {
  return api.put(`/teams/${id}`, data)
}

// 删除团队
export const deleteTeam = (id: number) => {
  return api.delete(`/teams/${id}`)
}

// 切换团队状态
export const toggleTeamStatus = (id: number) => {
  return api.put(`/teams/${id}/toggle`)
}
