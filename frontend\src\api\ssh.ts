import api from './index'

// SSH连接配置接口
export interface SSHConnection {
  id?: number
  name: string
  host: string
  port: number
  username: string
  password?: string
  private_key?: string
  description?: string
  user_id?: number
  is_active?: boolean
  created_at?: string
  updated_at?: string
}

// SSH会话接口
export interface SSHSession {
  id: number
  connection_id: number
  user_id: number
  session_id: string
  status: string
  start_time: string
  end_time?: string
  last_activity: string
  created_at: string
  updated_at: string
}

// WebSocket消息接口
export interface WebSocketMessage {
  type: string
  data: any
  session?: string
}

// 终端大小调整接口
export interface TerminalResize {
  width: number
  height: number
}

// 创建SSH连接
export const createSSHConnection = (data: SSHConnection) => {
  return api.post('/ssh/connections', data)
}

// 获取SSH连接列表
export const getSSHConnections = () => {
  return api.get('/ssh/connections')
}

// 获取单个SSH连接
export const getSSHConnection = (id: number) => {
  return api.get(`/ssh/connections/${id}`)
}

// 更新SSH连接
export const updateSSHConnection = (id: number, data: SSHConnection) => {
  return api.put(`/ssh/connections/${id}`, data)
}

// 删除SSH连接
export const deleteSSHConnection = (id: number) => {
  return api.delete(`/ssh/connections/${id}`)
}

// 测试SSH连接
export const testSSHConnection = (id: number) => {
  return api.post(`/ssh/connections/${id}/test`)
}

// 获取服务器信息
export const getServerInfo = (id: number) => {
  return api.get(`/ssh/connections/${id}/info`)
}

// SSH WebSocket连接类
export class SSHWebSocket {
  private ws: WebSocket | null = null
  private connectionId: number
  private token: string
  private onMessage: (message: WebSocketMessage) => void
  private onError: (error: Event) => void
  private onClose: (event: CloseEvent) => void
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  constructor(
    connectionId: number,
    token: string,
    onMessage: (message: WebSocketMessage) => void,
    onError: (error: Event) => void,
    onClose: (event: CloseEvent) => void
  ) {
    this.connectionId = connectionId
    this.token = token
    this.onMessage = onMessage
    this.onError = onError
    this.onClose = onClose
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 构建WebSocket URL - 直接连接到后端端口
        const protocol = 'ws:'
        const wsUrl = `${protocol}//localhost:8081/ssh/ws?connection_id=${this.connectionId}&token=${encodeURIComponent(this.token)}`

        console.log('Connecting to WebSocket:', wsUrl)
        this.ws = new WebSocket(wsUrl)

        // 设置连接超时
        const connectTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close()
            reject(new Error('WebSocket连接超时'))
          }
        }, 10000) // 10秒超时

        this.ws.onopen = (event) => {
          console.log('SSH WebSocket connected')
          clearTimeout(connectTimeout)
          this.reconnectAttempts = 0
          resolve()
        }

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data)
            this.onMessage(message)
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error)
          }
        }

        this.ws.onerror = (error) => {
          console.error('SSH WebSocket error:', error)
          clearTimeout(connectTimeout)
          this.onError(error)
          reject(error)
        }

        this.ws.onclose = (event) => {
          console.log('SSH WebSocket closed:', event.code, event.reason)
          clearTimeout(connectTimeout)
          this.onClose(event)

          // 只在非正常关闭时尝试重连
          if (this.reconnectAttempts < this.maxReconnectAttempts && !event.wasClean && event.code !== 1000) {
            this.reconnectAttempts++
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
            setTimeout(() => {
              this.connect().catch(console.error)
            }, this.reconnectDelay * this.reconnectAttempts)
          }
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  sendMessage(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  sendInput(data: string): void {
    this.sendMessage({
      type: 'input',
      data: data
    })
  }

  resizeTerminal(width: number, height: number): void {
    this.sendMessage({
      type: 'resize',
      data: { width, height }
    })
  }

  ping(): void {
    this.sendMessage({
      type: 'ping',
      data: Date.now()
    })
  }

  close(): void {
    if (this.ws) {
      this.ws.close(1000, 'Client closing connection')
      this.ws = null
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }
}
