package middleware

import (
	"ReqFlow/models"
	"ReqFlow/utils"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			utils.Unauthorized(c, "缺少授权头")
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			utils.Unauthorized(c, "授权头格式错误")
			c.Abort()
			return
		}

		// 解析token
		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			utils.Unauthorized(c, "令牌无效")
			c.Abort()
			return
		}

		// 验证用户是否存在且活跃
		var user models.User
		if err := db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&user).Error; err != nil {
			utils.Unauthorized(c, "用户不存在或已禁用")
			c.Abort()
			return
		}

		// 将用户信息和数据库实例存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_role", claims.Role)
		c.Set("user", &user)
		c.Set("db", db)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func RequireRole(roles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			utils.Forbidden(c, "缺少角色信息")
			c.Abort()
			return
		}

		role := models.UserRole(userRole.(string))
		for _, requiredRole := range roles {
			if role == requiredRole {
				c.Next()
				return
			}
		}

		utils.Forbidden(c, "权限不足")
		c.Abort()
	}
}

// RequireAdmin 要求管理员权限
func RequireAdmin() gin.HandlerFunc {
	return RequireRole(models.RoleAdmin)
}

// RequireDeveloper 要求开发者权限
func RequireDeveloper() gin.HandlerFunc {
	return RequireRole(models.RoleDeveloper, models.RoleAdmin)
}
