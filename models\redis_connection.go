package models

import (
	"time"

	"gorm.io/gorm"
)

// RedisConnection Redis连接配置
type RedisConnection struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null;size:100" validate:"required"`
	Host        string    `json:"host" gorm:"not null;size:255" validate:"required"`
	Port        int       `json:"port" gorm:"not null;default:6379" validate:"required,min=1,max=65535"`
	Password    string    `json:"password,omitempty" gorm:"size:255"`
	Database    int       `json:"database" gorm:"default:0" validate:"min=0,max=15"`
	Description string    `json:"description" gorm:"size:500"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	User        User      `json:"user,omitempty" gorm:"-"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (RedisConnection) TableName() string {
	return "redis_connections"
}

// BeforeCreate 创建前的钩子
func (rc *RedisConnection) BeforeCreate(tx *gorm.DB) error {
	if rc.Port == 0 {
		rc.Port = 6379
	}
	return nil
}

// RedisKey Redis键值对
type RedisKey struct {
	Key   string      `json:"key"`
	Type  string      `json:"type"`
	TTL   int64       `json:"ttl"`
	Size  int64       `json:"size"`
	Value interface{} `json:"value,omitempty"`
}

// RedisInfo Redis服务器信息
type RedisInfo struct {
	Version          string            `json:"version"`
	Mode             string            `json:"mode"`
	Role             string            `json:"role"`
	ConnectedClients int               `json:"connected_clients"`
	UsedMemory       string            `json:"used_memory"`
	UsedMemoryHuman  string            `json:"used_memory_human"`
	TotalKeys        int64             `json:"total_keys"`
	Uptime           int64             `json:"uptime"`
	Stats            map[string]string `json:"stats"`
}

// RedisDatabase Redis数据库信息
type RedisDatabase struct {
	Index int   `json:"index"`
	Keys  int64 `json:"keys"`
}
