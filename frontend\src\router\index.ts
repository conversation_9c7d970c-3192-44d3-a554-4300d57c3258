import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/xshell-fullscreen',
      name: 'XShellFullscreen',
      component: () => import('@/views/XShellFullscreenView.vue'),
      meta: { requiresAuth: true, requiresDeveloper: true }
    },
    {
      path: '/',
      component: () => import('@/layouts/MainLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: 'dashboard',
          name: 'Dashboard',
          component: () => import('@/views/DashboardView.vue')
        },
        {
          path: 'tickets',
          name: 'Tickets',
          component: () => import('@/views/TicketListView.vue')
        },
        {
          path: 'tickets/:id',
          name: 'TicketDetail',
          component: () => import('@/views/TicketDetailView.vue')
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('@/views/UserManagementView.vue'),
          meta: { requiresAdmin: true }
        },
        {
          path: 'teams',
          name: 'TeamManagement',
          component: () => import('@/views/TeamManagementView.vue'),
          meta: { requiresAdmin: true }
        },
        {
          path: 'admin',
          name: 'Admin',
          component: () => import('@/views/AdminView.vue'),
          meta: { requiresAdmin: true }
        },
        {
          path: 'remote',
          name: 'Remote',
          meta: { requiresDeveloper: true },
          children: [
            {
              path: 'xshell',
              name: 'XShell',
              component: () => import('@/views/XShellView.vue'),
              meta: { requiresDeveloper: true }
            },
            {
              path: 'xftp',
              name: 'XFTP',
              component: () => import('@/views/XFTPView.vue'),
              meta: { requiresDeveloper: true }
            },
            {
              path: 'redis',
              name: 'Redis',
              component: () => import('@/views/RedisView.vue'),
              meta: { requiresDeveloper: true }
            },
            {
              path: 'mysql',
              name: 'MySQL',
              component: () => import('@/views/MySQLView.vue'),
              meta: { requiresDeveloper: true }
            }
          ]
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 检查是否需要登录
  if (to.meta.requiresAuth && !userStore.isLoggedIn()) {
    next('/login')
    return
  }
  
  // 检查是否需要游客状态（如登录页面）
  if (to.meta.requiresGuest && userStore.isLoggedIn()) {
    next('/dashboard')
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !userStore.isAdmin()) {
    next('/dashboard')
    return
  }

  // 检查是否需要开发者权限
  if (to.meta.requiresDeveloper && !userStore.isDeveloper() && !userStore.isAdmin()) {
    next('/dashboard')
    return
  }
  
  next()
})

export default router
