package services

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	"gorm.io/gorm"

	"ReqFlow/models"
)

type SystemService struct {
	db *gorm.DB
}

func NewSystemService(db *gorm.DB) *SystemService {
	return &SystemService{db: db}
}

// GetSystemInfo 获取完整的系统信息
func (s *SystemService) GetSystemInfo() (*models.SystemInfo, error) {
	serverInfo, err := s.getServerInfo()
	if err != nil {
		return nil, fmt.Errorf("获取服务器信息失败: %v", err)
	}

	hardwareInfo, err := s.getHardwareInfo()
	if err != nil {
		return nil, fmt.Errorf("获取硬件信息失败: %v", err)
	}

	softwareInfo := s.getSoftwareInfo()
	runtimeInfo := models.GetRuntimeInfo()
	
	databaseInfo, err := s.getDatabaseInfo()
	if err != nil {
		return nil, fmt.Errorf("获取数据库信息失败: %v", err)
	}

	return &models.SystemInfo{
		ServerInfo: *serverInfo,
		Hardware:   *hardwareInfo,
		Software:   *softwareInfo,
		Runtime:    runtimeInfo,
		Database:   *databaseInfo,
	}, nil
}

// getServerInfo 获取服务器基本信息
func (s *SystemService) getServerInfo() (*models.ServerInfo, error) {
	hostInfo, err := host.Info()
	if err != nil {
		return nil, err
	}

	hostname, _ := os.Hostname()

	// 获取更详细的系统版本信息
	platformVersion := s.getPlatformVersion(hostInfo)

	return &models.ServerInfo{
		Hostname:    hostname,
		Platform:    platformVersion,
		OS:          hostInfo.OS,
		Arch:        hostInfo.KernelArch,
		Uptime:      int64(hostInfo.Uptime),
		BootTime:    time.Unix(int64(hostInfo.BootTime), 0),
		CurrentTime: time.Now(),
	}, nil
}

// getPlatformVersion 获取详细的平台版本信息
func (s *SystemService) getPlatformVersion(hostInfo *host.InfoStat) string {
	// 根据不同操作系统返回更友好的版本名称
	switch hostInfo.OS {
	case "windows":
		return s.getWindowsVersion(hostInfo)
	case "linux":
		return s.getLinuxVersion(hostInfo)
	case "darwin":
		return s.getMacOSVersion(hostInfo)
	default:
		// 如果有版本信息，返回平台 + 版本，否则只返回平台
		if hostInfo.PlatformVersion != "" {
			return fmt.Sprintf("%s %s", hostInfo.Platform, hostInfo.PlatformVersion)
		}
		return hostInfo.Platform
	}
}

// getWindowsVersion 获取Windows版本信息
func (s *SystemService) getWindowsVersion(hostInfo *host.InfoStat) string {
	version := hostInfo.PlatformVersion

	// Windows版本映射 - 使用精确的构建号
	switch {
	// Windows 11 各版本
	case strings.Contains(version, "10.0.22631"):
		return "Windows 11 23H2"
	case strings.Contains(version, "10.0.22621"):
		return "Windows 11 22H2"
	case strings.Contains(version, "10.0.22000"):
		return "Windows 11 21H2"
	case strings.Contains(version, "10.0.22"):
		return "Windows 11"

	// Windows 10 各版本
	case strings.Contains(version, "10.0.19045"):
		return "Windows 10 22H2"
	case strings.Contains(version, "10.0.19044"):
		return "Windows 10 21H2"
	case strings.Contains(version, "10.0.19043"):
		return "Windows 10 21H1"
	case strings.Contains(version, "10.0.19042"):
		return "Windows 10 20H2"
	case strings.Contains(version, "10.0.19041"):
		return "Windows 10 20H1"
	case strings.Contains(version, "10.0.18363"):
		return "Windows 10 1909"
	case strings.Contains(version, "10.0.18362"):
		return "Windows 10 1903"
	case strings.Contains(version, "10.0.17763"):
		return "Windows 10 1809"
	case strings.Contains(version, "10.0.17134"):
		return "Windows 10 1803"
	case strings.Contains(version, "10.0.16299"):
		return "Windows 10 1709"
	case strings.Contains(version, "10.0.15063"):
		return "Windows 10 1703"
	case strings.Contains(version, "10.0.14393"):
		return "Windows 10 1607"
	case strings.Contains(version, "10.0.10586"):
		return "Windows 10 1511"
	case strings.Contains(version, "10.0.10240"):
		return "Windows 10 1507"
	case strings.Contains(version, "10.0"):
		return "Windows 10"

	// 其他Windows版本
	case strings.Contains(version, "6.3"):
		return "Windows 8.1"
	case strings.Contains(version, "6.2"):
		return "Windows 8"
	case strings.Contains(version, "6.1"):
		return "Windows 7"
	case strings.Contains(version, "6.0"):
		return "Windows Vista"
	default:
		return fmt.Sprintf("Windows %s", version)
	}
}

// getLinuxVersion 获取Linux版本信息
func (s *SystemService) getLinuxVersion(hostInfo *host.InfoStat) string {
	platform := hostInfo.Platform
	version := hostInfo.PlatformVersion

	// Linux发行版映射
	switch {
	case strings.Contains(strings.ToLower(platform), "ubuntu"):
		return fmt.Sprintf("Ubuntu %s", version)
	case strings.Contains(strings.ToLower(platform), "centos"):
		return fmt.Sprintf("CentOS %s", version)
	case strings.Contains(strings.ToLower(platform), "rhel") || strings.Contains(strings.ToLower(platform), "red hat"):
		return fmt.Sprintf("Red Hat Enterprise Linux %s", version)
	case strings.Contains(strings.ToLower(platform), "almalinux"):
		return fmt.Sprintf("AlmaLinux %s", version)
	case strings.Contains(strings.ToLower(platform), "rocky"):
		return fmt.Sprintf("Rocky Linux %s", version)
	case strings.Contains(strings.ToLower(platform), "debian"):
		return fmt.Sprintf("Debian %s", version)
	case strings.Contains(strings.ToLower(platform), "fedora"):
		return fmt.Sprintf("Fedora %s", version)
	case strings.Contains(strings.ToLower(platform), "opensuse"):
		return fmt.Sprintf("openSUSE %s", version)
	case strings.Contains(strings.ToLower(platform), "arch"):
		return "Arch Linux"
	default:
		if version != "" {
			return fmt.Sprintf("%s %s", platform, version)
		}
		return platform
	}
}

// getMacOSVersion 获取macOS版本信息
func (s *SystemService) getMacOSVersion(hostInfo *host.InfoStat) string {
	version := hostInfo.PlatformVersion

	// macOS版本映射
	switch {
	case strings.HasPrefix(version, "13."):
		return fmt.Sprintf("macOS Ventura %s", version)
	case strings.HasPrefix(version, "12."):
		return fmt.Sprintf("macOS Monterey %s", version)
	case strings.HasPrefix(version, "11."):
		return fmt.Sprintf("macOS Big Sur %s", version)
	case strings.HasPrefix(version, "10.15"):
		return fmt.Sprintf("macOS Catalina %s", version)
	case strings.HasPrefix(version, "10.14"):
		return fmt.Sprintf("macOS Mojave %s", version)
	case strings.HasPrefix(version, "10.13"):
		return fmt.Sprintf("macOS High Sierra %s", version)
	default:
		return fmt.Sprintf("macOS %s", version)
	}
}

// getHardwareInfo 获取硬件信息
func (s *SystemService) getHardwareInfo() (*models.HardwareInfo, error) {
	// CPU信息
	cpuInfo, err := s.getCPUInfo()
	if err != nil {
		return nil, err
	}

	// 内存信息
	memInfo, err := s.getMemoryInfo()
	if err != nil {
		return nil, err
	}

	// 磁盘信息
	diskInfo, err := s.getDiskInfo()
	if err != nil {
		return nil, err
	}

	return &models.HardwareInfo{
		CPU:    *cpuInfo,
		Memory: *memInfo,
		Disk:   *diskInfo,
	}, nil
}

// getCPUInfo 获取CPU信息
func (s *SystemService) getCPUInfo() (*models.CPUInfo, error) {
	cpuInfos, err := cpu.Info()
	if err != nil {
		return nil, err
	}

	var modelName string
	if len(cpuInfos) > 0 {
		modelName = cpuInfos[0].ModelName
	}

	cores := runtime.NumCPU()

	// 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	var usage float64
	if err == nil && len(cpuPercent) > 0 {
		usage = cpuPercent[0]
	}

	// 获取负载平均值
	loadAvg, err := load.Avg()
	var loadAvgSlice []float64
	if err == nil {
		loadAvgSlice = []float64{loadAvg.Load1, loadAvg.Load5, loadAvg.Load15}
	}

	return &models.CPUInfo{
		ModelName: modelName,
		Cores:     cores,
		Usage:     usage,
		LoadAvg:   loadAvgSlice,
	}, nil
}

// getMemoryInfo 获取内存信息
func (s *SystemService) getMemoryInfo() (*models.MemoryInfo, error) {
	memStat, err := mem.VirtualMemory()
	if err != nil {
		return nil, err
	}

	return &models.MemoryInfo{
		Total:       memStat.Total,
		Available:   memStat.Available,
		Used:        memStat.Used,
		UsedPercent: memStat.UsedPercent,
		Free:        memStat.Free,
		Buffers:     memStat.Buffers,
		Cached:      memStat.Cached,
	}, nil
}

// getDiskInfo 获取磁盘信息
func (s *SystemService) getDiskInfo() (*models.DiskInfo, error) {
	// 获取根目录磁盘使用情况
	diskStat, err := disk.Usage("/")
	if err != nil {
		// Windows系统尝试获取C盘
		diskStat, err = disk.Usage("C:")
		if err != nil {
			return nil, err
		}
	}

	return &models.DiskInfo{
		Total:       diskStat.Total,
		Free:        diskStat.Free,
		Used:        diskStat.Used,
		UsedPercent: diskStat.UsedPercent,
		Path:        diskStat.Path,
	}, nil
}

// getSoftwareInfo 获取软件信息
func (s *SystemService) getSoftwareInfo() *models.SoftwareInfo {
	return &models.SoftwareInfo{
		GoVersion:   runtime.Version(),
		GinVersion:  "v1.9.1", // 可以从go.mod中读取
		GormVersion: "v1.25.0", // 可以从go.mod中读取
		AppVersion:  "v1.0.0",  // 从环境变量或构建信息中获取
		BuildTime:   "2024-01-01", // 构建时注入
		GitCommit:   "unknown",    // 构建时注入
	}
}

// getDatabaseInfo 获取数据库信息
func (s *SystemService) getDatabaseInfo() (*models.DatabaseInfo, error) {
	sqlDB, err := s.db.DB()
	if err != nil {
		return nil, err
	}

	stats := sqlDB.Stats()

	// 获取数据库版本
	var version string
	err = s.db.Raw("SELECT VERSION()").Scan(&version).Error
	if err != nil {
		version = "unknown"
	}

	// 解析数据库连接信息
	// 简化处理，实际项目中可以从配置文件读取
	host := "localhost"
	port := "3306"
	database := "reqflow"

	return &models.DatabaseInfo{
		Type:            "MySQL",
		Version:         version,
		Host:            host,
		Port:            port,
		Database:        database,
		MaxConnections:  stats.MaxOpenConnections,
		OpenConnections: stats.OpenConnections,
		InUse:           stats.InUse,
		Idle:            stats.Idle,
	}, nil
}

// GetSystemStats 获取系统实时统计信息（用于监控图表）
func (s *SystemService) GetSystemStats() (map[string]interface{}, error) {
	// CPU使用率
	cpuPercent, _ := cpu.Percent(time.Second, false)
	var cpuUsage float64
	if len(cpuPercent) > 0 {
		cpuUsage = cpuPercent[0]
	}

	// 内存使用率
	memStat, _ := mem.VirtualMemory()
	
	// 磁盘使用率
	diskStat, _ := disk.Usage("/")
	if diskStat == nil {
		diskStat, _ = disk.Usage("C:")
	}

	// Go运行时信息
	runtimeInfo := models.GetRuntimeInfo()

	return map[string]interface{}{
		"timestamp":     time.Now().Unix(),
		"cpu_usage":     cpuUsage,
		"memory_usage":  memStat.UsedPercent,
		"disk_usage":    diskStat.UsedPercent,
		"goroutines":    runtimeInfo.Goroutines,
		"heap_alloc":    runtimeInfo.MemStats.HeapAlloc,
		"heap_sys":      runtimeInfo.MemStats.HeapSys,
		"gc_num":        runtimeInfo.MemStats.NextGC,
	}, nil
}
