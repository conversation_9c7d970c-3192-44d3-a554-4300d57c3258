# SSH Connection Test Script
$baseUrl = "http://localhost:8081/api"

# Login to get token
Write-Host "Logging in..." -ForegroundColor Yellow
$loginData = @{
    username = "admin"
    password = "admin123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -Body $loginData -ContentType "application/json"
    if ($loginResponse.code -eq 200) {
        $token = $loginResponse.data.token
        Write-Host "Login successful, token obtained" -ForegroundColor Green
    } else {
        Write-Host "Login failed: $($loginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Login request failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Create SSH connection configuration
Write-Host "Creating SSH connection configuration..." -ForegroundColor Yellow
$sshConnectionData = @{
    name = "Test Server"
    host = "**************"
    port = 22
    username = "root"
    password = "Asdty1234"
    description = "Test SSH Connection"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

try {
    $createResponse = Invoke-RestMethod -Uri "$baseUrl/ssh/connections" -Method POST -Body $sshConnectionData -Headers $headers
    if ($createResponse.code -eq 200) {
        $connectionId = $createResponse.data.id
        Write-Host "SSH connection configuration created successfully, ID: $connectionId" -ForegroundColor Green
    } else {
        Write-Host "Failed to create SSH connection configuration: $($createResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "SSH connection configuration request failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test SSH connection
Write-Host "Testing SSH connection..." -ForegroundColor Yellow
try {
    $testResponse = Invoke-RestMethod -Uri "$baseUrl/ssh/connections/$connectionId/test" -Method POST -Headers $headers
    if ($testResponse.code -eq 200) {
        Write-Host "SSH connection test successful!" -ForegroundColor Green
        Write-Host "Message: $($testResponse.data.message)" -ForegroundColor Green
    } else {
        Write-Host "SSH connection test failed: $($testResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "SSH connection test request failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Error details: $responseBody" -ForegroundColor Red
    }
}

# Get connection list
Write-Host "Getting SSH connection list..." -ForegroundColor Yellow
try {
    $listResponse = Invoke-RestMethod -Uri "$baseUrl/ssh/connections" -Method GET -Headers $headers
    if ($listResponse.code -eq 200) {
        Write-Host "SSH connection list retrieved successfully:" -ForegroundColor Green
        $listResponse.data | ForEach-Object {
            Write-Host "  - ID: $($_.id), Name: $($_.name), Host: $($_.host):$($_.port)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "Failed to get SSH connection list: $($listResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "SSH connection list request failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "Test completed" -ForegroundColor Yellow
