package main

import (
	"ReqFlow/config"
	"ReqFlow/controllers"
	"ReqFlow/handlers"
	"ReqFlow/middleware"
	"ReqFlow/migrations"
	"ReqFlow/services"
	"log"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func main() {
	// 初始化数据库（根据环境变量选择）
	var db *gorm.DB
	var err error

	env := os.Getenv("GO_ENV")
	if env == "production" {
		log.Println("启动正式环境...")
		db, err = config.InitProductionDB()
		if err != nil {
			log.Fatal("正式环境数据库初始化失败:", err)
		}
	} else {
		log.Println("启动测试环境...")
		db = config.InitDatabase()
	}

	// 自动迁移数据库表
	if err := migrations.AutoMigrate(db); err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	// 初始化种子数据
	if err := migrations.SeedData(db); err != nil {
		log.Fatal("种子数据初始化失败:", err)
	}

	// 修复用户团队分配
	if err := migrations.FixUserTeamAssignments(db); err != nil {
		log.Printf("警告: 修复用户团队分配失败: %v", err)
	}

	// 初始化Gin路由
	r := gin.Default()

	// 配置CORS - 支持更多浏览器和端口
	config := cors.DefaultConfig()

	// 根据环境配置不同的CORS策略
	// env 变量已在上面定义过，这里直接使用
	if env == "production" {
		// 生产环境：指定具体的前端域名
		config.AllowOrigins = []string{
			"http://flow.anpu.shop",
			"https://flow.anpu.shop",
			// 如果有其他前端域名，请添加到这里
		}
	} else {
		// 开发环境：指定具体域名
		config.AllowOrigins = []string{
			// 本地开发环境
			"http://localhost:3000",
			"http://localhost:5173",
			"http://localhost:5174",
			"http://localhost:5175",
			"http://127.0.0.1:5173",
			"http://127.0.0.1:5174",
			"http://127.0.0.1:5175",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8080",
		}
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"}
	config.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-Requested-With",
		"Access-Control-Allow-Origin",
		"Access-Control-Allow-Headers",
		"Access-Control-Allow-Methods",
	}
	config.AllowCredentials = true
	config.ExposeHeaders = []string{"Content-Length", "Access-Control-Allow-Origin"}
	r.Use(cors.New(config))

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(db)
	ticketHandler := handlers.NewTicketHandler(db)
	commentHandler := handlers.NewCommentHandler(db)
	userHandler := handlers.NewUserHandler(db)
	inviteCodeHandler := handlers.NewInviteCodeHandler(db)
	teamHandler := handlers.NewTeamHandler(db)
	sshHandler := handlers.NewSSHHandler(db)
	sftpHandler := handlers.NewSFTPHandler(db)

	// 初始化服务和控制器
	systemService := services.NewSystemService(db)
	systemController := controllers.NewSystemController(systemService)

	// 公开路由（不使用/api前缀）
	{
		r.POST("/register", authHandler.Register)
		r.POST("/login", authHandler.Login)
	}

	// 需要认证的路由
	auth := r.Group("/")
	auth.Use(middleware.AuthMiddleware(db))
	{
		// 用户相关
		auth.GET("/profile", authHandler.GetProfile)
		auth.GET("/developers", userHandler.GetDevelopers)

		// 工单相关
		auth.POST("/tickets", ticketHandler.CreateTicket)
		auth.GET("/tickets", ticketHandler.GetTickets)
		auth.GET("/tickets/:id", ticketHandler.GetTicket)
		auth.PUT("/tickets/:id/status", ticketHandler.UpdateTicketStatus)
		auth.PUT("/tickets/:id/priority", ticketHandler.UpdateTicketPriority)
		auth.POST("/tickets/:id/transfer", ticketHandler.TransferTicket)

		// 评论相关
		auth.POST("/comments", commentHandler.CreateComment)
		auth.GET("/comments/ticket/:ticket_id", commentHandler.GetComments)
	}

	// 用户管理（管理员专用）
	userMgmt := auth.Group("/users")
	userMgmt.Use(middleware.RequireAdmin())
	{
		userMgmt.GET("", userHandler.GetUsers)
		userMgmt.POST("", userHandler.CreateUser)
		userMgmt.PUT("/:id", userHandler.UpdateUser)
		userMgmt.DELETE("/:id", userHandler.DeleteUser)
		userMgmt.PUT("/:id/toggle", userHandler.ToggleUserStatus)
	}

	// 团队管理（管理员专用）
	teamMgmt := auth.Group("/teams")
	teamMgmt.Use(middleware.RequireAdmin())
	{
		teamMgmt.GET("", teamHandler.GetTeams)
		teamMgmt.POST("", teamHandler.CreateTeam)
		teamMgmt.GET("/:id", teamHandler.GetTeam)
		teamMgmt.PUT("/:id", teamHandler.UpdateTeam)
		teamMgmt.DELETE("/:id", teamHandler.DeleteTeam)
		teamMgmt.PUT("/:id/toggle", teamHandler.ToggleTeamStatus)
	}

	// 邀请码管理路由
	inviteCodeMgmt := auth.Group("/invite-codes")
	inviteCodeMgmt.Use(middleware.RequireAdmin())
	{
		inviteCodeMgmt.GET("", inviteCodeHandler.GetInviteCodes)
		inviteCodeMgmt.POST("", inviteCodeHandler.CreateInviteCode)
		inviteCodeMgmt.PUT("/:id", inviteCodeHandler.UpdateInviteCode)
		inviteCodeMgmt.DELETE("/:id", inviteCodeHandler.DeleteInviteCode)
		inviteCodeMgmt.PUT("/:id/toggle", inviteCodeHandler.ToggleInviteCodeStatus)
	}

	// 管理员专用路由
	admin := auth.Group("/admin")
	admin.Use(middleware.RequireAdmin())
	{
		// 邀请码管理
		admin.POST("/invite-codes", inviteCodeHandler.CreateInviteCode)
		admin.GET("/invite-codes", inviteCodeHandler.GetInviteCodes)
		admin.PUT("/invite-codes/:id/toggle", inviteCodeHandler.ToggleInviteCodeStatus)
	}

	// SSH管理路由（管理员和开发者可用）
	sshMgmt := auth.Group("/ssh")
	sshMgmt.Use(middleware.RequireDeveloper())
	{
		sshMgmt.POST("/connections", sshHandler.CreateConnection)
		sshMgmt.GET("/connections", sshHandler.GetConnections)
		sshMgmt.GET("/connections/:id", sshHandler.GetConnection)
		sshMgmt.PUT("/connections/:id", sshHandler.UpdateConnection)
		sshMgmt.DELETE("/connections/:id", sshHandler.DeleteConnection)
		sshMgmt.POST("/connections/:id/test", sshHandler.TestConnection)
		sshMgmt.GET("/connections/:id/info", sshHandler.GetServerInfo)
	}

	// SSH WebSocket路由（单独处理认证）
	r.GET("/ssh/ws", sshHandler.ConnectWebSocket)

	// Redis管理路由（管理员和开发者可用）
	redisMgmt := auth.Group("/redis")
	redisMgmt.Use(middleware.RequireDeveloper())
	{
		redisMgmt.POST("/connections", handlers.CreateRedisConnection)
		redisMgmt.GET("/connections", handlers.GetRedisConnections)
		redisMgmt.PUT("/connections/:id", handlers.UpdateRedisConnection)
		redisMgmt.DELETE("/connections/:id", handlers.DeleteRedisConnection)
		redisMgmt.POST("/connections/test", handlers.TestRedisConnection)
		redisMgmt.GET("/connections/:id/info", handlers.GetRedisInfo)
		redisMgmt.GET("/connections/:id/databases", handlers.GetRedisDatabases)
		redisMgmt.GET("/connections/:id/keys", handlers.GetRedisKeys)
		redisMgmt.GET("/connections/:id/keys/:key", handlers.GetRedisValue)
		redisMgmt.POST("/connections/:id/keys", handlers.SetRedisValue)
		redisMgmt.DELETE("/connections/:id/keys/:key", handlers.DeleteRedisKey)
		redisMgmt.POST("/connections/:id/execute", handlers.ExecuteRedisCommand)
		redisMgmt.PUT("/connections/:id/database", handlers.SwitchRedisDatabase)
	}

	// MySQL管理路由（管理员和开发者可用）
	mysqlMgmt := auth.Group("/mysql")
	mysqlMgmt.Use(middleware.RequireDeveloper())
	{
		mysqlMgmt.POST("/connections", handlers.CreateMySQLConnection)
		mysqlMgmt.GET("/connections", handlers.GetMySQLConnections)
		mysqlMgmt.PUT("/connections/:id", handlers.UpdateMySQLConnection)
		mysqlMgmt.DELETE("/connections/:id", handlers.DeleteMySQLConnection)
		mysqlMgmt.POST("/connections/test", handlers.TestMySQLConnection)
		mysqlMgmt.GET("/connections/:id/databases", handlers.GetMySQLDatabases)
		mysqlMgmt.GET("/connections/:id/tables", handlers.GetMySQLTables)
		mysqlMgmt.GET("/connections/:id/columns", handlers.GetMySQLColumns)
		mysqlMgmt.POST("/connections/:id/query", handlers.ExecuteMySQLQuery)
	}

	// SFTP管理路由（管理员和开发者可用）
	sftpMgmt := auth.Group("/sftp")
	sftpMgmt.Use(middleware.RequireDeveloper())
	{
		sftpMgmt.GET("/files", sftpHandler.GetFileList)
		sftpMgmt.GET("/download", sftpHandler.DownloadFile)
		sftpMgmt.POST("/upload", sftpHandler.UploadFile)
		sftpMgmt.POST("/folder", sftpHandler.CreateFolder)
		sftpMgmt.DELETE("/delete", sftpHandler.DeleteFile)
	}

	// 系统管理路由（管理员专用）
	systemMgmt := auth.Group("/system")
	systemMgmt.Use(middleware.RequireAdmin())
	{
		systemMgmt.GET("/info", systemController.GetSystemInfo)
		systemMgmt.GET("/stats", systemController.GetSystemStats)
		systemMgmt.GET("/stats/history", systemController.GetSystemStatsHistory)
		systemMgmt.GET("/health", systemController.GetSystemHealth)
		systemMgmt.GET("/logs", systemController.GetSystemLogs)
		systemMgmt.POST("/restart", systemController.RestartService)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "ReqFlow API 运行正常",
		})
	})

	// 启动服务器
	log.Println("ReqFlow 服务器启动中，端口：8081")
	log.Println("API 健康检查地址：http://localhost:8081/health")
	if err := r.Run(":8081"); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}
