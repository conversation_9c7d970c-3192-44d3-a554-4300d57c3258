<template>
  <div class="ticket-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/tickets' }">工单列表</el-breadcrumb-item>
          <el-breadcrumb-item>工单详情</el-breadcrumb-item>
        </el-breadcrumb>
        <h2 class="page-title">{{ ticket?.title }}</h2>
      </div>
      <div class="header-actions">
        <!-- 状态更新下拉 -->
        <el-dropdown v-if="canUpdateStatus" @command="handleStatusChange" :disabled="!hasAvailableStatuses">
          <el-button-group>
            <el-button type="primary" :icon="Edit" :disabled="!hasAvailableStatuses">
              状态
            </el-button>
            <el-dropdown trigger="click" @command="handleStatusChange">
              <el-button type="primary" :disabled="!hasAvailableStatuses">
                {{ ticket ? getStatusName(ticket.status) : '加载中' }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="status in getAvailableStatuses()"
                    :key="status.value"
                    :command="status.value"
                  >
                    {{ status.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-button-group>
        </el-dropdown>

        <!-- 分配人员下拉 -->
        <div v-if="canAssign && (userStore.user?.role === 'admin' || userStore.user?.role === 'developer')">
          <el-button-group>
            <el-button type="warning" :icon="User">
              开发者
            </el-button>
            <el-dropdown trigger="click" @command="handleAssigneeChange">
              <el-button type="warning">
                {{ getCurrentAssignee() }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-for="developer in developers"
                    :key="developer.id"
                    :command="developer.id"
                  >
                    {{ developer.name }} ({{ developer.username }})
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-button-group>
        </div>

        <!-- 优先级设置下拉 -->
        <div v-if="canUpdatePriority && (userStore.user?.role === 'admin' || userStore.user?.role === 'developer')">
          <el-button-group>
            <el-button :type="getPriorityButtonType()" :icon="Flag">
              优先级
            </el-button>
            <el-dropdown trigger="click" @command="handlePriorityChange">
              <el-button :type="getPriorityButtonType()">
                {{ ticket ? getPriorityName(ticket.priority) : '加载中' }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="P0">P0 (最高)</el-dropdown-item>
                  <el-dropdown-item command="P1">P1</el-dropdown-item>
                  <el-dropdown-item command="P2">P2</el-dropdown-item>
                  <el-dropdown-item command="P3">P3</el-dropdown-item>
                  <el-dropdown-item command="P4">P4</el-dropdown-item>
                  <el-dropdown-item command="P5">P5</el-dropdown-item>
                  <el-dropdown-item command="P6">P6</el-dropdown-item>
                  <el-dropdown-item command="default">默认</el-dropdown-item>
                  <el-dropdown-item command="P7">P7 (最低)</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-button-group>
        </div>

        <el-button
          v-if="canTransfer"
          type="warning"
          @click="showTransferDialog = true"
          :icon="Share"
        >
          转交工单
        </el-button>
      </div>
    </div>

    <!-- Loading状态 -->
    <el-card v-if="loading" class="ticket-info" v-loading="true" element-loading-text="加载工单详情中...">
      <el-skeleton :rows="5" animated />
    </el-card>

    <!-- 工单信息卡片 -->
    <el-card v-else-if="ticket" class="ticket-info">
      <template #header>
        <div class="card-header">
          <div class="ticket-title">
            <h2>{{ ticket.title }}</h2>
            <div class="ticket-meta">
              <el-tag :type="getTypeColor(ticket.type)">
                {{ getTypeName(ticket.type) }}
              </el-tag>
              <el-tag v-if="userStore.user?.role === 'admin' || userStore.user?.role === 'developer'" :type="getPriorityColor(ticket.priority)">
                {{ getPriorityName(ticket.priority) }}
              </el-tag>
              <el-tag :type="getStatusColor(ticket.status)">
                {{ getStatusName(ticket.status) }}
              </el-tag>
            </div>
          </div>
          <div class="ticket-id">
            #{{ ticket.id }}
          </div>
        </div>
      </template>

      <el-row :gutter="20">
        <el-col :span="16">
          <div class="ticket-content">
            <h3>描述</h3>
            <div class="description" v-html="ticket.description || '暂无描述'"></div>

            <!-- 拒绝原因 -->
            <div v-if="ticket.reject_reason" class="reject-reason">
              <h3>拒绝原因</h3>
              <el-alert type="error" :closable="false">
                {{ ticket.reject_reason }}
              </el-alert>
            </div>

            <!-- 反馈信息 -->
            <div v-if="ticket.feedback" class="feedback">
              <h3>反馈信息</h3>
              <el-alert type="warning" :closable="false">
                {{ ticket.feedback }}
              </el-alert>
            </div>
          </div>
        </el-col>

        <el-col :span="8">
          <div class="ticket-sidebar">
            <div class="info-section">
              <h3>基本信息</h3>
              <div class="info-item">
                <span class="label">创建者：</span>
                <span class="value">{{ ticket.creator?.username }}</span>
              </div>
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(ticket.created_at) }}</span>
              </div>
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span class="value">{{ formatDate(ticket.updated_at) }}</span>
              </div>
            </div>

            <!-- 分配信息 -->
            <div v-if="assignees.length > 0 && (userStore.user?.role === 'admin' || userStore.user?.role === 'developer')" class="info-section">
              <h3>分配给</h3>
              <div class="assignees">
                <el-tag
                  v-for="assignee in assignees"
                  :key="assignee.id"
                  class="assignee-tag"
                >
                  {{ assignee.name || assignee.username }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 流转历史 -->
    <el-card v-if="ticket?.transfers && ticket.transfers.length > 0" class="transfer-history">
      <template #header>
        <h3>流转历史</h3>
      </template>
      <el-timeline>
        <el-timeline-item
          v-for="transfer in ticket.transfers"
          :key="transfer.id"
          :timestamp="formatDate(transfer.created_at)"
        >
          <div class="transfer-item">
            <strong>{{ transfer.from_user?.username }}</strong>
            转交给
            <strong>{{ transfer.to_user?.username }}</strong>
            <div v-if="transfer.reason" class="transfer-reason">
              原因：{{ transfer.reason }}
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-card>

    <!-- 评论区域 -->
    <el-card v-if="!loading" class="comments-section">
      <template #header>
        <div class="comments-header">
          <h3>评论 ({{ comments.length }})</h3>
          <el-button
            v-if="canComment"
            type="primary"
            @click="showCommentDialog = true"
            :icon="ChatDotRound"
          >
            添加评论
          </el-button>
        </div>
      </template>

      <div v-if="comments.length === 0" class="no-comments">
        <el-empty description="暂无评论" />
      </div>

      <div v-else class="comments-list">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="comment-item"
        >
          <div class="comment-header">
            <el-avatar :size="32">
              {{ comment.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <div class="comment-meta">
              <strong>{{ comment.user?.username }}</strong>
              <span class="comment-time">{{ formatDate(comment.created_at) }}</span>
            </div>
          </div>
          <div class="comment-content" v-html="comment.content"></div>
        </div>
      </div>
    </el-card>

    <!-- 更新状态对话框 -->
    <el-dialog
      v-model="showStatusDialog"
      title="更新工单状态"
      width="500px"
    >
      <el-form :model="statusForm" label-width="100px">
        <el-form-item label="新状态">
          <el-select v-model="statusForm.status" placeholder="请选择状态">
            <el-option
              v-for="status in availableStatuses"
              :key="status.value"
              :label="status.label"
              :value="status.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="statusForm.status === 0"
          label="拒绝原因"
        >
          <el-input
            v-model="statusForm.reject_reason"
            type="textarea"
            :rows="3"
            placeholder="请输入拒绝原因"
          />
        </el-form-item>

        <el-form-item
          v-if="statusForm.status === 6"
          label="反馈信息"
        >
          <el-input
            v-model="statusForm.feedback"
            type="textarea"
            :rows="3"
            placeholder="请输入反馈信息"
          />
        </el-form-item>

        <!-- 管理员可以设置优先级 -->
        <el-form-item
          v-if="userStore.user?.role === 'admin'"
          label="优先级"
        >
          <el-select v-model="statusForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="needsAssignee"
          label="分配给"
          required
        >
          <el-select
            v-model="statusForm.assignee_ids"
            multiple
            placeholder="请选择开发者"
            style="width: 100%"
          >
            <el-option
              v-for="dev in developers"
              :key="dev.id"
              :label="`${dev.name} (${dev.username})`"
              :value="dev.id"
            />
          </el-select>
          <div class="form-tip">
            <el-text type="info" size="small">
              💡 更新到此状态需要分配开发者
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showStatusDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="updateStatus"
          :loading="updating"
        >
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 优先级设置对话框 -->
    <el-dialog
      v-model="showPriorityDialog"
      title="设置工单优先级"
      width="400px"
    >
      <el-form :model="priorityForm" label-width="80px">
        <el-form-item label="优先级">
          <el-select v-model="priorityForm.priority" placeholder="请选择优先级" style="width: 100%">
            <el-option label="P0 (最高)" value="P0" />
            <el-option label="P1" value="P1" />
            <el-option label="P2" value="P2" />
            <el-option label="P3" value="P3" />
            <el-option label="P4" value="P4" />
            <el-option label="P5" value="P5" />
            <el-option label="P6" value="P6" />
            <el-option label="P7 (最低)" value="P7" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showPriorityDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleUpdatePriority"
          :loading="priorityUpdating"
        >
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 转交工单对话框 -->
    <el-dialog
      v-model="showTransferDialog"
      title="转交工单"
      width="500px"
    >
      <el-form :model="transferForm" label-width="100px">
        <el-form-item label="转交给" required>
          <el-select
            v-model="transferForm.to_user_id"
            placeholder="请选择开发者"
          >
            <el-option
              v-for="dev in developers"
              :key="dev.id"
              :label="`${dev.name} (${dev.username})`"
              :value="dev.id"
              :disabled="dev.id === userStore.user?.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="转交原因">
          <el-input
            v-model="transferForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入转交原因（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showTransferDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="transferTicket"
          :loading="transferring"
        >
          确定转交
        </el-button>
      </template>
    </el-dialog>

    <!-- 添加评论对话框 -->
    <el-dialog
      v-model="showCommentDialog"
      title="添加评论"
      width="600px"
    >
      <el-form :model="commentForm" label-width="80px">
        <el-form-item label="评论内容" required>
          <RichTextEditor
            v-model="commentForm.content"
            placeholder="请输入评论内容，支持Markdown格式"
            :rows="5"
            :maxlength="1000"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCommentDialog = false">取消</el-button>
        <el-button
          type="primary"
          @click="addComment"
          :loading="commenting"
        >
          发表评论
        </el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="showImagePreview"
      title="图片预览"
      width="80%"
      :before-close="closeImagePreview"
    >
      <div class="image-preview-container">
        <img
          :src="previewImageUrl"
          alt="预览图片"
          class="preview-image"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Edit,
  Share,
  ChatDotRound,
  Flag,
  ArrowDown,
  User
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import {
  getTicket,
  updateTicketStatus,
  updateTicketPriority,
  transferTicket as transferTicketApi,
  createComment,
  getComments,
  type Ticket,
  type Comment,
  type UpdateStatusRequest,
  type TransferTicketRequest,
  type CreateCommentRequest
} from '@/api/ticket'
import { getDevelopers } from '@/api/auth'
import RichTextEditor from '@/components/RichTextEditor.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 响应式数据
const ticket = ref<Ticket | null>(null)
const comments = ref<Comment[]>([])
const developers = ref<Array<{id: number, name: string, username: string}>>([])
const loading = ref(false)
const updating = ref(false)
const transferring = ref(false)
const commenting = ref(false)

// 对话框显示状态
const showStatusDialog = ref(false)
const showTransferDialog = ref(false)
const showCommentDialog = ref(false)
const showImagePreview = ref(false)
const showPriorityDialog = ref(false)
const previewImageUrl = ref('')
const priorityUpdating = ref(false)

// 表单数据
const statusForm = ref<UpdateStatusRequest>({
  status: 1,
  priority: '',
  reject_reason: '',
  feedback: '',
  assignee_ids: []
})

const transferForm = ref<TransferTicketRequest>({
  to_user_id: 0,
  reason: ''
})

const commentForm = ref<CreateCommentRequest>({
  ticket_id: 0,
  content: '',
  images: []
})

const priorityForm = ref({
  priority: 'default'
})

// 计算属性
const ticketId = computed(() => parseInt(route.params.id as string))

const assignees = computed(() => {
  if (!ticket.value || !ticket.value.assignee_ids || !Array.isArray(ticket.value.assignee_ids)) return []

  // 从developers列表中获取真实的开发者信息
  return ticket.value.assignee_ids.map(id => {
    const developer = developers.value.find(dev => dev.id === id)
    if (developer) {
      return {
        id: developer.id,
        username: developer.username,
        name: developer.name || developer.username
      }
    }
    // 如果找不到开发者信息，返回默认值
    return {
      id,
      username: `User${id}`,
      name: `User${id}`
    }
  }).filter(assignee => assignee.id) // 过滤掉无效的ID
})

const canUpdateStatus = computed(() => {
  if (!ticket.value) return false
  const user = userStore.user
  if (!user) return false

  // 管理员可以更新任何状态
  if (user.role === 'admin') return true

  // 开发者可以更新被分配给自己的工单
  if (user.role === 'developer' && ticket.value.assignee_ids?.includes(user.id)) {
    return [2, 3, 4].includes(ticket.value.status) // 已分配、开发中、已流转
  }

  return false
})

const canTransfer = computed(() => {
  if (!ticket.value) return false
  const user = userStore.user
  if (!user) return false

  return user.role === 'developer' &&
         ticket.value.assignee_ids?.includes(user.id) &&
         [2, 3].includes(ticket.value.status) // 已分配、开发中
})

const canUpdatePriority = computed(() => {
  if (!ticket.value) return false
  const user = userStore.user
  if (!user) return false

  // 只有管理员可以更新优先级
  return user.role === 'admin'
})

const canAssign = computed(() => {
  if (!ticket.value) return false
  const user = userStore.user
  if (!user) return false

  // 只有管理员可以分配人员，且工单状态为待处理或已分配
  return user.role === 'admin' && [1, 2].includes(ticket.value.status)
})

// 检查是否有可用的状态选项
const hasAvailableStatuses = computed(() => {
  return getAvailableStatuses().length > 0
})

// 判断是否需要分配开发者
const needsAssignee = computed(() => {
  // 需要分配开发者的状态：已分配(2)、开发中(3)、已提交(5)
  const statusesNeedingAssignee = [2, 3, 5]
  const needsAssigneeForStatus = statusesNeedingAssignee.includes(statusForm.value.status)

  // 开发者在"已分配"状态时不能再分配开发者，只有管理员可以
  if (statusForm.value.status === 2 && userStore.user?.role === 'developer') {
    return false
  }

  // 开发者更新状态到"开发中"时，不需要分配开发者（自动分配给自己）
  if (statusForm.value.status === 3 && userStore.user?.role === 'developer') {
    return false
  }

  return needsAssigneeForStatus
})

const canComment = computed(() => {
  if (!ticket.value) return false
  const user = userStore.user
  if (!user) return false

  // 管理员可以评论任何工单
  if (user.role === 'admin') return true

  // 创建者可以评论自己的工单
  if (ticket.value.creator_id === user.id) return true

  // 被分配的开发者可以评论
  if (user.role === 'developer' && ticket.value.assignee_ids?.includes(user.id)) {
    return true
  }

  return false
})

const availableStatuses = computed(() => {
  if (!ticket.value) return []
  const user = userStore.user
  if (!user) return []

  const currentStatus = ticket.value.status
  const statuses = []

  // 首先添加当前状态（用于显示）
  const currentStatusName = getStatusName(currentStatus)
  statuses.push({ value: currentStatus, label: `${currentStatusName} (当前)`, disabled: true })

  if (user.role === 'admin') {
    switch (currentStatus) {
      case 1: // 审核中
        statuses.push(
          { value: 2, label: '已分配' },
          { value: 0, label: '已拒绝' }
        )
        break
      case 5: // 已提交
        statuses.push(
          { value: 7, label: '已完成' },
          { value: 6, label: '已退回' }
        )
        break
    }
  } else if (user.role === 'developer') {
    switch (currentStatus) {
      case 2: // 已分配
        statuses.push({ value: 3, label: '开发中' })
        break
      case 3: // 开发中
        statuses.push({ value: 5, label: '已提交' })
        break
      case 4: // 已流转
        statuses.push({ value: 3, label: '开发中' })
        break
      case 6: // 已退回
        statuses.push({ value: 3, label: '开发中' })
        break
    }
  }

  return statuses
})

// 方法
// 获取优先级按钮类型
const getPriorityButtonType = () => {
  if (!ticket.value) return 'info'
  return getPriorityColor(ticket.value.priority)
}

// 获取当前分配的开发者名称
const getCurrentAssignee = () => {
  if (!ticket.value || !ticket.value.assignee_ids || ticket.value.assignee_ids.length === 0) {
    return '无'
  }

  // 如果有多个分配者，显示第一个
  const assigneeId = ticket.value.assignee_ids[0]
  const assignee = developers.value.find(dev => dev.id === assigneeId)

  return assignee ? assignee.name : '无'
}

// 获取可用状态选项
const getAvailableStatuses = () => {
  if (!ticket.value) return []
  const user = userStore.user
  if (!user) return []

  const currentStatus = ticket.value.status
  const statuses = []

  if (user.role === 'admin') {
    switch (currentStatus) {
      case 1: // 待处理
        statuses.push(
          { value: 2, label: '已分配' },
          { value: 0, label: '已拒绝' }
        )
        break
      case 2: // 已分配
        statuses.push(
          { value: 1, label: '待处理' },
          { value: 0, label: '已拒绝' }
        )
        break
      case 5: // 已提交
        statuses.push(
          { value: 7, label: '已完成' },
          { value: 6, label: '已退回' }
        )
        break
    }
  } else if (user.role === 'developer') {
    switch (currentStatus) {
      case 2: // 已分配
        if (ticket.value.assignee_ids?.includes(user.id)) {
          statuses.push({ value: 3, label: '开发中' })
        }
        break
      case 3: // 开发中
        if (ticket.value.assignee_ids?.includes(user.id)) {
          statuses.push({ value: 5, label: '已提交' })
        }
        break
      case 6: // 已退回
        if (ticket.value.assignee_ids?.includes(user.id)) {
          statuses.push({ value: 3, label: '开发中' })
        }
        break
    }
  }

  return statuses
}

// 处理状态下拉选择
const handleStatusChange = async (status: number) => {
  if (!ticket.value) return

  try {
    const statusData = {
      status: status,
      assignee_ids: ticket.value.assignee_ids || []
    }

    const response = await updateTicketStatus(ticket.value.id, statusData)
    if (response.code === 200) {
      ElMessage.success('状态更新成功')
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('状态更新失败')
    console.error('Update status error:', error)
  }
}

// 处理分配人员下拉选择
const handleAssigneeChange = async (developerId: number) => {
  if (!ticket.value) return

  try {
    const statusData = {
      status: 2, // 分配状态
      assignee_ids: [developerId]
    }

    const response = await updateTicketStatus(ticket.value.id, statusData)
    if (response.code === 200) {
      ElMessage.success('人员分配成功')
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('人员分配失败')
    console.error('Assign error:', error)
  }
}

// 处理优先级下拉选择
const handlePriorityChange = async (priority: string) => {
  if (!ticket.value) return

  try {
    const response = await updateTicketPriority(ticket.value.id, priority)
    if (response.code === 200) {
      ElMessage.success('优先级更新成功')
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('优先级更新失败')
    console.error('Update priority error:', error)
  }
}

const loadTicketDetail = async () => {
  loading.value = true
  try {
    // 并行加载工单详情和评论
    const [ticketResponse] = await Promise.all([
      getTicket(ticketId.value),
      loadComments() // 评论可以独立加载
    ])

    if (ticketResponse.code === 200) {
      ticket.value = ticketResponse.data
      commentForm.value.ticket_id = ticket.value.id
    } else {
      ElMessage.error(ticketResponse.message)
    }
  } catch (error: any) {
    ElMessage.error('加载工单详情失败')
    console.error('Load ticket error:', error)
  } finally {
    loading.value = false
  }
}

const loadComments = async () => {
  try {
    const response = await getComments(ticketId.value)
    if (response.code === 200) {
      comments.value = response.data || []
      // 评论加载后设置图片点击处理器
      nextTick(() => {
        setupImageClickHandlers()
      })
    }
  } catch (error) {
    console.error('Load comments error:', error)
  }
}

const loadDevelopers = async () => {
  try {
    const response = await getDevelopers()
    if (response.code === 200) {
      developers.value = response.data || []
    }
  } catch (error) {
    console.error('Load developers error:', error)
  }
}

const openStatusDialog = () => {
  if (!ticket.value) return

  // 重置表单，但不设置默认状态，让用户选择
  statusForm.value = {
    status: ticket.value.status, // 设置为当前状态作为默认值
    priority: ticket.value.priority || '', // 设置当前优先级
    reject_reason: '',
    feedback: '',
    assignee_ids: []
  }

  showStatusDialog.value = true
}

const openCommentDialog = () => {
  commentForm.value.content = ''
  showCommentDialog.value = true
}

const updateStatus = async () => {
  if (!ticket.value) return

  // 验证是否需要分配开发者
  if (needsAssignee.value && (!statusForm.value.assignee_ids || statusForm.value.assignee_ids.length === 0)) {
    ElMessage.warning('请选择要分配的开发者')
    return
  }

  updating.value = true
  try {
    const response = await updateTicketStatus(ticket.value.id, statusForm.value)
    if (response.code === 200) {
      ElMessage.success('状态更新成功')
      showStatusDialog.value = false
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('状态更新失败')
    console.error('Update status error:', error)
  } finally {
    updating.value = false
  }
}

const handleUpdatePriority = async () => {
  if (!ticket.value) return

  priorityUpdating.value = true
  try {
    const response = await updateTicketPriority(ticket.value.id, priorityForm.value.priority)
    if (response.code === 200) {
      ElMessage.success('优先级更新成功')
      showPriorityDialog.value = false
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('优先级更新失败')
    console.error('Update priority error:', error)
  } finally {
    priorityUpdating.value = false
  }
}

const transferTicket = async () => {
  if (!ticket.value || !transferForm.value.to_user_id) return

  transferring.value = true
  try {
    const response = await transferTicketApi(ticket.value.id, transferForm.value)
    if (response.code === 200) {
      ElMessage.success('工单转交成功')
      showTransferDialog.value = false
      await loadTicketDetail()
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('工单转交失败')
    console.error('Transfer ticket error:', error)
  } finally {
    transferring.value = false
  }
}

const addComment = async () => {
  if (!commentForm.value.content.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  commenting.value = true
  try {
    const response = await createComment(commentForm.value)
    if (response.code === 200) {
      ElMessage.success('评论发表成功')
      showCommentDialog.value = false
      commentForm.value.content = ''
      await loadComments()
      // 重新设置图片点击处理器
      nextTick(() => {
        setupImageClickHandlers()
      })
    } else {
      ElMessage.error(response.message)
    }
  } catch (error: any) {
    ElMessage.error('评论发表失败')
    console.error('Add comment error:', error)
  } finally {
    commenting.value = false
  }
}

// 工具方法
const getTypeName = (type: string) => {
  const names = {
    requirement: '需求',
    bug: '缺陷',
    suggestion: '建议'
  }
  return names[type] || type
}

const getTypeColor = (type: string) => {
  const colors = {
    requirement: 'primary',
    bug: 'danger',
    suggestion: 'success'
  }
  return colors[type] || ''
}

const getPriorityName = (priority: string) => {
  if (!priority) return '未设置'
  if (priority === 'default') return '默认'
  return priority // P0-P7 直接显示
}

const getPriorityColor = (priority: string) => {
  const colors = {
    'P0': 'danger',     // 红色 - 最高优先级
    'P1': 'danger',     // 红色
    'P2': 'warning',    // 橙色
    'P3': 'warning',    // 橙色
    'P4': 'primary',    // 蓝色
    'P5': 'primary',    // 蓝色
    'P6': 'info',       // 青色
    'P7': 'info',       // 青色 - 最低优先级
    'default': 'success' // 绿色 - 默认优先级
  }
  return colors[priority] || 'primary'
}

const getStatusName = (status: number) => {
  const names = {
    0: '已拒绝',
    1: '待处理',
    2: '已分配',
    3: '开发中',
    4: '已流转',
    5: '已提交',
    6: '已退回',
    7: '已完成'
  }
  return names[status] || '未知'
}

const getStatusColor = (status: number) => {
  const colors = {
    0: 'danger',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'info',
    5: 'primary',
    6: 'warning',
    7: 'success'
  }
  return colors[status] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 图片预览相关方法
const openImagePreview = (imageUrl: string) => {
  previewImageUrl.value = imageUrl
  showImagePreview.value = true
}

const closeImagePreview = () => {
  showImagePreview.value = false
  previewImageUrl.value = ''
}

// 设置图片点击处理器
const setupImageClickHandlers = () => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 处理评论中的图片
    const commentImages = document.querySelectorAll('.comment-content img')
    commentImages.forEach((img: Element) => {
      const imgElement = img as HTMLImageElement
      // 移除之前的事件监听器（如果有）
      imgElement.removeEventListener('click', handleImageClick)
      // 添加新的事件监听器
      imgElement.addEventListener('click', handleImageClick)
    })

    // 处理工单描述中的图片
    const descriptionImages = document.querySelectorAll('.description img')
    descriptionImages.forEach((img: Element) => {
      const imgElement = img as HTMLImageElement
      // 移除之前的事件监听器（如果有）
      imgElement.removeEventListener('click', handleImageClick)
      // 添加新的事件监听器
      imgElement.addEventListener('click', handleImageClick)
    })
  })
}

// 图片点击处理函数
const handleImageClick = (event: Event) => {
  const imgElement = event.target as HTMLImageElement
  openImagePreview(imgElement.src)
}

// 生命周期
onMounted(async () => {
  // 并行加载用户信息和开发者列表
  const profilePromise = userStore.fetchProfile()
  const developersPromise = loadDevelopers()

  // 立即开始加载工单详情
  const ticketPromise = loadTicketDetail()

  // 等待所有数据加载完成
  await Promise.all([profilePromise, developersPromise, ticketPromise])

  // 为评论中的图片添加点击事件
  setupImageClickHandlers()
})
</script>

<style scoped>
.ticket-detail {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 8px 0 0 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* 确保按钮组样式正确 */
.header-actions .el-button-group {
  display: inline-flex;
}

.header-actions .el-button-group .el-button {
  margin: 0;
}

.header-actions .el-button-group .el-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.header-actions .el-button-group .el-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.header-actions .el-button-group .el-dropdown .el-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.ticket-info {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.ticket-title h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.ticket-meta {
  display: flex;
  gap: 8px;
}

.ticket-id {
  font-size: 18px;
  color: #909399;
  font-weight: bold;
}

.ticket-content h3 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 16px;
}

.description {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  line-height: 1.6;
}

/* 工单描述HTML格式样式 */
.description p {
  margin: 8px 0;
}

.description h1,
.description h2,
.description h3,
.description h4,
.description h5,
.description h6 {
  margin: 16px 0 8px 0;
  font-weight: bold;
  color: #303133;
}

.description h1 { font-size: 20px; }
.description h2 { font-size: 18px; }
.description h3 { font-size: 16px; }

.description ul,
.description ol {
  margin: 8px 0;
  padding-left: 20px;
}

.description li {
  margin: 4px 0;
}

.description strong {
  font-weight: bold;
  color: #303133;
}

.description em {
  font-style: italic;
}

.description u {
  text-decoration: underline;
}

.description a {
  color: #409eff;
  text-decoration: none;
}

.description a:hover {
  text-decoration: underline;
}

/* 工单描述中的图片样式 */
.description :deep(img) {
  width: 500px !important;
  max-width: 500px !important;
  height: auto !important;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 8px 0;
  display: block !important;
}

.description :deep(img:hover) {
  border-color: #409eff;
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.reject-reason,
.feedback {
  margin-bottom: 20px;
}

.ticket-sidebar {
  padding-left: 20px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item .label {
  width: 80px;
  color: #909399;
  flex-shrink: 0;
}

.info-item .value {
  color: #303133;
}

.assignees {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.assignee-tag {
  margin: 0;
}

.transfer-history,
.comments-section {
  margin-bottom: 20px;
}

.transfer-item {
  color: #303133;
}

.transfer-reason {
  margin-top: 5px;
  color: #909399;
  font-size: 14px;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comments-header h3 {
  margin: 0;
}

.no-comments {
  text-align: center;
  padding: 40px 0;
}

.comments-list {
  max-height: 500px;
  overflow-y: auto;
}

.comment-item {
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.comment-meta {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
}

.comment-meta strong {
  color: #303133;
  font-size: 14px;
}

.comment-time {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.comment-content {
  margin-left: 42px;
  color: #606266;
  line-height: 1.6;
}

/* 评论内容HTML格式样式 */
.comment-content p {
  margin: 8px 0;
}

.comment-content ul,
.comment-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.comment-content li {
  margin: 4px 0;
}

.comment-content strong {
  font-weight: bold;
  color: #303133;
}

.comment-content em {
  font-style: italic;
}

.comment-content u {
  text-decoration: underline;
}

.comment-content a {
  color: #409eff;
  text-decoration: none;
}

.comment-content a:hover {
  text-decoration: underline;
}

/* 评论中的图片缩略图样式 */
.comment-content :deep(img) {
  width: 150px !important;
  height: 150px !important;
  min-width: 150px !important;
  min-height: 150px !important;
  max-width: 150px !important;
  max-height: 150px !important;
  border-radius: 8px;
  border: 2px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 8px 4px;
  display: inline-block !important;
  object-fit: cover;
}

.comment-content :deep(img:hover) {
  border-color: #409eff;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.comment-editor {
  width: 100%;
}

/* 表单提示样式 */
.form-tip {
  margin-top: 4px;
}

.form-tip .el-text {
  font-size: 12px;
}

/* 图片预览对话框样式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  width: auto;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.editor-tips {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ticket-detail {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .card-header {
    flex-direction: column;
    gap: 10px;
  }

  .ticket-sidebar {
    padding-left: 0;
    margin-top: 20px;
  }
}

/* 骨架屏样式 */
.ticket-skeleton {
  padding: 20px;
}

.comments-skeleton {
  padding: 20px;
}

/* 优化loading状态 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

.el-loading-text {
  color: #409eff;
  font-size: 14px;
}
</style>
