<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div>
        <button onclick="testWebSocket()">Test WebSocket Connection</button>
        <button onclick="closeWebSocket()">Close Connection</button>
    </div>
    <div>
        <h3>Log:</h3>
        <div id="log" style="border: 1px solid #ccc; padding: 10px; height: 300px; overflow-y: scroll;"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function testWebSocket() {
            if (ws) {
                log('Closing existing connection...');
                ws.close();
            }
            
            // 使用测试token（这里需要一个有效的token）
            const testToken = 'test-token'; // 这里需要替换为真实token
            const wsUrl = `ws://localhost:8081/api/ssh/ws?connection_id=1&token=${encodeURIComponent(testToken)}`;
            
            log(`Attempting to connect to: ${wsUrl}`);
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connection opened successfully!');
                };
                
                ws.onmessage = function(event) {
                    log(`📨 Received message: ${event.data}`);
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`);
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket closed. Code: ${event.code}, Reason: ${event.reason}, Clean: ${event.wasClean}`);
                };
                
                // 设置超时
                setTimeout(() => {
                    if (ws && ws.readyState === WebSocket.CONNECTING) {
                        log('⏰ Connection timeout after 10 seconds');
                        ws.close();
                    }
                }, 10000);
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error}`);
            }
        }
        
        function closeWebSocket() {
            if (ws) {
                log('Manually closing WebSocket connection...');
                ws.close(1000, 'Manual close');
                ws = null;
            } else {
                log('No active WebSocket connection to close');
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('WebSocket test page loaded');
            log('Click "Test WebSocket Connection" to start testing');
        };
    </script>
</body>
</html>
