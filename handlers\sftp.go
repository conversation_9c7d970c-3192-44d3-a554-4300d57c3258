package handlers

import (
	"ReqFlow/models"
	"ReqFlow/services"
	"ReqFlow/utils"
	"fmt"
	"io"
	"log"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/sftp"
	"gorm.io/gorm"
)

// SFTPHandler SFTP处理器
type SFTPHandler struct {
	db         *gorm.DB
	sshService *services.SSHService
}

// NewSFTPHandler 创建SFTP处理器
func NewSFTPHandler(db *gorm.DB) *SFTPHandler {
	return &SFTPHandler{
		db:         db,
		sshService: services.NewSSHService(db),
	}
}

// FileInfo 文件信息
type FileInfo struct {
	Name        string `json:"name"`
	Size        int64  `json:"size"`
	IsDirectory bool   `json:"isDirectory"`
	ModTime     string `json:"modTime"`
	Permissions string `json:"permissions"`
}

// GetFileList 获取文件列表
func (h *SFTPHandler) GetFileList(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 获取连接ID和路径
	connectionIDStr := c.Query("connection_id")
	connectionID, err := strconv.ParseUint(connectionIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	path := c.Query("path")
	if path == "" {
		path = "/"
	}

	// 获取SSH连接配置
	connection, err := h.sshService.GetConnection(uint(connectionID), currentUser.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "SSH connection not found")
		} else {
			utils.InternalServerError(c, "Failed to fetch SSH connection")
		}
		return
	}

	// 创建SFTP客户端
	sftpClient, err := h.createSFTPClient(connection)
	if err != nil {
		utils.BadRequest(c, "Failed to create SFTP client: "+err.Error())
		return
	}
	defer sftpClient.Close()

	// 读取目录内容
	files, err := sftpClient.ReadDir(path)
	if err != nil {
		utils.InternalServerError(c, "Failed to read directory: "+err.Error())
		return
	}

	// 转换文件信息
	var fileList []FileInfo
	for _, file := range files {
		fileInfo := FileInfo{
			Name:        file.Name(),
			Size:        file.Size(),
			IsDirectory: file.IsDir(),
			ModTime:     file.ModTime().Format(time.RFC3339),
			Permissions: file.Mode().String(),
		}
		fileList = append(fileList, fileInfo)
	}

	utils.Success(c, fileList)
}

// DownloadFile 下载文件
func (h *SFTPHandler) DownloadFile(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 获取参数
	connectionIDStr := c.Query("connection_id")
	connectionID, err := strconv.ParseUint(connectionIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	filePath := c.Query("path")
	if filePath == "" {
		utils.BadRequest(c, "File path is required")
		return
	}

	// 获取SSH连接配置
	connection, err := h.sshService.GetConnection(uint(connectionID), currentUser.ID)
	if err != nil {
		utils.NotFound(c, "SSH connection not found")
		return
	}

	// 创建SFTP客户端
	sftpClient, err := h.createSFTPClient(connection)
	if err != nil {
		utils.BadRequest(c, "Failed to create SFTP client: "+err.Error())
		return
	}
	defer sftpClient.Close()

	// 打开远程文件
	remoteFile, err := sftpClient.Open(filePath)
	if err != nil {
		utils.InternalServerError(c, "Failed to open remote file: "+err.Error())
		return
	}
	defer remoteFile.Close()

	// 获取文件信息
	stat, err := remoteFile.Stat()
	if err != nil {
		utils.InternalServerError(c, "Failed to get file info: "+err.Error())
		return
	}

	// 设置响应头
	fileName := filepath.Base(filePath)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Length", fmt.Sprintf("%d", stat.Size()))

	// 流式传输文件内容
	_, err = io.Copy(c.Writer, remoteFile)
	if err != nil {
		utils.InternalServerError(c, "Failed to transfer file: "+err.Error())
		return
	}
}

// UploadFile 上传文件
func (h *SFTPHandler) UploadFile(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 获取参数
	connectionIDStr := c.PostForm("connection_id")
	connectionID, err := strconv.ParseUint(connectionIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	remotePath := c.PostForm("path")
	if remotePath == "" {
		remotePath = "/"
	}

	log.Printf("Upload request - Connection ID: %d, Path: %s", connectionID, remotePath)

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("Failed to get uploaded file: %v", err)
		utils.BadRequest(c, "Failed to get uploaded file: "+err.Error())
		return
	}
	defer file.Close()

	log.Printf("Uploading file: %s, Size: %d", header.Filename, header.Size)

	// 获取SSH连接配置
	connection, err := h.sshService.GetConnection(uint(connectionID), currentUser.ID)
	if err != nil {
		log.Printf("SSH connection not found: %v", err)
		utils.NotFound(c, "SSH connection not found")
		return
	}

	// 创建SFTP客户端
	sftpClient, err := h.createSFTPClient(connection)
	if err != nil {
		log.Printf("Failed to create SFTP client: %v", err)
		utils.BadRequest(c, "Failed to create SFTP client: "+err.Error())
		return
	}
	defer sftpClient.Close()

	// 构建远程文件路径
	var remoteFilePath string
	if remotePath == "/" {
		remoteFilePath = "/" + header.Filename
	} else if strings.HasSuffix(remotePath, "/") {
		remoteFilePath = remotePath + header.Filename
	} else {
		remoteFilePath = remotePath + "/" + header.Filename
	}

	log.Printf("Remote file path: %s", remoteFilePath)

	// 创建远程文件
	remoteFile, err := sftpClient.Create(remoteFilePath)
	if err != nil {
		log.Printf("Failed to create remote file: %v", err)
		utils.InternalServerError(c, "Failed to create remote file: "+err.Error())
		return
	}
	defer remoteFile.Close()

	// 复制文件内容
	bytesWritten, err := io.Copy(remoteFile, file)
	if err != nil {
		log.Printf("Failed to upload file: %v", err)
		utils.InternalServerError(c, "Failed to upload file: "+err.Error())
		return
	}

	log.Printf("File uploaded successfully: %s (%d bytes)", remoteFilePath, bytesWritten)

	utils.Success(c, gin.H{
		"message":  "File uploaded successfully",
		"filename": header.Filename,
		"path":     remoteFilePath,
		"size":     bytesWritten,
	})
}

// CreateFolder 创建文件夹
func (h *SFTPHandler) CreateFolder(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var req struct {
		ConnectionID uint   `json:"connection_id" binding:"required"`
		Path         string `json:"path" binding:"required"`
		FolderName   string `json:"folder_name" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request data")
		return
	}

	// 获取SSH连接配置
	connection, err := h.sshService.GetConnection(req.ConnectionID, currentUser.ID)
	if err != nil {
		utils.NotFound(c, "SSH connection not found")
		return
	}

	// 创建SFTP客户端
	sftpClient, err := h.createSFTPClient(connection)
	if err != nil {
		utils.BadRequest(c, "Failed to create SFTP client: "+err.Error())
		return
	}
	defer sftpClient.Close()

	// 构建文件夹路径
	folderPath := filepath.Join(req.Path, req.FolderName)

	// 创建文件夹
	err = sftpClient.Mkdir(folderPath)
	if err != nil {
		utils.InternalServerError(c, "Failed to create folder: "+err.Error())
		return
	}

	utils.Success(c, gin.H{
		"message": "Folder created successfully",
		"path":    folderPath,
	})
}

// DeleteFile 删除文件或文件夹
func (h *SFTPHandler) DeleteFile(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var req struct {
		ConnectionID uint   `json:"connection_id" binding:"required"`
		Path         string `json:"path" binding:"required"`
		IsDirectory  bool   `json:"is_directory"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request data")
		return
	}

	// 获取SSH连接配置
	connection, err := h.sshService.GetConnection(req.ConnectionID, currentUser.ID)
	if err != nil {
		utils.NotFound(c, "SSH connection not found")
		return
	}

	// 创建SFTP客户端
	sftpClient, err := h.createSFTPClient(connection)
	if err != nil {
		utils.BadRequest(c, "Failed to create SFTP client: "+err.Error())
		return
	}
	defer sftpClient.Close()

	// 删除文件或文件夹
	var deleteErr error
	if req.IsDirectory {
		deleteErr = sftpClient.RemoveDirectory(req.Path)
	} else {
		deleteErr = sftpClient.Remove(req.Path)
	}

	if deleteErr != nil {
		utils.InternalServerError(c, "Failed to delete: "+deleteErr.Error())
		return
	}

	utils.Success(c, gin.H{
		"message": "Deleted successfully",
		"path":    req.Path,
	})
}

// createSFTPClient 创建SFTP客户端
func (h *SFTPHandler) createSFTPClient(conn *models.SSHConnection) (*sftp.Client, error) {
	// 创建SSH客户端
	sshClient, err := h.sshService.CreateSSHClient(conn)
	if err != nil {
		return nil, fmt.Errorf("failed to create SSH client: %v", err)
	}

	// 创建SFTP客户端
	sftpClient, err := sftp.NewClient(sshClient)
	if err != nil {
		sshClient.Close()
		return nil, fmt.Errorf("failed to create SFTP client: %v", err)
	}

	return sftpClient, nil
}
