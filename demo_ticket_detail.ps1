# 工单详情页面演示脚本

Write-Host "=== ReqFlow 工单详情页面演示 ===" -ForegroundColor Green

# 1. 管理员登录并创建工单
Write-Host "`n1. 创建演示工单..." -ForegroundColor Yellow
$token = (Invoke-RestMethod -Uri "http://localhost:8081/api/login" -Method POST -ContentType "application/json" -Body '{"username":"admin","password":"admin123456"}').data.token
$headers = @{"Authorization" = "Bearer $token"; "Content-Type" = "application/json"}

$ticketData = @{
    title = "【演示】工单详情页面功能测试"
    description = "这是一个完整的演示工单，用于展示工单详情页面的所有功能：`n`n1. 工单基本信息展示`n2. 状态流转功能`n3. 评论系统`n4. 权限控制`n5. 响应式设计`n`n请在前端页面中测试各项功能。"
    type = "requirement"
    priority = "high"
} | ConvertTo-Json

$ticket = Invoke-RestMethod -Uri "http://localhost:8081/api/tickets" -Method POST -Headers $headers -Body $ticketData
$ticketId = $ticket.data.id
Write-Host "✓ 演示工单创建成功，ID: $ticketId" -ForegroundColor Green

# 2. 添加多条评论
Write-Host "`n2. 添加演示评论..." -ForegroundColor Yellow

$comments = @(
    "这是第一条评论，由管理员添加。用于测试评论显示功能。",
    "这是第二条评论，包含多行内容：`n`n- 功能点1：工单信息展示`n- 功能点2：状态管理`n- 功能点3：评论系统",
    "这是第三条评论，用于测试评论列表的滚动效果和时间显示。"
)

foreach ($content in $comments) {
    $commentData = @{
        ticket_id = $ticketId
        content = $content
    } | ConvertTo-Json
    
    Invoke-RestMethod -Uri "http://localhost:8081/api/comments" -Method POST -Headers $headers -Body $commentData | Out-Null
    Start-Sleep -Seconds 1
}
Write-Host "✓ 演示评论添加完成" -ForegroundColor Green

# 3. 注册开发者用户（如果不存在）
Write-Host "`n3. 准备开发者账号..." -ForegroundColor Yellow
try {
    $devRegisterData = @{
        username = "demo_developer"
        email = "<EMAIL>"
        password = "demo123456"
        invite_code = "DEV2024DEMO"
    } | ConvertTo-Json

    Invoke-RestMethod -Uri "http://localhost:8081/api/register" -Method POST -ContentType "application/json" -Body $devRegisterData | Out-Null
    Write-Host "✓ 演示开发者账号创建成功" -ForegroundColor Green
} catch {
    Write-Host "! 演示开发者账号已存在，继续..." -ForegroundColor Yellow
}

# 4. 获取开发者信息并分配工单
$devLoginResult = Invoke-RestMethod -Uri "http://localhost:8081/api/login" -Method POST -ContentType "application/json" -Body '{"username":"demo_developer","password":"demo123456"}'
$devUserId = $devLoginResult.data.user_id

Write-Host "`n4. 分配工单给开发者..." -ForegroundColor Yellow
$assignData = @{
    status = 2
    assignee_ids = @($devUserId)
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8081/api/tickets/$ticketId/status" -Method PUT -Headers $headers -Body $assignData | Out-Null
Write-Host "✓ 工单已分配给开发者" -ForegroundColor Green

# 5. 开发者添加评论
Write-Host "`n5. 开发者添加评论..." -ForegroundColor Yellow
$devToken = $devLoginResult.data.token
$devHeaders = @{"Authorization" = "Bearer $devToken"; "Content-Type" = "application/json"}

$devCommentData = @{
    ticket_id = $ticketId
    content = "我是开发者 demo_developer，已收到工单分配。`n`n计划处理步骤：`n1. 分析需求`n2. 设计方案`n3. 编码实现`n4. 测试验证`n`n预计完成时间：2天"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:8081/api/comments" -Method POST -Headers $devHeaders -Body $devCommentData | Out-Null
Write-Host "✓ 开发者评论添加完成" -ForegroundColor Green

# 6. 显示演示信息
Write-Host "`n=== 演示环境准备完成 ===" -ForegroundColor Green
Write-Host "`n📋 演示工单信息：" -ForegroundColor Cyan
Write-Host "  工单ID: $ticketId" -ForegroundColor White
Write-Host "  标题: 【演示】工单详情页面功能测试" -ForegroundColor White
Write-Host "  状态: 已分配 (可测试状态流转)" -ForegroundColor White
Write-Host "  评论数: 4条 (包含管理员和开发者评论)" -ForegroundColor White

Write-Host "`n🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  前端地址: http://localhost:5173/tickets/$ticketId" -ForegroundColor Yellow

Write-Host "`n👤 测试账号：" -ForegroundColor Cyan
Write-Host "  管理员: admin / admin123456" -ForegroundColor White
Write-Host "    - 可以审核工单、分配开发者、验收工单" -ForegroundColor Gray
Write-Host "    - 可以查看所有工单和评论" -ForegroundColor Gray
Write-Host "  开发者: demo_developer / demo123456" -ForegroundColor White
Write-Host "    - 可以更新工单状态、转交工单" -ForegroundColor Gray
Write-Host "    - 可以添加评论" -ForegroundColor Gray

Write-Host "`n🎯 可测试功能：" -ForegroundColor Cyan
Write-Host "  ✅ 工单信息完整展示" -ForegroundColor Green
Write-Host "  ✅ 基于角色的权限控制" -ForegroundColor Green
Write-Host "  ✅ 状态流转管理" -ForegroundColor Green
Write-Host "  ✅ 评论系统" -ForegroundColor Green
Write-Host "  ✅ 工单转交功能" -ForegroundColor Green
Write-Host "  ✅ 响应式设计" -ForegroundColor Green

Write-Host "`n🚀 开始测试吧！" -ForegroundColor Yellow
