package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// TicketStatus 工单状态枚举
type TicketStatus int

const (
	StatusRejected    TicketStatus = 0 // 已拒绝
	StatusPending     TicketStatus = 1 // 待处理
	StatusAssigned    TicketStatus = 2 // 已分配
	StatusInProgress  TicketStatus = 3 // 开发中
	StatusTransferred TicketStatus = 4 // 已流转
	StatusSubmitted   TicketStatus = 5 // 已提交
	StatusReturned    TicketStatus = 6 // 已退回
	StatusCompleted   TicketStatus = 7 // 已完成
)

// TicketType 工单类型枚举
type TicketType string

const (
	TypeRequirement TicketType = "requirement" // 需求
	TypeBug         TicketType = "bug"         // 缺陷
	TypeSuggestion  TicketType = "suggestion"  // 建议
)

// TicketPriority 工单优先级枚举
type TicketPriority string

const (
	PriorityP0      TicketPriority = "P0" // 最高优先级
	PriorityP1      TicketPriority = "P1"
	PriorityP2      TicketPriority = "P2"
	PriorityP3      TicketPriority = "P3"
	PriorityP4      TicketPriority = "P4"
	PriorityP5      TicketPriority = "P5"
	PriorityP6      TicketPriority = "P6"
	PriorityDefault TicketPriority = "default" // 默认优先级
	PriorityP7      TicketPriority = "P7"      // 最低优先级
)

// UintArray 用于存储JSON数组
type UintArray []uint

// Scan 实现 sql.Scanner 接口
func (ua *UintArray) Scan(value interface{}) error {
	if value == nil {
		*ua = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into UintArray", value)
	}

	return json.Unmarshal(bytes, ua)
}

// Value 实现 driver.Valuer 接口
func (ua UintArray) Value() (driver.Value, error) {
	if ua == nil {
		return nil, nil
	}
	return json.Marshal(ua)
}

// Ticket 工单模型
type Ticket struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Title        string         `json:"title" gorm:"not null;size:200"`
	Description  string         `json:"description" gorm:"type:longtext"`
	Type         TicketType     `json:"type" gorm:"not null"`
	Priority     TicketPriority `json:"priority" gorm:"default:'default'"`
	Status       TicketStatus   `json:"status" gorm:"default:1"`
	CreatorID    uint           `json:"creator_id" gorm:"not null"`
	AssigneeIDs  UintArray      `json:"assignee_ids" gorm:"type:json"`
	RejectReason string         `json:"reject_reason" gorm:"size:500"`
	Feedback     string         `json:"feedback" gorm:"type:text"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Creator   *User            `json:"creator,omitempty" gorm:"foreignKey:CreatorID"`
	Comments  []Comment        `json:"comments,omitempty" gorm:"foreignKey:TicketID"`
	Transfers []TicketTransfer `json:"transfers,omitempty" gorm:"foreignKey:TicketID"`
}

// TableName 指定表名
func (Ticket) TableName() string {
	return "tickets"
}

// GetStatusName 获取状态名称
func (t *Ticket) GetStatusName() string {
	statusNames := map[TicketStatus]string{
		StatusRejected:    "已拒绝",
		StatusPending:     "待处理",
		StatusAssigned:    "已分配",
		StatusInProgress:  "开发中",
		StatusTransferred: "已流转",
		StatusSubmitted:   "已提交",
		StatusReturned:    "已退回",
		StatusCompleted:   "已完成",
	}
	return statusNames[t.Status]
}

// GetAssigneeIDs 获取分配者ID列表
func (t *Ticket) GetAssigneeIDs() []uint {
	if t.AssigneeIDs == nil {
		return []uint{}
	}
	return []uint(t.AssigneeIDs)
}

// AddAssignee 添加分配者
func (t *Ticket) AddAssignee(userID uint) {
	assigneeIDs := t.GetAssigneeIDs()
	for _, id := range assigneeIDs {
		if id == userID {
			return // 已存在
		}
	}
	t.AssigneeIDs = append(t.AssigneeIDs, userID)
}

// RemoveAssignee 移除分配者
func (t *Ticket) RemoveAssignee(userID uint) {
	assigneeIDs := t.GetAssigneeIDs()
	newAssigneeIDs := make([]uint, 0)
	for _, id := range assigneeIDs {
		if id != userID {
			newAssigneeIDs = append(newAssigneeIDs, id)
		}
	}
	t.AssigneeIDs = UintArray(newAssigneeIDs)
}

// CanTransitionTo 检查是否可以转换到目标状态
func (t *Ticket) CanTransitionTo(targetStatus TicketStatus, userRole UserRole) bool {
	switch t.Status {
	case StatusPending:
		// 待处理 -> 已分配/已拒绝 (仅管理员)
		return userRole == RoleAdmin && (targetStatus == StatusAssigned || targetStatus == StatusRejected)
	case StatusAssigned:
		// 已分配 -> 开发中 (开发者/管理员)
		return (userRole == RoleDeveloper || userRole == RoleAdmin) && targetStatus == StatusInProgress
	case StatusInProgress:
		// 开发中 -> 已提交/已流转 (开发者/管理员)
		return (userRole == RoleDeveloper || userRole == RoleAdmin) &&
			(targetStatus == StatusSubmitted || targetStatus == StatusTransferred)
	case StatusTransferred:
		// 已流转 -> 开发中 (接收的开发者/管理员)
		return (userRole == RoleDeveloper || userRole == RoleAdmin) && targetStatus == StatusInProgress
	case StatusSubmitted:
		// 已提交 -> 已完成/已退回 (仅管理员)
		return userRole == RoleAdmin && (targetStatus == StatusCompleted || targetStatus == StatusReturned)
	case StatusReturned:
		// 已退回 -> 开发中 (开发者/管理员)
		return (userRole == RoleDeveloper || userRole == RoleAdmin) && targetStatus == StatusInProgress
	default:
		return false
	}
}
