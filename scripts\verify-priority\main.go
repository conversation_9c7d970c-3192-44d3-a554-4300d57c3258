package main

import (
	"ReqFlow/config"
	"ReqFlow/models"
	"log"
)

func main() {
	// 连接数据库
	db := config.InitDatabase()
	if db == nil {
		log.Fatal("数据库连接失败")
	}

	log.Println("验证工单优先级...")

	// 统计各种优先级的工单数量
	var priorityStats []struct {
		Priority string
		Count    int64
	}

	db.Model(&models.Ticket{}).
		Select("priority, COUNT(*) as count").
		Group("priority").
		Find(&priorityStats)

	log.Println("优先级分布:")
	totalTickets := int64(0)
	for _, stat := range priorityStats {
		log.Printf("  %s: %d 个工单", stat.Priority, stat.Count)
		totalTickets += stat.Count
	}
	log.Printf("总工单数: %d", totalTickets)

	// 检查是否还有老格式的优先级
	var oldFormatCount int64
	db.Model(&models.Ticket{}).
		Where("priority NOT IN ('P0', 'P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7') OR priority = '' OR priority IS NULL").
		Count(&oldFormatCount)

	if oldFormatCount > 0 {
		log.Printf("⚠️  警告: 发现 %d 个工单使用旧优先级格式", oldFormatCount)

		// 显示这些工单的详细信息
		var oldTickets []models.Ticket
		db.Where("priority NOT IN ('P0', 'P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'P7') OR priority = '' OR priority IS NULL").
			Find(&oldTickets)

		for _, ticket := range oldTickets {
			log.Printf("  工单 %d: 优先级 = '%s'", ticket.ID, ticket.Priority)
		}
	} else {
		log.Println("✅ 所有工单都使用有效的P0-P7优先级格式!")
	}

	// 检查P7优先级的工单数量
	var p7Count int64
	db.Model(&models.Ticket{}).Where("priority = 'P7'").Count(&p7Count)
	log.Printf("📊 Tickets with P7 priority: %d", p7Count)
}
