package models

import (
	"time"

	"gorm.io/gorm"
)

// InviteCode 邀请码模型
type InviteCode struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Code      string         `json:"code" gorm:"uniqueIndex;not null;size:50"`
	RoleType  UserRole       `json:"role_type" gorm:"not null"`
	TeamName  string         `json:"team_name" gorm:"not null;size:100"`
	MaxUses   int            `json:"max_uses" gorm:"default:100"`
	UsedCount int            `json:"used_count" gorm:"default:0"`
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Users []User `json:"users,omitempty" gorm:"foreignKey:InviteCodeID"`
}

// TableName 指定表名
func (InviteCode) TableName() string {
	return "invite_codes"
}

// CanUse 检查邀请码是否可用
func (ic *InviteCode) CanUse() bool {
	return ic.IsActive && ic.UsedCount < ic.MaxUses
}

// Use 使用邀请码
func (ic *InviteCode) Use() {
	ic.UsedCount++
}
