package handlers

import (
	"ReqFlow/models"
	"ReqFlow/services"
	"ReqFlow/utils"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"golang.org/x/crypto/ssh"
	"gorm.io/gorm"
)

// SSHHandler SSH处理器
type SSHHandler struct {
	db         *gorm.DB
	sshService *services.SSHService
	upgrader   websocket.Upgrader
}

// NewSSHHandler 创建SSH处理器
func NewSSHHandler(db *gorm.DB) *SSHHandler {
	return &SSHHandler{
		db:         db,
		sshService: services.NewSSHService(db),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				origin := r.Header.Get("Origin")
				log.Printf("WebSocket connection from origin: %s", origin)
				// 允许本地开发环境的所有连接
				return origin == "http://localhost:5173" ||
					origin == "http://localhost:5174" ||
					origin == "http://localhost:5175" ||
					origin == "http://127.0.0.1:5173" ||
					origin == "http://127.0.0.1:5174" ||
					origin == "http://127.0.0.1:5175" ||
					origin == "http://localhost:3000" ||
					origin == "" // 允许没有Origin头的连接
			},
		},
	}
}

// CreateConnection 创建SSH连接配置
func (h *SSHHandler) CreateConnection(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var req models.SSHConnection
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request data")
		return
	}

	req.UserID = currentUser.ID
	if err := h.sshService.CreateConnection(&req); err != nil {
		utils.InternalServerError(c, "Failed to create SSH connection")
		return
	}

	utils.Success(c, req)
}

// GetConnections 获取SSH连接列表
func (h *SSHHandler) GetConnections(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	connections, err := h.sshService.GetConnections(currentUser.ID)
	if err != nil {
		utils.InternalServerError(c, "Failed to fetch SSH connections")
		return
	}

	utils.Success(c, connections)
}

// GetConnection 获取单个SSH连接
func (h *SSHHandler) GetConnection(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	connection, err := h.sshService.GetConnection(uint(id), currentUser.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "SSH connection not found")
		} else {
			utils.InternalServerError(c, "Failed to fetch SSH connection")
		}
		return
	}

	utils.Success(c, connection)
}

// UpdateConnection 更新SSH连接配置
func (h *SSHHandler) UpdateConnection(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	// 检查连接是否存在且属于当前用户
	connection, err := h.sshService.GetConnection(uint(id), currentUser.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "SSH connection not found")
		} else {
			utils.InternalServerError(c, "Failed to fetch SSH connection")
		}
		return
	}

	var req models.SSHConnection
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request data")
		return
	}

	// 保持原有的ID和用户ID
	req.ID = connection.ID
	req.UserID = currentUser.ID

	if err := h.sshService.UpdateConnection(&req); err != nil {
		utils.InternalServerError(c, "Failed to update SSH connection")
		return
	}

	utils.Success(c, req)
}

// DeleteConnection 删除SSH连接配置
func (h *SSHHandler) DeleteConnection(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	if err := h.sshService.DeleteConnection(uint(id), currentUser.ID); err != nil {
		utils.InternalServerError(c, "Failed to delete SSH connection")
		return
	}

	utils.Success(c, gin.H{"message": "SSH connection deleted successfully"})
}

// TestConnection 测试SSH连接
func (h *SSHHandler) TestConnection(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	connection, err := h.sshService.GetConnection(uint(id), currentUser.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "SSH connection not found")
		} else {
			utils.InternalServerError(c, "Failed to fetch SSH connection")
		}
		return
	}

	// 测试连接
	client, err := h.sshService.CreateSSHClient(connection)
	if err != nil {
		utils.BadRequest(c, "Connection test failed: "+err.Error())
		return
	}
	defer client.Close()

	utils.Success(c, gin.H{"message": "Connection test successful"})
}

// GetServerInfo 获取服务器资源信息
func (h *SSHHandler) GetServerInfo(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "Invalid connection ID")
		return
	}

	connection, err := h.sshService.GetConnection(uint(id), currentUser.ID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "SSH connection not found")
		} else {
			utils.InternalServerError(c, "Failed to fetch SSH connection")
		}
		return
	}

	// 创建SSH客户端
	client, err := h.sshService.CreateSSHClient(connection)
	if err != nil {
		utils.BadRequest(c, "Failed to connect to server: "+err.Error())
		return
	}
	defer client.Close()

	// 获取服务器信息
	serverInfo, err := h.getServerResourceInfo(client)
	if err != nil {
		utils.InternalServerError(c, "Failed to get server info: "+err.Error())
		return
	}

	utils.Success(c, serverInfo)
}

// getServerResourceInfo 获取服务器资源信息
func (h *SSHHandler) getServerResourceInfo(client *ssh.Client) (*ServerInfo, error) {
	info := &ServerInfo{}

	// 获取CPU使用率 - 使用更兼容的命令
	if cpuUsage, err := h.executeCommand(client, "grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$3+$4+$5)} END {print usage}'"); err == nil {
		if cpu, err := strconv.ParseFloat(strings.TrimSpace(cpuUsage), 64); err == nil {
			info.CPU = cpu
		}
	} else {
		// 备用方案：使用top命令
		if cpuUsage, err := h.executeCommand(client, "top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id.*/\\1/' | awk '{print 100 - $1}'"); err == nil {
			if cpu, err := strconv.ParseFloat(strings.TrimSpace(cpuUsage), 64); err == nil {
				info.CPU = cpu
			}
		}
	}

	// 获取内存信息 - 修复单位转换
	if memInfo, err := h.executeCommand(client, "free -m | grep '^Mem:' | awk '{printf \"%.1f %.1f %.1f\", $3/$2*100, $3/1024, $2/1024}'"); err == nil {
		parts := strings.Fields(strings.TrimSpace(memInfo))
		log.Printf("Memory info raw: %s, parts: %v", memInfo, parts)
		if len(parts) >= 3 {
			if memory, err := strconv.ParseFloat(parts[0], 64); err == nil {
				info.Memory = memory
			}
			if memUsed, err := strconv.ParseFloat(parts[1], 64); err == nil {
				info.MemoryUsed = memUsed
			}
			if memTotal, err := strconv.ParseFloat(parts[2], 64); err == nil {
				info.MemoryTotal = memTotal
			}
		}
	}

	// 获取硬盘信息 - 修复解析逻辑
	if diskInfo, err := h.executeCommand(client, "df -BG / | awk 'NR==2{gsub(/G/, \"\", $2); gsub(/G/, \"\", $3); gsub(/%/, \"\", $5); printf \"%.0f %.0f %.0f\", $5, $3, $2}'"); err == nil {
		parts := strings.Fields(strings.TrimSpace(diskInfo))
		log.Printf("Disk info raw: %s, parts: %v", diskInfo, parts)
		if len(parts) >= 3 {
			if disk, err := strconv.ParseFloat(parts[0], 64); err == nil {
				info.Disk = disk
			}
			if diskUsed, err := strconv.ParseFloat(parts[1], 64); err == nil {
				info.DiskUsed = diskUsed
			}
			if diskTotal, err := strconv.ParseFloat(parts[2], 64); err == nil {
				info.DiskTotal = diskTotal
			}
		}
	}

	// 获取系统负载
	if loadInfo, err := h.executeCommand(client, "uptime | awk -F'load average:' '{print $2}' | sed 's/,//g' | awk '{printf \"%.2f %.2f %.2f\", $1, $2, $3}'"); err == nil {
		parts := strings.Fields(strings.TrimSpace(loadInfo))
		log.Printf("Load info raw: %s, parts: %v", loadInfo, parts)
		if len(parts) >= 3 {
			if load1, err := strconv.ParseFloat(parts[0], 64); err == nil {
				info.Load1 = load1
			}
			if load5, err := strconv.ParseFloat(parts[1], 64); err == nil {
				info.Load5 = load5
			}
			if load15, err := strconv.ParseFloat(parts[2], 64); err == nil {
				info.Load15 = load15
			}
		}
	}

	log.Printf("Final server info: %+v", info)
	return info, nil
}

// executeCommand 执行SSH命令并返回结果
func (h *SSHHandler) executeCommand(client *ssh.Client, command string) (string, error) {
	session, err := client.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()

	output, err := session.Output(command)
	if err != nil {
		return "", err
	}

	return string(output), nil
}

// WebSocketMessage WebSocket消息结构
type WebSocketMessage struct {
	Type    string      `json:"type"`
	Data    interface{} `json:"data"`
	Session string      `json:"session,omitempty"`
}

// TerminalResize 终端大小调整消息
type TerminalResize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// ServerInfo 服务器信息
type ServerInfo struct {
	CPU         float64 `json:"cpu"`
	Memory      float64 `json:"memory"`
	MemoryUsed  float64 `json:"memoryUsed"`
	MemoryTotal float64 `json:"memoryTotal"`
	Disk        float64 `json:"disk"`
	DiskUsed    float64 `json:"diskUsed"`
	DiskTotal   float64 `json:"diskTotal"`
	Load1       float64 `json:"load1"`
	Load5       float64 `json:"load5"`
	Load15      float64 `json:"load15"`
}

// ConnectWebSocket 建立WebSocket连接进行SSH交互
func (h *SSHHandler) ConnectWebSocket(c *gin.Context) {
	log.Printf("WebSocket connection attempt from %s", c.ClientIP())

	// 从URL参数获取token进行认证
	token := c.Query("token")
	if token == "" {
		log.Printf("WebSocket connection rejected: missing token")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing token"})
		return
	}

	// 验证token
	claims, err := utils.ParseToken(token)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// 获取用户信息
	var currentUser models.User
	if err := h.db.Where("id = ? AND is_active = ?", claims.UserID, true).First(&currentUser).Error; err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found or inactive"})
		return
	}

	// 检查权限
	if currentUser.Role != models.RoleAdmin && currentUser.Role != models.RoleDeveloper {
		c.JSON(http.StatusForbidden, gin.H{"error": "Insufficient permissions"})
		return
	}

	// 获取连接ID
	connectionIDStr := c.Query("connection_id")
	connectionID, err := strconv.ParseUint(connectionIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid connection ID"})
		return
	}

	// 升级到WebSocket连接
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("WebSocket upgrade failed: %v", err)
		return
	}
	defer conn.Close()

	// 发送连接中状态
	conn.WriteJSON(WebSocketMessage{
		Type: "connecting",
		Data: "正在建立SSH连接...",
	})

	log.Printf("User %d attempting to connect to SSH connection %d", currentUser.ID, connectionID)

	// 创建SSH会话
	session, err := h.sshService.CreateSession(uint(connectionID), currentUser.ID)
	if err != nil {
		log.Printf("Failed to create SSH session for user %d, connection %d: %v", currentUser.ID, connectionID, err)
		conn.WriteJSON(WebSocketMessage{
			Type: "error",
			Data: err.Error(),
		})
		return
	}
	defer h.sshService.CloseSession(session.SessionID)

	log.Printf("SSH session created successfully: %s", session.SessionID)

	// 启动Shell
	if err := session.StartShell(); err != nil {
		log.Printf("Failed to start shell: %v", err)
		conn.WriteJSON(WebSocketMessage{
			Type: "error",
			Data: "启动Shell失败: " + err.Error(),
		})
		return
	}

	// 发送连接成功消息
	conn.WriteJSON(WebSocketMessage{
		Type:    "connected",
		Data:    "SSH连接已建立",
		Session: session.SessionID,
	})

	// 启动输出读取协程
	go h.handleSSHOutput(conn, session)

	// 处理WebSocket消息
	h.handleWebSocketMessages(conn, session)
}

// handleSSHOutput 处理SSH输出
func (h *SSHHandler) handleSSHOutput(conn *websocket.Conn, session *services.SSHSessionManager) {
	buffer := make([]byte, 1024)

	// 处理stdout
	go func() {
		for {
			n, err := session.StdoutPipe.Read(buffer)
			if err != nil {
				if err != io.EOF {
					log.Printf("Read stdout error: %v", err)
				}
				break
			}

			if n > 0 {
				h.sshService.UpdateSessionActivity(session.SessionID)
				conn.WriteJSON(WebSocketMessage{
					Type: "output",
					Data: string(buffer[:n]),
				})
			}
		}
	}()

	// 处理stderr
	go func() {
		for {
			n, err := session.StderrPipe.Read(buffer)
			if err != nil {
				if err != io.EOF {
					log.Printf("Read stderr error: %v", err)
				}
				break
			}

			if n > 0 {
				h.sshService.UpdateSessionActivity(session.SessionID)
				conn.WriteJSON(WebSocketMessage{
					Type: "output",
					Data: string(buffer[:n]),
				})
			}
		}
	}()
}

// handleWebSocketMessages 处理WebSocket消息
func (h *SSHHandler) handleWebSocketMessages(conn *websocket.Conn, session *services.SSHSessionManager) {
	for {
		var msg WebSocketMessage
		err := conn.ReadJSON(&msg)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket error: %v", err)
			}
			break
		}

		h.sshService.UpdateSessionActivity(session.SessionID)

		switch msg.Type {
		case "input":
			// 处理用户输入
			if input, ok := msg.Data.(string); ok {
				session.StdinPipe.Write([]byte(input))
			}

		case "resize":
			// 处理终端大小调整
			if resizeData, ok := msg.Data.(map[string]interface{}); ok {
				if width, ok := resizeData["width"].(float64); ok {
					if height, ok := resizeData["height"].(float64); ok {
						session.ResizeTerminal(int(width), int(height))
					}
				}
			}

		case "ping":
			// 处理心跳
			conn.WriteJSON(WebSocketMessage{
				Type: "pong",
				Data: time.Now().Unix(),
			})
		}
	}
}
