package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type TicketHandler struct {
	db *gorm.DB
}

func NewTicketHandler(db *gorm.DB) *TicketHandler {
	return &TicketHandler{db: db}
}

// CreateTicketRequest 创建工单请求结构
type CreateTicketRequest struct {
	Title       string            `json:"title" binding:"required,min=1,max=255"`
	Description string            `json:"description"`
	Type        models.TicketType `json:"type" binding:"required"`
	// Priority 优先级由管理员分配时设置，创建时不需要
}

// UpdateStatusRequest 更新状态请求结构
type UpdateStatusRequest struct {
	Status       models.TicketStatus   `json:"status" binding:"required"`
	Priority     models.TicketPriority `json:"priority"`
	RejectReason string                `json:"reject_reason"`
	Feedback     string                `json:"feedback"`
	AssigneeIDs  []uint                `json:"assignee_ids"`
}

// TransferTicketRequest 转交工单请求结构
type TransferTicketRequest struct {
	ToUserID uint   `json:"to_user_id" binding:"required"`
	Reason   string `json:"reason"`
}

// CreateTicket 创建工单
func (h *TicketHandler) CreateTicket(c *gin.Context) {
	var req CreateTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	userID, _ := c.Get("user_id")

	ticket := models.Ticket{
		Title:       req.Title,
		Description: req.Description,
		Type:        req.Type,
		Priority:    models.PriorityDefault, // 新创建的工单默认为"默认"优先级
		Status:      models.StatusPending,
		CreatorID:   userID.(uint),
	}

	if err := h.db.Create(&ticket).Error; err != nil {
		utils.InternalServerError(c, "创建工单失败")
		return
	}

	// 预加载创建者信息
	h.db.Preload("Creator").First(&ticket, ticket.ID)

	utils.SuccessWithMessage(c, "工单创建成功", ticket)
}

// GetTickets 获取工单列表
func (h *TicketHandler) GetTickets(c *gin.Context) {
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var tickets []models.Ticket
	query := h.db.Preload("Creator").Preload("Comments.User")

	// 基础权限过滤
	switch currentUser.Role {
	case models.RoleAdmin:
		// 管理员可以查看所有工单
	case models.RoleDeveloper:
		// 开发者可以查看自己创建的、被分配的、曾参与的工单
		query = query.Where("creator_id = ? OR JSON_CONTAINS(assignee_ids, ?)",
			currentUser.ID, strconv.Itoa(int(currentUser.ID)))
	case models.RoleUser:
		// 普通用户只能查看自己创建的工单
		query = query.Where("creator_id = ?", currentUser.ID)
	}

	// 条件查询
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if ticketType := c.Query("type"); ticketType != "" {
		query = query.Where("type = ?", ticketType)
	}

	if priority := c.Query("priority"); priority != "" {
		query = query.Where("priority = ?", priority)
	}

	if creatorID := c.Query("creator_id"); creatorID != "" {
		query = query.Where("creator_id = ?", creatorID)
	}

	if title := c.Query("title"); title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	// 时间范围查询
	if startDate := c.Query("start_date"); startDate != "" {
		query = query.Where("created_at >= ?", startDate)
	}

	if endDate := c.Query("end_date"); endDate != "" {
		query = query.Where("created_at <= ?", endDate)
	}

	// 按时间倒序排序
	query = query.Order("created_at DESC")

	if err := query.Find(&tickets).Error; err != nil {
		utils.InternalServerError(c, "获取工单列表失败")
		return
	}

	utils.Success(c, tickets)
}

// GetTicket 获取单个工单详情
func (h *TicketHandler) GetTicket(c *gin.Context) {
	ticketID := c.Param("id")
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var ticket models.Ticket
	if err := h.db.Preload("Creator").Preload("Comments.User").
		Preload("Transfers.FromUser").Preload("Transfers.ToUser").
		First(&ticket, ticketID).Error; err != nil {
		utils.NotFound(c, "工单不存在")
		return
	}

	// 检查权限
	if !currentUser.HasPermission("view_ticket", &ticket) {
		utils.Forbidden(c, "无权限查看此工单")
		return
	}

	utils.Success(c, ticket)
}

// UpdateTicketStatus 更新工单状态
func (h *TicketHandler) UpdateTicketStatus(c *gin.Context) {
	ticketID := c.Param("id")
	var req UpdateStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var ticket models.Ticket
	if err := h.db.First(&ticket, ticketID).Error; err != nil {
		utils.NotFound(c, "工单不存在")
		return
	}

	// 检查权限
	if !currentUser.HasPermission("update_status", &ticket) {
		utils.Forbidden(c, "无权限更新此工单")
		return
	}

	// 检查状态转换是否合法
	if !ticket.CanTransitionTo(req.Status, currentUser.Role) {
		utils.BadRequest(c, "无效的状态转换")
		return
	}

	// 更新工单
	updates := map[string]interface{}{
		"status": req.Status,
	}

	if req.Status == models.StatusRejected && req.RejectReason != "" {
		updates["reject_reason"] = req.RejectReason
	}

	if req.Status == models.StatusReturned && req.Feedback != "" {
		updates["feedback"] = req.Feedback
	}

	// 需要分配开发者的状态：已分配、开发中、已提交
	statusesNeedingAssignee := []models.TicketStatus{
		models.StatusAssigned,
		models.StatusInProgress,
		models.StatusSubmitted,
	}

	// 检查是否需要分配开发者
	needsAssignee := false
	for _, status := range statusesNeedingAssignee {
		if req.Status == status {
			needsAssignee = true
			break
		}
	}

	if needsAssignee {
		// 开发者在"已分配"状态时不能再分配开发者，只有管理员可以
		if req.Status == models.StatusAssigned && currentUser.Role == models.RoleDeveloper {
			utils.BadRequest(c, "开发者无法在'已分配'状态下重新分配工单，只有管理员可以分配开发者")
			return
		}

		// 开发者更新状态到"开发中"时，自动分配给自己，不需要手动选择
		if req.Status == models.StatusInProgress && currentUser.Role == models.RoleDeveloper {
			// 自动分配给当前开发者
			updates["assignee_ids"] = models.UintArray([]uint{currentUser.ID})
		} else {
			// 其他情况需要手动分配开发者
			if len(req.AssigneeIDs) == 0 {
				utils.BadRequest(c, "此状态需要选择分配者")
				return
			}
			updates["assignee_ids"] = models.UintArray(req.AssigneeIDs)
		}
	}

	if err := h.db.Model(&ticket).Updates(updates).Error; err != nil {
		utils.InternalServerError(c, "更新工单失败")
		return
	}

	// 重新加载工单数据
	h.db.Preload("Creator").First(&ticket, ticket.ID)

	utils.SuccessWithMessage(c, "工单状态更新成功", ticket)
}

// TransferTicket 转交工单
func (h *TicketHandler) TransferTicket(c *gin.Context) {
	ticketID := c.Param("id")
	var req TransferTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	var ticket models.Ticket
	if err := h.db.First(&ticket, ticketID).Error; err != nil {
		utils.NotFound(c, "工单不存在")
		return
	}

	// 检查权限
	if !currentUser.HasPermission("transfer", &ticket) {
		utils.Forbidden(c, "无权限转交此工单")
		return
	}

	// 检查不能流转给自己
	if req.ToUserID == currentUser.ID {
		utils.BadRequest(c, "不能将工单转交给自己")
		return
	}

	// 检查目标用户是否存在且为开发者
	var toUser models.User
	if err := h.db.Where("id = ? AND role = ? AND is_active = ?",
		req.ToUserID, models.RoleDeveloper, true).First(&toUser).Error; err != nil {
		utils.BadRequest(c, "目标用户不存在或不是开发者")
		return
	}

	// 开始事务
	tx := h.db.Begin()

	// 添加新的分配者
	ticket.AddAssignee(req.ToUserID)

	// 更新工单状态为已流转
	if err := tx.Model(&ticket).Updates(map[string]interface{}{
		"status":       models.StatusTransferred,
		"assignee_ids": ticket.AssigneeIDs,
	}).Error; err != nil {
		tx.Rollback()
		utils.InternalServerError(c, "更新工单失败")
		return
	}

	// 创建流转记录
	transfer := models.TicketTransfer{
		TicketID:   ticket.ID,
		FromUserID: currentUser.ID,
		ToUserID:   req.ToUserID,
		Reason:     req.Reason,
	}

	if err := tx.Create(&transfer).Error; err != nil {
		tx.Rollback()
		utils.InternalServerError(c, "创建转交记录失败")
		return
	}

	tx.Commit()

	// 重新加载工单数据
	h.db.Preload("Creator").Preload("Transfers.FromUser").Preload("Transfers.ToUser").
		First(&ticket, ticket.ID)

	utils.SuccessWithMessage(c, "工单转交成功", ticket)
}

// UpdateTicketPriorityRequest 更新工单优先级请求结构
type UpdateTicketPriorityRequest struct {
	Priority models.TicketPriority `json:"priority" binding:"required"`
}

// UpdateTicketPriority 更新工单优先级（仅管理员）
func (h *TicketHandler) UpdateTicketPriority(c *gin.Context) {
	ticketID := c.Param("id")

	var req UpdateTicketPriorityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 只有管理员可以更新优先级
	if currentUser.Role != models.RoleAdmin {
		utils.Forbidden(c, "Only admin can update ticket priority")
		return
	}

	var ticket models.Ticket
	if err := h.db.First(&ticket, ticketID).Error; err != nil {
		utils.NotFound(c, "Ticket not found")
		return
	}

	// 验证优先级是否有效
	validPriorities := []models.TicketPriority{
		models.PriorityP0, models.PriorityP1, models.PriorityP2, models.PriorityP3,
		models.PriorityP4, models.PriorityP5, models.PriorityP6, models.PriorityDefault, models.PriorityP7,
	}

	priorityValid := false
	for _, priority := range validPriorities {
		if req.Priority == priority {
			priorityValid = true
			break
		}
	}

	if !priorityValid {
		utils.BadRequest(c, "无效的优先级，必须是P0-P7或default")
		return
	}

	// 更新优先级
	if err := h.db.Model(&ticket).Update("priority", req.Priority).Error; err != nil {
		utils.InternalServerError(c, "更新工单优先级失败")
		return
	}

	// 重新加载工单数据
	h.db.Preload("Creator").First(&ticket, ticket.ID)

	utils.SuccessWithMessage(c, "工单优先级更新成功", ticket)
}
