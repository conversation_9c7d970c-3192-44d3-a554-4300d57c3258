# 工单详情页面功能测试脚本

$baseUrl = "http://localhost:8081/api"

Write-Host "=== 工单详情页面功能测试 ===" -ForegroundColor Green

# 1. 管理员登录
Write-Host "`n1. 管理员登录..." -ForegroundColor Yellow
$loginResult = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body '{"username":"admin","password":"admin123456"}'
$adminToken = $loginResult.data.token
$headers = @{
    "Authorization" = "Bearer $adminToken"
    "Content-Type" = "application/json"
}
Write-Host "✓ 管理员登录成功" -ForegroundColor Green

# 2. 创建测试工单
Write-Host "`n2. 创建测试工单..." -ForegroundColor Yellow
$ticketData = @{
    title = "工单详情页面测试"
    description = "这是一个用于测试工单详情页面功能的测试工单。`n`n包含多行描述内容，用于验证页面显示效果。"
    type = "requirement"
    priority = "high"
} | ConvertTo-Json

$ticket = Invoke-RestMethod -Uri "$baseUrl/tickets" -Method POST -Headers $headers -Body $ticketData
$ticketId = $ticket.data.id
Write-Host "✓ 测试工单创建成功，ID: $ticketId" -ForegroundColor Green

# 3. 添加评论
Write-Host "`n3. 添加评论..." -ForegroundColor Yellow
$commentData = @{
    ticket_id = $ticketId
    content = "这是第一条测试评论。`n`n用于验证评论功能是否正常工作。"
} | ConvertTo-Json

$comment1 = Invoke-RestMethod -Uri "$baseUrl/comments" -Method POST -Headers $headers -Body $commentData
Write-Host "✓ 第一条评论添加成功" -ForegroundColor Green

# 添加第二条评论
$commentData2 = @{
    ticket_id = $ticketId
    content = "这是第二条测试评论，用于测试多条评论的显示效果。"
} | ConvertTo-Json

$comment2 = Invoke-RestMethod -Uri "$baseUrl/comments" -Method POST -Headers $headers -Body $commentData2
Write-Host "✓ 第二条评论添加成功" -ForegroundColor Green

# 4. 注册开发者用户
Write-Host "`n4. 注册开发者用户..." -ForegroundColor Yellow
try {
    $devRegisterData = @{
        username = "testdev001"
        email = "<EMAIL>"
        password = "testdev123"
        invite_code = "DEV2024DEMO"
    } | ConvertTo-Json

    $devRegister = Invoke-RestMethod -Uri "$baseUrl/register" -Method POST -ContentType "application/json" -Body $devRegisterData
    Write-Host "✓ 开发者用户注册成功" -ForegroundColor Green
} catch {
    Write-Host "! 开发者用户可能已存在，继续测试..." -ForegroundColor Yellow
}

# 5. 开发者登录
Write-Host "`n5. 开发者登录..." -ForegroundColor Yellow
$devLoginResult = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body '{"username":"testdev001","password":"testdev123"}'
$devToken = $devLoginResult.data.token
$devHeaders = @{
    "Authorization" = "Bearer $devToken"
    "Content-Type" = "application/json"
}
Write-Host "✓ 开发者登录成功" -ForegroundColor Green

# 6. 管理员分配工单给开发者
Write-Host "`n6. 管理员分配工单给开发者..." -ForegroundColor Yellow
$assignData = @{
    status = 2
    assignee_ids = @($devLoginResult.data.user_id)
} | ConvertTo-Json

$assignResult = Invoke-RestMethod -Uri "$baseUrl/tickets/$ticketId/status" -Method PUT -Headers $headers -Body $assignData
Write-Host "✓ 工单分配成功，状态更新为：已分配" -ForegroundColor Green

# 7. 开发者更新状态为开发中
Write-Host "`n7. 开发者更新状态为开发中..." -ForegroundColor Yellow
$devUpdateData = @{
    status = 3
} | ConvertTo-Json

$devUpdateResult = Invoke-RestMethod -Uri "$baseUrl/tickets/$ticketId/status" -Method PUT -Headers $devHeaders -Body $devUpdateData
Write-Host "✓ 状态更新为：开发中" -ForegroundColor Green

# 8. 开发者添加评论
Write-Host "`n8. 开发者添加评论..." -ForegroundColor Yellow
$devCommentData = @{
    ticket_id = $ticketId
    content = "我是开发者，已开始处理这个工单。预计明天完成。"
} | ConvertTo-Json

$devComment = Invoke-RestMethod -Uri "$baseUrl/comments" -Method POST -Headers $devHeaders -Body $devCommentData
Write-Host "✓ 开发者评论添加成功" -ForegroundColor Green

# 9. 获取完整的工单详情
Write-Host "`n9. 获取工单详情..." -ForegroundColor Yellow
$ticketDetail = Invoke-RestMethod -Uri "$baseUrl/tickets/$ticketId" -Method GET -Headers $headers
Write-Host "✓ 工单详情获取成功" -ForegroundColor Green
Write-Host "  标题: $($ticketDetail.data.title)" -ForegroundColor Cyan
Write-Host "  状态: $($ticketDetail.data.status) (开发中)" -ForegroundColor Cyan
Write-Host "  类型: $($ticketDetail.data.type)" -ForegroundColor Cyan
Write-Host "  优先级: $($ticketDetail.data.priority)" -ForegroundColor Cyan

# 10. 获取工单评论
Write-Host "`n10. 获取工单评论..." -ForegroundColor Yellow
$comments = Invoke-RestMethod -Uri "$baseUrl/comments/ticket/$ticketId" -Method GET -Headers $headers
Write-Host "✓ 评论获取成功，共 $($comments.data.Count) 条评论" -ForegroundColor Green

foreach ($comment in $comments.data) {
    Write-Host "  - $($comment.user.username): $($comment.content.Substring(0, [Math]::Min(30, $comment.content.Length)))..." -ForegroundColor Cyan
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "现在可以访问以下地址查看工单详情页面：" -ForegroundColor Yellow
Write-Host "  前端地址: http://localhost:5173/tickets/$ticketId" -ForegroundColor Cyan
Write-Host "  请使用以下账号登录：" -ForegroundColor Yellow
Write-Host "  管理员: admin / admin123456" -ForegroundColor Cyan
Write-Host "  开发者: testdev001 / testdev123" -ForegroundColor Cyan
