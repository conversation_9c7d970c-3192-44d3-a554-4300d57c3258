# ReqFlow - 需求与缺陷管理系统

一个支持多角色协作的需求与缺陷管理系统，实现工单的提交、审核、分配、开发、流转、评论与闭环管理。

## 功能特性

### 角色权限
- **管理员 (Admin)**: 系统初始化创建，可审核工单、分配开发者、管理用户
- **开发者 (Developer)**: 使用邀请码注册，可接收任务、更新状态、转交工单
- **普通用户 (User)**: 使用邀请码注册，可提交工单、查看自己的工单

### 工单状态流转
- 0: 已拒绝 - 管理员审核不通过
- 1: 审核中 - 用户提交工单后的初始状态
- 2: 已分配 - 管理员审核通过并分配开发者
- 3: 开发中 - 开发者开始处理任务
- 4: 已流转 - 开发者转交任务给其他人
- 5: 已提交 - 开发完成，等待管理员验收
- 6: 已退回 - 管理员验收未通过
- 7: 已完成 - 管理员确认功能通过

### 核心功能
- 工单管理：创建、查看、更新状态、转交
- 评论系统：支持文字和图片评论
- 邀请码机制：通过邀请码控制用户注册和团队归属
- 权限控制：基于角色的细粒度权限管理
- 流转日志：记录工单转交历史

## 技术栈

### 后端
- **语言**: Go (Golang)
- **框架**: Gin
- **数据库**: MySQL
- **ORM**: GORM
- **认证**: JWT

### 前端
- **构建工具**: Vite
- **框架**: Vue 3 (Composition API)
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router

## 快速开始

### 环境要求
- Go 1.21+
- Node.js 18+
- MySQL 8.0+

### 数据库配置
1. 创建数据库：
```sql
CREATE DATABASE req_flow CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改数据库连接配置（如需要）：
```go
// config/database.go
Host:     "localhost",
Port:     "3306",
User:     "root",
Password: "123456",
DBName:   "req_flow",
```

### 启动后端服务
```bash
# 安装依赖
go mod tidy

# 启动服务
go run main.go
```

后端服务将在 http://localhost:8081 启动

### 启动前端服务
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 http://localhost:5173 启动

### 功能测试
运行测试脚本验证系统功能：
```bash
# 基础功能测试
powershell -ExecutionPolicy Bypass -File simple_test.ps1

# 完整功能测试
powershell -ExecutionPolicy Bypass -File test_simple.ps1
```

## 默认账号

系统会自动创建默认管理员账号：
- **用户名**: admin
- **密码**: admin123456

演示邀请码：
- **开发者邀请码**: DEV2024DEMO
- **普通用户邀请码**: USER2024DEMO

## 打包部署

### 环境配置

项目支持测试环境和正式环境两种配置：

#### 测试环境
- **数据库**: `localhost:3306/req_flow`
- **后端端口**: 8081
- **前端端口**: 5173

#### 正式环境
- **数据库**: `rm-bp164h3vigb384mg0no.rwlb.rds.aliyuncs.com:3306/req_flow`
- **后端端口**: 8081
- **环境变量**: `GO_ENV=production`

### 打包命令

#### 后端打包

**本地开发环境**:
```bash
# Windows
go build -o reqflow-dev.exe main.go

# Linux
go build -o reqflow-dev main.go
```

**云效流水线构建命令**:

```bash
#!/bin/bash
# 设置 GOPROXY 环境变量
export GOPROXY=https://goproxy.cn,direct
export GO111MODULE=on

# 下载依赖
go mod download

# 构建 Go 项目（不区分环境，统一构建）
go build -o reqFlow main.go

# 删除除了 reqFlow 之外的所有文件
find . -maxdepth 1 -type f -not -name 'reqFlow' -delete
```

#### 前端打包

**测试环境**:
```bash
cd frontend
npm run build:dev
# 输出目录: frontend/dist/
```

**正式环境**:
```bash
cd frontend
npm run build:prod
# 输出目录: frontend/dist/
```

### 运行命令

#### 测试环境
```bash
# 后端
go run main.go
# 或
./reqflow-dev

# 前端
cd frontend
npm run dev
```

#### 正式环境
```bash
# 后端
$env:GO_ENV="production"; go run main.go
# 或
$env:GO_ENV="production"; ./reqflow-prod

# 前端 (构建后部署到Web服务器)
# 将 frontend/dist/ 目录部署到 Nginx/Apache 等
```

### 部署说明

**构建和部署流程**：
1. **构建阶段**：使用统一的构建命令，不区分环境
2. **部署阶段**：通过 `GO_ENV` 环境变量区分测试和正式环境

**环境区别**：
- **测试环境**：`GO_ENV=development` → 连接本地数据库
- **正式环境**：`GO_ENV=production` → 连接阿里云RDS数据库

**部署步骤**：
1. **后端部署**：
   - 云效自动构建生成 `reqFlow` 二进制文件
   - 根据目标环境设置对应的 `GO_ENV` 值
   - 部署脚本会自动停止旧进程并启动新进程

2. **前端部署**：
   - 执行对应环境的构建命令
   - 将 `frontend/dist/` 目录内容部署到Web服务器

3. **数据库**：
   - 程序启动时会根据 `GO_ENV` 自动选择数据库配置
   - 自动创建表结构和初始数据

### 云效流水线部署脚本

**测试环境部署**:
```bash
#!/bin/bash
# 停止现有进程
pid=$(ps aux | grep reqFlow | grep -v grep | awk '{print $2}')

if [ -n "$pid" ]; then
  echo "Terminating previous process: $pid"
  kill $pid
fi

# 解压新的文件
echo "Extracting new files..."
tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/app/

# 设置测试环境变量并启动程序
echo "Starting new program..."
export GO_ENV=development
nohup /home/<USER>/app/reqFlow > output.log 2>&1 &
```

**正式环境部署**:
```bash
#!/bin/bash
# 停止现有进程
pid=$(ps aux | grep reqFlow | grep -v grep | awk '{print $2}')

if [ -n "$pid" ]; then
  echo "Terminating previous process: $pid"
  kill $pid
fi

# 解压新的文件
echo "Extracting new files..."
tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/app/

# 设置正式环境变量并启动程序
echo "Starting new program..."
export GO_ENV=production
nohup /home/<USER>/app/reqFlow > output.log 2>&1 &
```

## API 文档

### 认证相关
- `POST /api/register` - 用户注册
- `POST /api/login` - 用户登录
- `GET /api/profile` - 获取用户信息

### 工单相关
- `POST /api/tickets` - 创建工单
- `GET /api/tickets` - 获取工单列表
- `GET /api/tickets/:id` - 获取工单详情
- `PUT /api/tickets/:id/status` - 更新工单状态
- `POST /api/tickets/:id/transfer` - 转交工单

### 评论相关
- `POST /api/comments` - 创建评论
- `GET /api/tickets/:ticket_id/comments` - 获取工单评论

### 管理员功能
- `GET /api/admin/users` - 获取用户列表
- `PUT /api/admin/users/:id/toggle` - 启用/禁用用户
- `POST /api/admin/invite-codes` - 创建邀请码
- `GET /api/admin/invite-codes` - 获取邀请码列表

## 项目结构

```
ReqFlow/
├── backend/
│   ├── config/          # 配置文件
│   ├── handlers/        # 请求处理器
│   ├── middleware/      # 中间件
│   ├── migrations/      # 数据库迁移
│   ├── models/          # 数据模型
│   ├── utils/           # 工具函数
│   └── main.go          # 主程序入口
├── frontend/
│   ├── src/
│   │   ├── api/         # API接口
│   │   ├── components/  # 组件
│   │   ├── router/      # 路由配置
│   │   ├── stores/      # 状态管理
│   │   ├── views/       # 页面组件
│   │   └── main.ts      # 前端入口
│   ├── package.json
│   └── vite.config.ts
├── go.mod
└── README.md
```

## 开发计划

- [x] 基础架构搭建
- [x] 用户认证系统
- [x] 工单基础功能
- [x] 权限控制系统
- [x] 完整的前端界面
  - [x] 登录注册页面
  - [x] 仪表板页面
  - [x] 工单列表页面
  - [x] 工单详情页面
  - [x] 创建工单页面
- [x] 评论系统
- [x] 状态流转功能
- [x] 工单转交功能
- [ ] 文件上传功能
- [ ] 邮件通知
- [ ] 数据统计报表
- [ ] 移动端适配

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
