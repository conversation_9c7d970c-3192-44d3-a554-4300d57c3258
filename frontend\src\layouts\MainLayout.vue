<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <div class="header-left">
        <h1 class="logo">ReqFlow</h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-avatar :size="32" :src="userStore.user?.avatar">
              {{ userStore.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="username">{{ userStore.user?.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人资料</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container>
      <!-- 侧边栏 -->
      <el-aside class="main-aside" width="200px">
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          router
          unique-opened
        >
          <el-menu-item index="/dashboard">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </el-menu-item>
          
          <el-menu-item index="/tickets">
            <el-icon><Document /></el-icon>
            <span>工单管理</span>
          </el-menu-item>

          <el-sub-menu
            v-if="userStore.isDeveloper()"
            index="/remote"
          >
            <template #title>
              <el-icon><Monitor /></el-icon>
              <span>远程管理</span>
            </template>
            <el-menu-item index="/remote/xshell">
              <el-icon><Monitor /></el-icon>
              <span>XShell终端</span>
            </el-menu-item>
            <el-menu-item index="/remote/xftp">
              <el-icon><FolderOpened /></el-icon>
              <span>XFTP文件</span>
            </el-menu-item>
            <el-menu-item index="/remote/redis">
              <el-icon><Coin /></el-icon>
              <span>Redis客户端</span>
            </el-menu-item>
          </el-sub-menu>

          <el-menu-item
            v-if="userStore.isAdmin()"
            index="/users"
          >
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </el-menu-item>

          <el-menu-item
            v-if="userStore.isAdmin()"
            index="/teams"
          >
            <el-icon><UserFilled /></el-icon>
            <span>团队管理</span>
          </el-menu-item>

          <el-menu-item
            v-if="userStore.isAdmin()"
            index="/admin"
          >
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区域 -->
      <el-main class="main-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowDown,
  House,
  Document,
  User,
  UserFilled,
  Setting,
  Monitor,
  FolderOpened,
  Coin
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.startsWith('/tickets')) {
    return '/tickets'
  }
  return path
})

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '确认退出',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        userStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消退出
      }
      break
  }
}
</script>

<style scoped>
.main-layout {
  height: 100vh;
  overflow: hidden; /* 禁用布局容器滚动 */
}

.main-header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left .logo {
  margin: 0;
  color: #409eff;
  font-size: 24px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}

.main-aside {
  background: #fff;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.main-content {
  background: #f5f7fa;
  padding: 0;
  height: calc(100vh - 60px); /* 减去header高度 */
  overflow: hidden;
  overflow: hidden; /* 禁用主内容区域滚动 */
}

.content-wrapper {
  height: calc(100vh - 60px); /* 减去header高度 */
  padding: 20px;
  overflow-y: auto;
  box-sizing: border-box;
}

/* 为XShell页面特殊处理 - 通过类名控制 */
.content-wrapper.no-scroll {
  overflow: hidden;
  overflow-y: auto; /* 只在内容包装器中启用滚动 */
  box-sizing: border-box;
}
</style>
