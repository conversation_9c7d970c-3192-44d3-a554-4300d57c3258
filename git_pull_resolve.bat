@echo off
echo Git Pull and Conflict Resolution Script
echo =======================================

echo Step 1: Checking current status...
git status

echo.
echo Step 2: Fetching latest changes from remote...
git fetch origin

echo.
echo Step 3: Attempting to pull and merge...
git pull origin main

echo.
echo Step 4: Checking for merge conflicts...
for /f %%i in ('git diff --name-only --diff-filter=U 2^>nul') do (
    echo Resolving conflict in: %%i
    git checkout --theirs "%%i"
    git add "%%i"
)

echo.
echo Step 5: Completing merge if needed...
git status | findstr "All conflicts fixed" >nul 2>&1
if not errorlevel 1 (
    git commit -m "Resolve merge conflicts using remote version"
)

echo.
echo Step 6: Final status...
git status

echo.
echo Git pull completed successfully!
pause