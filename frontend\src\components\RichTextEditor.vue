<template>
  <div class="rich-text-editor">
    <div class="editor-toolbar">
      <el-button-group size="small">
        <el-button 
          @mousedown.prevent="applyFormat('bold')" 
          title="粗体" 
          :class="{ active: formatStates.bold }"
        >
          <strong>B</strong>
        </el-button>
        <el-button 
          @mousedown.prevent="applyFormat('italic')" 
          title="斜体" 
          :class="{ active: formatStates.italic }"
        >
          <em>I</em>
        </el-button>
        <el-button 
          @mousedown.prevent="applyFormat('underline')" 
          title="下划线" 
          :class="{ active: formatStates.underline }"
        >
          <u>U</u>
        </el-button>
        <el-button @mousedown.prevent="applyFormat('insertUnorderedList')" title="无序列表">
          • 
        </el-button>
        <el-button @mousedown.prevent="applyFormat('insertOrderedList')" title="有序列表">
          1.
        </el-button>
        <el-button @mousedown.prevent="insertLink" title="插入链接">
          🔗
        </el-button>
        <el-button @mousedown.prevent="triggerImageUpload" title="上传图片">
          🖼️
        </el-button>
      </el-button-group>
      
      <!-- 隐藏的文件上传输入框 -->
      <input 
        ref="fileInputRef" 
        type="file" 
        accept="image/*" 
        @change="handleImageUpload" 
        style="display: none;"
      />
      
      <div class="toolbar-right">
        <el-button size="small" @mousedown.prevent="clearFormat" title="清除格式">
          清除格式
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <div 
        ref="editorRef"
        class="wysiwyg-editor"
        contenteditable="true"
        :style="{ minHeight: rows * 24 + 'px' }"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @keyup="checkFormatStates"
        @mouseup="checkFormatStates"
        @click="checkFormatStates"
      ></div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'

interface Props {
  modelValue: string
  placeholder?: string
  rows?: number
  maxlength?: number
  showWordLimit?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'focus', event: FocusEvent): void
  (e: 'blur', event: FocusEvent): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请输入内容',
  rows: 6,
  maxlength: 2000,
  showWordLimit: true
})

const emit = defineEmits<Emits>()

const editorRef = ref<HTMLDivElement>()
const fileInputRef = ref<HTMLInputElement>()
const formatStates = ref({
  bold: false,
  italic: false,
  underline: false
})

// 初始化编辑器
onMounted(() => {
  if (editorRef.value) {
    editorRef.value.innerHTML = props.modelValue || ''
  }
})

// 监听外部内容变化
watch(() => props.modelValue, (newValue) => {
  if (editorRef.value && editorRef.value.innerHTML !== newValue) {
    editorRef.value.innerHTML = newValue || ''
  }
}, { immediate: true })

// 应用格式
const applyFormat = (command: string) => {
  if (!editorRef.value) return
  
  // 确保编辑器有焦点
  editorRef.value.focus()
  
  // 执行格式化命令
  try {
    document.execCommand(command, false, undefined)
    // 立即更新内容和格式状态
    updateContent()
    checkFormatStates()
  } catch (error) {
    console.error('Format command failed:', error)
  }
}

// 检查格式状态
const checkFormatStates = () => {
  if (!editorRef.value) return
  
  try {
    formatStates.value = {
      bold: document.queryCommandState('bold'),
      italic: document.queryCommandState('italic'),
      underline: document.queryCommandState('underline')
    }
  } catch (error) {
    console.error('检查格式状态失败:', error)
  }
}

// 更新内容
const updateContent = () => {
  if (editorRef.value) {
    emit('update:modelValue', editorRef.value.innerHTML)
  }
}

// 处理输入
const handleInput = () => {
  updateContent()
  checkFormatStates()
}

// 处理焦点
const handleFocus = (event: FocusEvent) => {
  checkFormatStates()
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  updateContent()
  emit('blur', event)
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  // 支持快捷键
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        applyFormat('bold')
        break
      case 'i':
        event.preventDefault()
        applyFormat('italic')
        break
      case 'u':
        event.preventDefault()
        applyFormat('underline')
        break
    }
  }
}

// 插入链接
const insertLink = async () => {
  if (!editorRef.value) return
  
  try {
    const { value: url } = await ElMessageBox.prompt('请输入链接地址', '插入链接', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^https?:\/\/.+/,
      inputErrorMessage: '请输入有效的链接地址'
    })
    
    if (url) {
      editorRef.value.focus()
      document.execCommand('createLink', false, url)
      updateContent()
    }
  } catch {
    // 用户取消
    editorRef.value.focus()
  }
}

// 清除格式
const clearFormat = () => {
  if (!editorRef.value) return
  
  editorRef.value.focus()
  document.execCommand('removeFormat', false, undefined)
  updateContent()
  checkFormatStates()
}

// 触发图片上传
const triggerImageUpload = () => {
  fileInputRef.value?.click()
}

// 处理图片上传
const handleImageUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请选择图片文件')
      return
    }

    // 检查文件大小 (2MB，减小限制)
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.error('图片大小不能超过2MB')
      return
    }

    // 压缩并插入图片
    compressAndInsertImage(file)
  }

  // 清空input值，允许重复选择同一文件
  target.value = ''
}

// 压缩图片并插入
const compressAndInsertImage = (file: File) => {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  const img = new Image()

  img.onload = () => {
    // 计算压缩后的尺寸，最大宽度800px
    const maxWidth = 800
    const maxHeight = 600
    let { width, height } = img

    if (width > maxWidth) {
      height = (height * maxWidth) / width
      width = maxWidth
    }

    if (height > maxHeight) {
      width = (width * maxHeight) / height
      height = maxHeight
    }

    // 设置canvas尺寸
    canvas.width = width
    canvas.height = height

    // 绘制压缩后的图片
    ctx?.drawImage(img, 0, 0, width, height)

    // 转换为Base64，质量0.7
    const compressedDataUrl = canvas.toDataURL('image/jpeg', 0.7)

    // 检查压缩后的大小
    const compressedSize = compressedDataUrl.length * 0.75 // 估算字节大小
    if (compressedSize > 500 * 1024) { // 500KB限制
      ElMessage.error('图片压缩后仍然过大，请选择更小的图片')
      return
    }

    insertImage(compressedDataUrl)
  }

  img.onerror = () => {
    ElMessage.error('图片加载失败')
  }

  // 读取文件
  const reader = new FileReader()
  reader.onload = (e) => {
    img.src = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

// 插入图片
const insertImage = (imageUrl: string) => {
  if (!editorRef.value) return
  
  editorRef.value.focus()
  
  // 创建图片HTML（不添加内联样式，让CSS控制尺寸）
  const imgHtml = `<img src="${imageUrl}" />`
  
  // 插入图片
  document.execCommand('insertHTML', false, imgHtml)
  updateContent()
}
</script>

<style scoped>
.rich-text-editor {
  width: 100%;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  flex-wrap: wrap;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-toolbar .el-button.active {
  background-color: #409eff;
  color: white;
}

.wysiwyg-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background: #fff;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  outline: none;
  transition: border-color 0.2s;
}

.wysiwyg-editor:focus {
  border-color: #409eff;
}

.wysiwyg-editor:empty:before {
  content: attr(placeholder);
  color: #c0c4cc;
  pointer-events: none;
}

.wysiwyg-editor p {
  margin: 8px 0;
}

.wysiwyg-editor ul,
.wysiwyg-editor ol {
  margin: 8px 0;
  padding-left: 24px;
}

.wysiwyg-editor li {
  margin: 4px 0;
}

.wysiwyg-editor a {
  color: #409eff;
  text-decoration: none;
}

.wysiwyg-editor a:hover {
  text-decoration: underline;
}

.editor-tips {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-right {
    justify-content: center;
    margin-top: 8px;
  }
  
  .wysiwyg-editor {
    font-size: 16px;
  }
}
</style>
