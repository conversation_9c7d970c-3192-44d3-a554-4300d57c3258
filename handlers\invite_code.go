package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type InviteCodeHandler struct {
	db *gorm.DB
}

func NewInviteCodeHandler(db *gorm.DB) *InviteCodeHandler {
	return &InviteCodeHandler{db: db}
}

// CreateInviteCodeRequest 创建邀请码请求结构
type CreateInviteCodeRequest struct {
	Code     string          `json:"code" binding:"required,min=3,max=50"`
	RoleType models.UserRole `json:"role_type" binding:"required"`
	TeamName string          `json:"team_name" binding:"required,max=100"`
	MaxUses  int             `json:"max_uses" binding:"required,min=1,max=1000"`
}

// UpdateInviteCodeRequest 更新邀请码请求结构
type UpdateInviteCodeRequest struct {
	Code     string          `json:"code" binding:"required,min=3,max=50"`
	RoleType models.UserRole `json:"role_type" binding:"required"`
	TeamName string          `json:"team_name" binding:"required,max=100"`
	MaxUses  int             `json:"max_uses" binding:"required,min=1,max=1000"`
}

// GetInviteCodes 获取邀请码列表
func (h *InviteCodeHandler) GetInviteCodes(c *gin.Context) {
	var inviteCodes []models.InviteCode
	if err := h.db.Find(&inviteCodes).Error; err != nil {
		utils.InternalServerError(c, "获取邀请码列表失败")
		return
	}

	utils.Success(c, inviteCodes)
}

// CreateInviteCode 创建邀请码（仅管理员）
func (h *InviteCodeHandler) CreateInviteCode(c *gin.Context) {
	var req CreateInviteCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 验证角色类型
	validRoles := []models.UserRole{models.RoleAdmin, models.RoleDeveloper, models.RoleUser}
	roleValid := false
	for _, role := range validRoles {
		if req.RoleType == role {
			roleValid = true
			break
		}
	}
	if !roleValid {
		utils.BadRequest(c, "无效的角色类型")
		return
	}

	// 检查邀请码是否已存在
	var existingCode models.InviteCode
	if err := h.db.Where("code = ?", req.Code).First(&existingCode).Error; err == nil {
		utils.BadRequest(c, "邀请码已存在")
		return
	}

	// 验证团队是否存在
	var team models.Team
	if err := h.db.Where("name = ?", req.TeamName).First(&team).Error; err != nil {
		utils.BadRequest(c, "团队不存在")
		return
	}

	inviteCode := models.InviteCode{
		Code:      req.Code,
		RoleType:  req.RoleType,
		TeamName:  req.TeamName,
		MaxUses:   req.MaxUses,
		UsedCount: 0,
		IsActive:  true,
	}

	if err := h.db.Create(&inviteCode).Error; err != nil {
		utils.InternalServerError(c, "创建邀请码失败")
		return
	}

	utils.SuccessWithMessage(c, "邀请码创建成功", inviteCode)
}

// UpdateInviteCode 更新邀请码
func (h *InviteCodeHandler) UpdateInviteCode(c *gin.Context) {
	inviteCodeID := c.Param("id")

	var req UpdateInviteCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	var inviteCode models.InviteCode
	if err := h.db.First(&inviteCode, inviteCodeID).Error; err != nil {
		utils.NotFound(c, "邀请码不存在")
		return
	}

	// 验证角色类型
	validRoles := []models.UserRole{models.RoleAdmin, models.RoleDeveloper, models.RoleUser}
	roleValid := false
	for _, role := range validRoles {
		if req.RoleType == role {
			roleValid = true
			break
		}
	}
	if !roleValid {
		utils.BadRequest(c, "无效的角色类型")
		return
	}

	// 检查邀请码是否被其他记录使用
	var existingCode models.InviteCode
	if err := h.db.Where("code = ? AND id != ?", req.Code, inviteCode.ID).First(&existingCode).Error; err == nil {
		utils.BadRequest(c, "邀请码已存在")
		return
	}

	// 验证团队是否存在
	var team models.Team
	if err := h.db.Where("name = ?", req.TeamName).First(&team).Error; err != nil {
		utils.BadRequest(c, "团队不存在")
		return
	}

	// 更新邀请码信息
	inviteCode.Code = req.Code
	inviteCode.RoleType = req.RoleType
	inviteCode.TeamName = req.TeamName
	inviteCode.MaxUses = req.MaxUses

	if err := h.db.Save(&inviteCode).Error; err != nil {
		utils.InternalServerError(c, "更新邀请码失败")
		return
	}

	utils.SuccessWithMessage(c, "邀请码更新成功", inviteCode)
}

// DeleteInviteCode 删除邀请码
func (h *InviteCodeHandler) DeleteInviteCode(c *gin.Context) {
	inviteCodeID := c.Param("id")

	var inviteCode models.InviteCode
	if err := h.db.First(&inviteCode, inviteCodeID).Error; err != nil {
		utils.NotFound(c, "邀请码不存在")
		return
	}

	// 检查是否有用户使用了这个邀请码
	var userCount int64
	h.db.Model(&models.User{}).Where("invite_code_id = ?", inviteCode.ID).Count(&userCount)

	if userCount > 0 {
		utils.BadRequest(c, "无法删除已被用户使用的邀请码")
		return
	}

	if err := h.db.Delete(&inviteCode).Error; err != nil {
		utils.InternalServerError(c, "删除邀请码失败")
		return
	}

	utils.SuccessWithMessage(c, "邀请码删除成功", nil)
}

// ToggleInviteCodeStatus 启用/禁用邀请码（仅管理员）
func (h *InviteCodeHandler) ToggleInviteCodeStatus(c *gin.Context) {
	codeID := c.Param("id")

	var inviteCode models.InviteCode
	if err := h.db.First(&inviteCode, codeID).Error; err != nil {
		utils.NotFound(c, "邀请码不存在")
		return
	}

	// 切换状态
	inviteCode.IsActive = !inviteCode.IsActive
	if err := h.db.Save(&inviteCode).Error; err != nil {
		utils.InternalServerError(c, "更新邀请码状态失败")
		return
	}

	status := "禁用"
	if inviteCode.IsActive {
		status = "启用"
	}

	utils.SuccessWithMessage(c, "邀请码"+status+"成功", inviteCode)
}
