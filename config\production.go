package config

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// ProductionConfig 正式环境配置
type ProductionConfig struct {
	Database DatabaseConfig
}

// GetProductionConfig 获取正式环境配置
func GetProductionConfig() *ProductionConfig {
	return &ProductionConfig{
		Database: DatabaseConfig{
			Host:     "rm-bp164h3vigb384mg0no.rwlb.rds.aliyuncs.com",
			Port:     "3306",
			User:     "root",
			Password: "Qixunetwork2023",
			DBName:   "req_flow",
			Charset:  "utf8mb4",
		},
	}
}

// InitProductionDB 初始化正式环境数据库连接
func InitProductionDB() (*gorm.DB, error) {
	config := GetProductionConfig()
	
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		config.Database.User,
		config.Database.Password,
		config.Database.Host,
		config.Database.Port,
		config.Database.DBName,
		config.Database.Charset,
	)

	// 正式环境使用更严格的日志级别
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Error), // 只记录错误日志
	})

	if err != nil {
		log.Printf("正式环境数据库连接失败: %v", err)
		return nil, err
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 正式环境连接池配置
	sqlDB.SetMaxIdleConns(10)   // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)  // 最大打开连接数
	sqlDB.SetConnMaxLifetime(0) // 连接最大生存时间

	log.Println("正式环境数据库连接成功")
	return db, nil
}
