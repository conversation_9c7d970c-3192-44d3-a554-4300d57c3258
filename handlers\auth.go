package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type AuthHandler struct {
	db *gorm.DB
}

func NewAuthHandler(db *gorm.DB) *AuthHandler {
	return &AuthHandler{db: db}
}

// RegisterRequest 注册请求结构
type RegisterRequest struct {
	Username   string `json:"username" binding:"required,min=3,max=20"`
	Name       string `json:"name" binding:"required,min=2,max=50"`
	Email      string `json:"email" binding:"required,email"`
	Password   string `json:"password" binding:"required,min=6"`
	InviteCode string `json:"invite_code" binding:"required"`
}

// LoginRequest 登录请求结构
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 验证邀请码
	var inviteCode models.InviteCode
	if err := h.db.Where("code = ? AND is_active = ?", req.InviteCode, true).First(&inviteCode).Error; err != nil {
		utils.BadRequest(c, "邀请码无效或已禁用")
		return
	}

	// 检查邀请码是否还有可用次数
	if inviteCode.UsedCount >= inviteCode.MaxUses {
		utils.BadRequest(c, "邀请码已达到最大使用次数")
		return
	}

	// 检查用户名和邮箱是否已存在
	var existingUser models.User
	if err := h.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		utils.BadRequest(c, "用户名或邮箱已存在")
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		utils.InternalServerError(c, "密码加密失败")
		return
	}

	// 根据邀请码类型确定用户角色
	userRole := inviteCode.RoleType

	// 根据邀请码的TeamName找到对应的团队
	var team models.Team
	if err := h.db.Where("name = ?", inviteCode.TeamName).First(&team).Error; err != nil {
		// 如果没有找到对应团队，分配到默认团队
		if err := h.db.Where("name = ?", "管理员团队").First(&team).Error; err != nil {
			utils.InternalServerError(c, "未找到默认团队")
			return
		}
	}

	// 创建用户
	user := models.User{
		Username:     req.Username,
		Name:         req.Name,
		Email:        req.Email,
		Password:     hashedPassword,
		Role:         userRole,
		TeamID:       team.ID,
		InviteCodeID: inviteCode.ID,
		IsActive:     true,
	}

	if err := h.db.Create(&user).Error; err != nil {
		utils.InternalServerError(c, "创建用户失败")
		return
	}

	// 更新邀请码使用次数
	inviteCode.Use()
	h.db.Save(&inviteCode)

	utils.SuccessWithMessage(c, "用户注册成功", gin.H{
		"user_id":  user.ID,
		"username": user.Username,
		"role":     user.Role,
	})
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 查找用户
	var user models.User
	if err := h.db.Where("username = ? AND is_active = ?", req.Username, true).First(&user).Error; err != nil {
		utils.Unauthorized(c, "用户名或密码错误")
		return
	}

	// 验证密码
	if !utils.CheckPassword(req.Password, user.Password) {
		utils.Unauthorized(c, "用户名或密码错误")
		return
	}

	// 生成JWT令牌
	token, err := utils.GenerateToken(user.ID, user.Username, string(user.Role))
	if err != nil {
		utils.InternalServerError(c, "生成令牌失败")
		return
	}

	utils.SuccessWithMessage(c, "登录成功", gin.H{
		"token":    token,
		"user_id":  user.ID,
		"username": user.Username,
		"role":     user.Role,
	})
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var user models.User
	if err := h.db.Preload("InviteCode").Where("id = ?", userID).First(&user).Error; err != nil {
		utils.NotFound(c, "用户不存在")
		return
	}

	teamName := ""
	if user.InviteCode != nil {
		teamName = user.InviteCode.TeamName
	}

	utils.Success(c, gin.H{
		"id":         user.ID,
		"username":   user.Username,
		"name":       user.Name,
		"email":      user.Email,
		"role":       user.Role,
		"avatar":     user.Avatar,
		"is_active":  user.IsActive,
		"team_name":  teamName,
		"created_at": user.CreatedAt,
	})
}
