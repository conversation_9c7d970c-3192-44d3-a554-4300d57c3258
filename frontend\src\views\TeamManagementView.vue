<template>
  <div class="team-management">
    <div class="page-header">
      <h2>团队管理</h2>
    </div>

    <div class="team-content">
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="团队列表" name="teams">
          <div class="tab-header">
            <el-button type="primary" @click="openCreateDialog">
              <el-icon><Plus /></el-icon>
              新建团队
            </el-button>
          </div>

          <el-table :data="teams" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="团队名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="user_count" label="成员数量" width="100">
          <template #default="{ row }">
            <el-tag>{{ row.user_count }} 人</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建者" width="120">
          <template #default="{ row }">
            {{ row.creator?.name || row.creator?.username }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="editTeam(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.is_active ? 'warning' : 'success'"
              @click="toggleTeamStatus(row)"
            >
              {{ row.is_active ? '禁用' : '启用' }}
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteTeam(row)"
              :disabled="row.user_count > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
        </el-tab-pane>

        <el-tab-pane label="邀请码管理" name="invite-codes">
          <div class="tab-header">
            <el-button type="primary" @click="showInviteCodeDialog = true">
              <el-icon><Plus /></el-icon>
              新建邀请码
            </el-button>
          </div>

          <el-table :data="inviteCodes" style="width: 100%" v-loading="inviteCodeLoading">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="code" label="邀请码" width="150">
              <template #default="{ row }">
                <el-tag style="font-family: monospace;">
                  {{ row.code }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="role_type" label="角色类型" width="120">
              <template #default="{ row }">
                <el-tag :type="getRoleTypeColor(row.role_type)">
                  {{ getRoleTypeName(row.role_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="team_name" label="分配团队" min-width="150" />
            <el-table-column prop="max_uses" label="最大使用次数" width="120" />
            <el-table-column prop="used_count" label="已使用次数" width="120">
              <template #default="{ row }">
                <el-tag :type="row.used_count >= row.max_uses ? 'danger' : 'success'">
                  {{ row.used_count }} / {{ row.max_uses }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="is_active" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="row.is_active ? 'success' : 'danger'">
                  {{ row.is_active ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="editInviteCode(row)">编辑</el-button>
                <el-button
                  size="small"
                  :type="row.is_active ? 'warning' : 'success'"
                  @click="toggleInviteCodeStatus(row)"
                >
                  {{ row.is_active ? '禁用' : '启用' }}
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteInviteCode(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 新建/编辑团队对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="isEditing ? '编辑团队' : '新建团队'"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="团队名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入团队名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入团队描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showCreateDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :loading="submitting"
          >
            {{ isEditing ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建/编辑邀请码对话框 -->
    <el-dialog
      v-model="showInviteCodeDialog"
      :title="isEditingInviteCode ? '编辑邀请码' : '新建邀请码'"
      width="500px"
      @close="handleCloseInviteCodeDialog"
    >
      <el-form
        ref="inviteCodeFormRef"
        :model="inviteCodeForm"
        :rules="inviteCodeRules"
        label-width="120px"
      >
        <el-form-item label="邀请码" prop="code">
          <el-input
            v-model="inviteCodeForm.code"
            placeholder="请输入邀请码"
            maxlength="50"
            show-word-limit
            style="font-family: monospace;"
          />
        </el-form-item>

        <el-form-item label="角色类型" prop="role_type">
          <el-select v-model="inviteCodeForm.role_type" placeholder="请选择角色类型" style="width: 100%">
            <el-option label="管理员" value="admin" />
            <el-option label="开发者" value="developer" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>

        <el-form-item label="分配团队" prop="team_name">
          <el-select v-model="inviteCodeForm.team_name" placeholder="请选择团队" style="width: 100%">
            <el-option
              v-for="team in teams"
              :key="team.id"
              :label="team.name"
              :value="team.name"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="最大使用次数" prop="max_uses">
          <el-input-number
            v-model="inviteCodeForm.max_uses"
            :min="1"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showInviteCodeDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="submitInviteCodeForm"
            :loading="inviteCodeSubmitting"
          >
            {{ isEditingInviteCode ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import * as teamApi from '@/api/team'

// 使用API中定义的类型
type Team = teamApi.Team
type CreateTeamRequest = teamApi.CreateTeamRequest
type UpdateTeamRequest = teamApi.UpdateTeamRequest

// 邀请码接口类型定义
interface InviteCode {
  id: number
  code: string
  role_type: string
  team_name: string
  max_uses: number
  used_count: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface CreateInviteCodeRequest {
  code: string
  role_type: string
  team_name: string
  max_uses: number
}

interface UpdateInviteCodeRequest {
  code: string
  role_type: string
  team_name: string
  max_uses: number
}

// 响应式数据
const activeTab = ref('teams')
const teams = ref<Team[]>([])
const loading = ref(false)
const showCreateDialog = ref(false)
const isEditing = ref(false)
const editingTeamId = ref<number | null>(null)
const submitting = ref(false)

// 邀请码相关数据
const inviteCodes = ref<InviteCode[]>([])
const inviteCodeLoading = ref(false)
const showInviteCodeDialog = ref(false)
const isEditingInviteCode = ref(false)
const editingInviteCodeId = ref<number | null>(null)
const inviteCodeSubmitting = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const form = ref<CreateTeamRequest>({
  name: '',
  description: ''
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入团队名称', trigger: 'blur' },
    { min: 2, max: 100, message: '团队名称长度应在 2 到 100 个字符之间', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 邀请码表单相关
const inviteCodeFormRef = ref<FormInstance>()
const inviteCodeForm = ref<CreateInviteCodeRequest>({
  code: '',
  role_type: 'user',
  team_name: '',
  max_uses: 100
})

// 邀请码表单验证规则
const inviteCodeRules: FormRules = {
  code: [
    { required: true, message: '请输入邀请码', trigger: 'blur' },
    { min: 3, max: 50, message: '邀请码长度应在 3 到 50 个字符之间', trigger: 'blur' }
  ],
  role_type: [
    { required: true, message: '请选择角色类型', trigger: 'change' }
  ],
  team_name: [
    { required: true, message: '请选择团队', trigger: 'change' }
  ],
  max_uses: [
    { required: true, message: '请输入最大使用次数', trigger: 'blur' }
  ]
}

onMounted(() => {
  loadTeams()
  loadInviteCodes()
})

// 加载团队列表
const loadTeams = async () => {
  loading.value = true
  try {
    const response = await teamApi.getTeams()
    teams.value = response.data || response
  } catch (error) {
    console.error('加载团队列表失败:', error)
    ElMessage.error('加载团队列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 打开新建对话框
const openCreateDialog = () => {
  showCreateDialog.value = true
}

// 编辑团队
const editTeam = (team: Team) => {
  isEditing.value = true
  editingTeamId.value = team.id
  form.value = {
    name: team.name,
    description: team.description
  }
  showCreateDialog.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value || submitting.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    // 保存当前编辑状态，避免在提交过程中状态被改变
    const currentIsEditing = isEditing.value
    const currentEditingId = editingTeamId.value

    if (currentIsEditing) {
      await teamApi.updateTeam(currentEditingId!, form.value)
      ElMessage.success('团队更新成功')
    } else {
      await teamApi.createTeam(form.value)
      ElMessage.success('团队创建成功')
    }

    // 保存成功后重新加载数据，清空表单数据，然后关闭对话框
    await loadTeams()
    // 清空表单数据，避免关闭时弹出确认对话框
    form.value = { name: '', description: '' }
    showCreateDialog.value = false
  } catch (error) {
    console.error('提交表单失败:', error)
    // 使用保存的状态来显示错误消息
    const currentIsEditing = isEditing.value
    ElMessage.error(currentIsEditing ? '团队更新失败' : '团队创建失败')
  } finally {
    submitting.value = false
  }
}

// 切换团队状态
const toggleTeamStatus = async (team: Team) => {
  try {
    const action = team.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}团队"${team.name}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await teamApi.toggleTeamStatus(team.id)
    ElMessage.success(`团队${action}成功`)
    await loadTeams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换团队状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除团队
const deleteTeam = async (team: Team) => {
  if (team.user_count > 0) {
    ElMessage.warning('该团队还有成员，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除团队"${team.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await teamApi.deleteTeam(team.id)
    ElMessage.success('团队删除成功')
    await loadTeams()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除团队失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  isEditing.value = false
  editingTeamId.value = null
  currentInviteCode.value = ''
  form.value = {
    name: '',
    description: ''
  }
  formRef.value?.clearValidate()
}

// 处理对话框关闭
const handleCloseDialog = async () => {
  // 如果有未保存的更改，显示确认对话框
  if (form.value.name || form.value.description) {
    try {
      await ElMessageBox.confirm(
        '有未保存的更改，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }

  resetForm()
}

// ==================== 邀请码管理相关方法 ====================

// 加载邀请码列表
const loadInviteCodes = async () => {
  inviteCodeLoading.value = true
  try {
    const { getInviteCodes } = await import('@/api/inviteCode')
    const response = await getInviteCodes()
    inviteCodes.value = response.data || response
  } catch (error) {
    console.error('加载邀请码列表失败:', error)
    ElMessage.error('加载邀请码列表失败')
  } finally {
    inviteCodeLoading.value = false
  }
}

// 获取角色类型名称
const getRoleTypeName = (roleType: string) => {
  const names = {
    admin: '管理员',
    developer: '开发者',
    user: '普通用户'
  }
  return names[roleType] || '普通用户'
}

// 获取角色类型颜色
const getRoleTypeColor = (roleType: string) => {
  const colors = {
    admin: 'danger',
    developer: 'warning',
    user: 'info'
  }
  return colors[roleType] || 'info'
}

// 编辑邀请码
const editInviteCode = (inviteCode: InviteCode) => {
  isEditingInviteCode.value = true
  editingInviteCodeId.value = inviteCode.id
  inviteCodeForm.value = {
    code: inviteCode.code,
    role_type: inviteCode.role_type,
    team_name: inviteCode.team_name,
    max_uses: inviteCode.max_uses
  }
  showInviteCodeDialog.value = true
}

// 提交邀请码表单
const submitInviteCodeForm = async () => {
  if (!inviteCodeFormRef.value) return

  try {
    await inviteCodeFormRef.value.validate()
    inviteCodeSubmitting.value = true

    const { createInviteCode, updateInviteCode } = await import('@/api/inviteCode')

    if (isEditingInviteCode.value) {
      await updateInviteCode(editingInviteCodeId.value!, inviteCodeForm.value)
      ElMessage.success('邀请码更新成功')
    } else {
      await createInviteCode(inviteCodeForm.value)
      ElMessage.success('邀请码创建成功')
    }

    // 保存成功后重置表单并关闭对话框
    resetInviteCodeForm()
    showInviteCodeDialog.value = false
    await loadInviteCodes()
  } catch (error) {
    console.error('提交邀请码表单失败:', error)
    ElMessage.error(isEditingInviteCode.value ? '邀请码更新失败' : '邀请码创建失败')
  } finally {
    inviteCodeSubmitting.value = false
  }
}

// 切换邀请码状态
const toggleInviteCodeStatus = async (inviteCode: InviteCode) => {
  try {
    const action = inviteCode.is_active ? '禁用' : '启用'
    await ElMessageBox.confirm(
      `确定要${action}邀请码"${inviteCode.code}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const { toggleInviteCodeStatus: toggleStatus } = await import('@/api/inviteCode')
    await toggleStatus(inviteCode.id)
    ElMessage.success(`邀请码${action}成功`)
    await loadInviteCodes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('切换邀请码状态失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 删除邀请码
const deleteInviteCode = async (inviteCode: InviteCode) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除邀请码"${inviteCode.code}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const { deleteInviteCode: deleteCode } = await import('@/api/inviteCode')
    await deleteCode(inviteCode.id)
    ElMessage.success('邀请码删除成功')
    await loadInviteCodes()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除邀请码失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 重置邀请码表单
const resetInviteCodeForm = () => {
  isEditingInviteCode.value = false
  editingInviteCodeId.value = null
  inviteCodeForm.value = {
    code: '',
    role_type: 'user',
    team_name: '',
    max_uses: 100
  }
  inviteCodeFormRef.value?.clearValidate()
}

// 处理邀请码对话框关闭
const handleCloseInviteCodeDialog = async () => {
  if (inviteCodeForm.value.code || inviteCodeForm.value.team_name) {
    try {
      await ElMessageBox.confirm(
        '有未保存的更改，确定要关闭吗？',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }

  resetInviteCodeForm()
}
</script>

<style scoped>
.team-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.team-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.tab-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.invite-code-section {
  width: 100%;
}

.invite-code-tip {
  margin-top: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
