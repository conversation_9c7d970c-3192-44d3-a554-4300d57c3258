package models

import (
	"time"
	"gorm.io/gorm"
)

// MySQLConnection MySQL连接配置
type MySQLConnection struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null;comment:连接名称"`
	Host        string    `json:"host" gorm:"not null;comment:主机地址"`
	Port        int       `json:"port" gorm:"not null;default:3306;comment:端口号"`
	Username    string    `json:"username" gorm:"not null;comment:用户名"`
	Password    string    `json:"password" gorm:"comment:密码"`
	Database    string    `json:"database" gorm:"comment:默认数据库"`
	Charset     string    `json:"charset" gorm:"default:utf8mb4;comment:字符集"`
	Description string    `json:"description" gorm:"comment:连接描述"`
	UserID      uint      `json:"user_id" gorm:"not null"`
	User        User      `json:"user,omitempty" gorm:"-"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (MySQLConnection) TableName() string {
	return "mysql_connections"
}

// MySQLDatabase 数据库信息
type MySQLDatabase struct {
	Name      string `json:"name"`
	Charset   string `json:"charset"`
	Collation string `json:"collation"`
	Size      string `json:"size"`
}

// MySQLTable 表信息
type MySQLTable struct {
	Name       string `json:"name"`
	Engine     string `json:"engine"`
	Rows       int64  `json:"rows"`
	DataLength int64  `json:"data_length"`
	Comment    string `json:"comment"`
}

// MySQLColumn 列信息
type MySQLColumn struct {
	Field   string `json:"field"`
	Type    string `json:"type"`
	Null    string `json:"null"`
	Key     string `json:"key"`
	Default string `json:"default"`
	Extra   string `json:"extra"`
	Comment string `json:"comment"`
}

// MySQLQueryResult 查询结果
type MySQLQueryResult struct {
	Columns []string        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
	Total   int64           `json:"total"`
	Message string          `json:"message,omitempty"`
}
