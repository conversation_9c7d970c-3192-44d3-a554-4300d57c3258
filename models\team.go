package models

import (
	"time"
	"gorm.io/gorm"
)

// Team 团队模型
type Team struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"not null;size:100"`
	Description string         `json:"description" gorm:"size:500"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedBy   uint           `json:"created_by" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User   `json:"creator" gorm:"foreignKey:CreatedBy"`
	Users   []User `json:"users,omitempty" gorm:"foreignKey:TeamID"`
}

// TableName 指定表名
func (Team) TableName() string {
	return "teams"
}
