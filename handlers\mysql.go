package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"ReqFlow/models"
	"ReqFlow/services"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var mysqlService = services.GetMySQLService()

// CreateMySQLConnection 创建MySQL连接
func CreateMySQLConnection(c *gin.Context) {
	var conn models.MySQLConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	conn.UserID = userID.(uint)
	fmt.Printf("DEBUG: 当前用户ID: %d\n", conn.UserID)

	// 验证用户是否存在
	db := c.MustGet("db").(*gorm.DB)
	var user models.User
	if err := db.Where("id = ?", conn.UserID).First(&user).Error; err != nil {
		fmt.Printf("DEBUG: 用户查询失败: %v\n", err)
		utils.ErrorResponse(c, http.StatusBadRequest, "用户不存在", "用户ID: "+fmt.Sprintf("%d", conn.UserID))
		return
	}
	fmt.Printf("DEBUG: 找到用户: %+v\n", user)

	// 保存到数据库
	if err := db.Create(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "创建连接失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "连接创建成功", conn)
}

// GetMySQLConnections 获取MySQL连接列表
func GetMySQLConnections(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var connections []models.MySQLConnection
	if err := db.Where("user_id = ?", userID).Find(&connections).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取连接列表成功", connections)
}

// UpdateMySQLConnection 更新MySQL连接
func UpdateMySQLConnection(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var conn models.MySQLConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var existingConn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&existingConn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	// 更新连接信息
	conn.ID = uint(id)
	conn.UserID = userID.(uint)
	if err := db.Save(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "更新连接失败", err.Error())
		return
	}

	// 关闭旧连接
	mysqlService.CloseConnection(uint(id))

	utils.SuccessResponse(c, "连接更新成功", conn)
}

// DeleteMySQLConnection 删除MySQL连接
func DeleteMySQLConnection(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	if err := db.Delete(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "删除连接失败", err.Error())
		return
	}

	// 关闭连接
	mysqlService.CloseConnection(uint(id))

	utils.SuccessResponse(c, "连接删除成功", nil)
}

// TestMySQLConnection 测试MySQL连接
func TestMySQLConnection(c *gin.Context) {
	var conn models.MySQLConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	if err := mysqlService.TestConnection(conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "连接测试失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "连接测试成功", nil)
}

// GetMySQLDatabases 获取数据库列表
func GetMySQLDatabases(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	databases, err := mysqlService.GetDatabases(uint(id), conn)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取数据库列表失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取数据库列表成功", databases)
}

// GetMySQLTables 获取表列表
func GetMySQLTables(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	database := c.Query("database")
	if database == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "数据库名称不能为空")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	tables, err := mysqlService.GetTables(uint(id), conn, database)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取表列表失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取表列表成功", tables)
}

// GetMySQLColumns 获取表结构
func GetMySQLColumns(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	database := c.Query("database")
	table := c.Query("table")
	if database == "" || table == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "数据库名称和表名称不能为空")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	columns, err := mysqlService.GetColumns(uint(id), conn, database, table)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取表结构失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取表结构成功", columns)
}

// ExecuteMySQLQuery 执行MySQL查询
func ExecuteMySQLQuery(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var req struct {
		Query string `json:"query" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.MySQLConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	result, err := mysqlService.ExecuteQuery(uint(id), conn, req.Query)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "执行查询失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "查询执行成功", result)
}
