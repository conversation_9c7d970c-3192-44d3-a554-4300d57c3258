package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"golang.org/x/crypto/ssh"
)

func main() {
	// SSH连接配置
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password("Asdty1234"),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	// 连接SSH服务器
	fmt.Println("正在连接到 **************:22...")
	client, err := ssh.Dial("tcp", "**************:22", config)
	if err != nil {
		log.Fatalf("SSH连接失败: %v", err)
	}
	defer client.Close()

	fmt.Println("SSH连接成功，开始测试服务器信息获取...")

	// 测试内存信息
	fmt.Println("\n=== 测试内存信息 ===")
	testMemoryCommands(client)

	// 测试硬盘信息
	fmt.Println("\n=== 测试硬盘信息 ===")
	testDiskCommands(client)

	// 测试CPU信息
	fmt.Println("\n=== 测试CPU信息 ===")
	testCPUCommands(client)

	// 测试系统负载
	fmt.Println("\n=== 测试系统负载 ===")
	testLoadCommands(client)
}

func executeCommand(client *ssh.Client, command string) (string, error) {
	session, err := client.NewSession()
	if err != nil {
		return "", err
	}
	defer session.Close()

	output, err := session.Output(command)
	if err != nil {
		return "", err
	}

	return string(output), nil
}

func testMemoryCommands(client *ssh.Client) {
	commands := []string{
		"free -m",
		"free -m | grep '^Mem:'",
		"free -m | grep '^Mem:' | awk '{printf \"%.1f %.1f %.1f\", $3/$2*100, $3/1024, $2/1024}'",
		"free -h | grep '^Mem:'",
	}

	for _, cmd := range commands {
		fmt.Printf("命令: %s\n", cmd)
		if output, err := executeCommand(client, cmd); err == nil {
			fmt.Printf("输出: %s\n", strings.TrimSpace(output))
		} else {
			fmt.Printf("错误: %v\n", err)
		}
		fmt.Println()
	}
}

func testDiskCommands(client *ssh.Client) {
	commands := []string{
		"df -h /",
		"df -BG /",
		"df -BG / | awk 'NR==2{print $2, $3, $5}'",
		"df -BG / | awk 'NR==2{gsub(/G/, \"\", $2); gsub(/G/, \"\", $3); gsub(/%/, \"\", $5); printf \"%.0f %.0f %.0f\", $5, $3, $2}'",
	}

	for _, cmd := range commands {
		fmt.Printf("命令: %s\n", cmd)
		if output, err := executeCommand(client, cmd); err == nil {
			fmt.Printf("输出: %s\n", strings.TrimSpace(output))
		} else {
			fmt.Printf("错误: %v\n", err)
		}
		fmt.Println()
	}
}

func testCPUCommands(client *ssh.Client) {
	commands := []string{
		"grep 'cpu ' /proc/stat",
		"grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$3+$4+$5)} END {print usage}'",
		"top -bn1 | grep 'Cpu(s)'",
	}

	for _, cmd := range commands {
		fmt.Printf("命令: %s\n", cmd)
		if output, err := executeCommand(client, cmd); err == nil {
			fmt.Printf("输出: %s\n", strings.TrimSpace(output))
		} else {
			fmt.Printf("错误: %v\n", err)
		}
		fmt.Println()
	}
}

func testLoadCommands(client *ssh.Client) {
	commands := []string{
		"uptime",
		"uptime | awk -F'load average:' '{print $2}'",
		"uptime | awk -F'load average:' '{print $2}' | sed 's/,//g' | awk '{printf \"%.2f %.2f %.2f\", $1, $2, $3}'",
	}

	for _, cmd := range commands {
		fmt.Printf("命令: %s\n", cmd)
		if output, err := executeCommand(client, cmd); err == nil {
			fmt.Printf("输出: %s\n", strings.TrimSpace(output))
		} else {
			fmt.Printf("错误: %v\n", err)
		}
		fmt.Println()
	}
}
