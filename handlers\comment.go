package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CommentHandler struct {
	db *gorm.DB
}

func NewCommentHandler(db *gorm.DB) *CommentHandler {
	return &CommentHandler{db: db}
}

// CreateCommentRequest 创建评论请求结构
type CreateCommentRequest struct {
	TicketID uint     `json:"ticket_id" binding:"required"`
	Content  string   `json:"content" binding:"required"`
	Images   []string `json:"images"`
}

// CreateComment 创建评论
func (h *CommentHandler) CreateComment(c *gin.Context) {
	var req CreateCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 检查工单是否存在
	var ticket models.Ticket
	if err := h.db.First(&ticket, req.TicketID).Error; err != nil {
		utils.NotFound(c, "工单不存在")
		return
	}

	// 检查权限
	if !currentUser.HasPermission("comment", &ticket) {
		utils.Forbidden(c, "无权限评论此工单")
		return
	}

	// 创建评论
	comment := models.Comment{
		TicketID: req.TicketID,
		UserID:   currentUser.ID,
		Content:  req.Content,
	}

	// 处理图片（这里简化处理，实际项目中需要文件上传功能）
	if len(req.Images) > 0 {
		// 将图片URL数组转换为JSON字符串存储
		// 实际项目中这里需要验证图片URL的有效性
		comment.Images = `["` + req.Images[0] + `"]` // 简化处理
	} else {
		comment.Images = "[]" // 空数组而不是空字符串
	}

	if err := h.db.Create(&comment).Error; err != nil {
		utils.InternalServerError(c, "创建评论失败")
		return
	}

	// 预加载用户信息
	h.db.Preload("User").First(&comment, comment.ID)

	utils.SuccessWithMessage(c, "评论创建成功", comment)
}

// GetComments 获取工单评论列表
func (h *CommentHandler) GetComments(c *gin.Context) {
	ticketID := c.Param("ticket_id")
	user, _ := c.Get("user")
	currentUser := user.(*models.User)

	// 检查工单是否存在
	var ticket models.Ticket
	if err := h.db.First(&ticket, ticketID).Error; err != nil {
		utils.NotFound(c, "工单不存在")
		return
	}

	// 检查权限
	if !currentUser.HasPermission("view_ticket", &ticket) {
		utils.Forbidden(c, "无权限查看此工单")
		return
	}

	var comments []models.Comment
	if err := h.db.Preload("User").Where("ticket_id = ?", ticketID).
		Order("created_at ASC").Find(&comments).Error; err != nil {
		utils.InternalServerError(c, "获取评论列表失败")
		return
	}

	utils.Success(c, comments)
}
