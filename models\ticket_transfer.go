package models

import (
	"time"

	"gorm.io/gorm"
)

// TicketTransfer 工单流转记录模型
type TicketTransfer struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	TicketID   uint           `json:"ticket_id" gorm:"not null"`
	FromUserID uint           `json:"from_user_id" gorm:"not null"`
	ToUserID   uint           `json:"to_user_id" gorm:"not null"`
	Reason     string         `json:"reason" gorm:"size:500"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Ticket   *Ticket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
	FromUser *User   `json:"from_user,omitempty" gorm:"foreignKey:FromUserID"`
	ToUser   *User   `json:"to_user,omitempty" gorm:"foreignKey:ToUserID"`
}

// TableName 指定表名
func (TicketTransfer) TableName() string {
	return "ticket_transfers"
}
