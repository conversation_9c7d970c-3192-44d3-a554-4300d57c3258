#!/bin/bash

# ReqFlow 部署脚本
# 适用于云效流水线自动部署

set -e

# 配置变量
APP_NAME="reqFlow"
APP_DIR="/home/<USER>/app"
PACKAGE_FILE="$APP_DIR/package.tgz"
LOG_FILE="$APP_DIR/output.log"
PID_FILE="$APP_DIR/reqflow.pid"

echo "========================================="
echo "ReqFlow 部署开始"
echo "时间: $(date)"
echo "应用目录: $APP_DIR"
echo "========================================="

# 创建应用目录（如果不存在）
mkdir -p "$APP_DIR"
cd "$APP_DIR"

# 函数：获取进程ID
get_pid() {
    ps aux | grep "$APP_NAME" | grep -v grep | awk '{print $2}' | head -1
}

# 函数：检查进程是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ]; then
        return 0  # 运行中
    else
        return 1  # 未运行
    fi
}

# 函数：停止应用
stop_app() {
    echo "检查现有进程..."
    local pid=$(get_pid)
    
    if [ -n "$pid" ]; then
        echo "发现运行中的进程: $pid"
        echo "正在停止进程..."
        
        # 优雅停止
        kill -TERM "$pid" 2>/dev/null || true
        
        # 等待进程停止
        local count=0
        while [ $count -lt 10 ]; do
            if ! is_running; then
                echo "✅ 进程已停止"
                break
            fi
            echo "等待进程停止... ($((count + 1))/10)"
            sleep 2
            count=$((count + 1))
        done
        
        # 如果进程仍在运行，强制杀死
        if is_running; then
            echo "⚠️  进程未响应，强制停止..."
            kill -KILL "$pid" 2>/dev/null || true
            sleep 2
        fi
        
        # 清理PID文件
        rm -f "$PID_FILE"
    else
        echo "没有发现运行中的进程"
    fi
}

# 函数：备份当前版本
backup_current() {
    if [ -f "$APP_NAME" ]; then
        local backup_name="${APP_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
        echo "备份当前版本: $backup_name"
        cp "$APP_NAME" "$backup_name"
        
        # 只保留最近5个备份
        ls -t ${APP_NAME}.backup.* 2>/dev/null | tail -n +6 | xargs rm -f 2>/dev/null || true
    fi
}

# 函数：解压新版本
extract_new() {
    if [ ! -f "$PACKAGE_FILE" ]; then
        echo "❌ 错误: 部署包不存在 $PACKAGE_FILE"
        exit 1
    fi
    
    echo "解压新版本..."
    tar zxvf "$PACKAGE_FILE"
    
    if [ ! -f "$APP_NAME" ]; then
        echo "❌ 错误: 解压后未找到可执行文件 $APP_NAME"
        exit 1
    fi
    
    # 设置执行权限
    chmod +x "$APP_NAME"
    
    echo "✅ 新版本解压完成"
    ls -lh "$APP_NAME"
}

# 函数：启动应用
start_app() {
    echo "启动新程序..."
    
    # 设置环境变量
    export GO_ENV=production
    
    # 启动应用
    nohup "./$APP_NAME" > "$LOG_FILE" 2>&1 &
    local new_pid=$!
    
    # 保存PID
    echo "$new_pid" > "$PID_FILE"
    
    echo "应用已启动，PID: $new_pid"
    
    # 等待应用启动
    echo "等待应用启动..."
    sleep 5
    
    # 检查应用是否正常运行
    if is_running; then
        echo "✅ 应用启动成功!"
        
        # 显示最近的日志
        echo "最近的日志:"
        tail -n 10 "$LOG_FILE" 2>/dev/null || echo "暂无日志"
        
        # 健康检查
        echo "执行健康检查..."
        if command -v curl >/dev/null 2>&1; then
            sleep 3
            if curl -f http://localhost:8081/health >/dev/null 2>&1; then
                echo "✅ 健康检查通过"
            else
                echo "⚠️  健康检查失败，请检查应用状态"
            fi
        else
            echo "⚠️  未安装curl，跳过健康检查"
        fi
    else
        echo "❌ 应用启动失败!"
        echo "错误日志:"
        tail -n 20 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
        exit 1
    fi
}

# 函数：清理部署包
cleanup() {
    echo "清理部署包..."
    rm -f "$PACKAGE_FILE"
}

# 主部署流程
main() {
    echo "开始部署流程..."
    
    # 1. 停止当前应用
    stop_app
    
    # 2. 备份当前版本
    backup_current
    
    # 3. 解压新版本
    extract_new
    
    # 4. 启动新应用
    start_app
    
    # 5. 清理部署包
    cleanup
    
    echo "========================================="
    echo "✅ 部署完成!"
    echo "应用名称: $APP_NAME"
    echo "应用目录: $APP_DIR"
    echo "日志文件: $LOG_FILE"
    echo "PID文件: $PID_FILE"
    echo "部署时间: $(date)"
    echo "========================================="
}

# 执行主流程
main "$@"
