# 简单的API测试脚本

Write-Host "=== ReqFlow API 测试 ===" -ForegroundColor Green

# 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
$health = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
Write-Host "健康检查结果: $($health.message)" -ForegroundColor Green

# 测试管理员登录
Write-Host "`n2. 测试管理员登录..." -ForegroundColor Yellow
$loginData = '{"username":"admin","password":"admin123456"}'
$loginResult = Invoke-RestMethod -Uri "http://localhost:8081/api/login" -Method POST -ContentType "application/json" -Body $loginData
Write-Host "登录成功，角色: $($loginResult.data.role)" -ForegroundColor Green

# 测试获取用户信息
Write-Host "`n3. 测试获取用户信息..." -ForegroundColor Yellow
$headers = @{"Authorization" = "Bearer $($loginResult.data.token)"}
$profile = Invoke-RestMethod -Uri "http://localhost:8081/api/profile" -Method GET -Headers $headers
Write-Host "用户信息: $($profile.data.username) - $($profile.data.team_name)" -ForegroundColor Green

# 测试创建工单
Write-Host "`n4. 测试创建工单..." -ForegroundColor Yellow
$ticketData = '{"title":"测试工单","description":"测试描述","type":"requirement","priority":"medium"}'
$ticket = Invoke-RestMethod -Uri "http://localhost:8081/api/tickets" -Method POST -Headers $headers -ContentType "application/json" -Body $ticketData
Write-Host "工单创建成功，ID: $($ticket.data.id)" -ForegroundColor Green

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
