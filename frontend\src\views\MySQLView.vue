<template>
  <div class="mysql-view">
    <!-- 头部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>MySQL客户端</h2>
      </div>
      <div class="toolbar-right">
        <el-button type="primary" @click="showConnectionDialog = true">
          <el-icon><Plus /></el-icon>
          新建连接
        </el-button>
        <el-button @click="loadConnections">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 连接列表 -->
    <div v-if="!activeConnection" class="connections-grid">
      <div class="connections-header">
        <h3>MySQL连接</h3>
        <p>选择一个连接开始使用MySQL客户端</p>
      </div>
      
      <div class="connections-list">
        <div 
          v-for="conn in connections" 
          :key="conn.id"
          class="connection-card"
          @click="selectConnection(conn)"
        >
          <div class="connection-info">
            <h4>{{ conn.name }}</h4>
            <p>{{ conn.host }}:{{ conn.port }}</p>
            <p v-if="conn.description" class="description">{{ conn.description }}</p>
          </div>
          <div class="connection-actions">
            <el-button size="small" @click.stop="editConnection(conn)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button size="small" type="danger" @click.stop="deleteConnection(conn)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- MySQL客户端界面 -->
    <div v-if="activeConnection" class="mysql-client">
      <div class="client-header">
        <div class="connection-info">
          <h3>{{ activeConnection.name }}</h3>
          <span class="connection-details">{{ activeConnection.host }}:{{ activeConnection.port }}</span>
        </div>
        <div class="header-actions">
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          <el-button @click="activeConnection = null">
            <el-icon><Back /></el-icon>
            返回
          </el-button>
        </div>
      </div>

      <div class="client-content">
        <el-tabs v-model="activeTab" type="card">
          <!-- 概览标签页 -->
          <el-tab-pane label="概览" name="overview">
            <div class="overview-content">
              <div class="overview-grid">
                <!-- 数据库列表 -->
                <el-card class="overview-card">
                  <template #header>
                    <div class="card-header">
                      <span>数据库</span>
                      <el-button size="small" @click="loadDatabases">
                        <el-icon><Refresh /></el-icon>
                      </el-button>
                    </div>
                  </template>
                  <div class="database-list">
                    <div 
                      v-for="db in databases" 
                      :key="db.name"
                      class="database-item"
                      @click="selectDatabase(db.name)"
                    >
                      <div class="database-info">
                        <strong>{{ db.name }}</strong>
                        <span class="database-meta">{{ db.charset }} / {{ db.collation }}</span>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>

          <!-- 表浏览器标签页 -->
          <el-tab-pane label="表浏览器" name="tables">
            <div class="tables-browser">
              <div class="browser-toolbar">
                <el-select 
                  v-model="selectedDatabase" 
                  placeholder="选择数据库"
                  @change="loadTables"
                  style="width: 200px"
                >
                  <el-option
                    v-for="db in databases"
                    :key="db.name"
                    :label="db.name"
                    :value="db.name"
                  />
                </el-select>
                <el-button @click="loadTables" :disabled="!selectedDatabase">
                  <el-icon><Refresh /></el-icon>
                  刷新表
                </el-button>
              </div>

              <div v-if="selectedDatabase" class="tables-content">
                <div class="tables-list">
                  <el-table :data="tables" @row-click="selectTable">
                    <el-table-column prop="name" label="表名" />
                    <el-table-column prop="engine" label="引擎" width="100" />
                    <el-table-column prop="rows" label="行数" width="100" />
                    <el-table-column prop="comment" label="备注" />
                  </el-table>
                </div>

                <div v-if="selectedTable" class="table-details">
                  <el-tabs v-model="tableTab">
                    <el-tab-pane label="表结构" name="structure">
                      <el-table :data="columns">
                        <el-table-column prop="field" label="字段名" />
                        <el-table-column prop="type" label="类型" />
                        <el-table-column prop="null" label="允许NULL" width="100" />
                        <el-table-column prop="key" label="键" width="80" />
                        <el-table-column prop="default" label="默认值" />
                        <el-table-column prop="extra" label="额外" />
                        <el-table-column prop="comment" label="备注" />
                      </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="数据预览" name="data">
                      <div class="data-preview">
                        <div class="preview-toolbar">
                          <el-button @click="previewTableData">
                            <el-icon><View /></el-icon>
                            预览数据
                          </el-button>
                        </div>
                        <div v-if="tableData" class="preview-result">
                          <el-table :data="tableData.rows.slice(0, 100)">
                            <el-table-column 
                              v-for="col in tableData.columns" 
                              :key="col"
                              :prop="col"
                              :label="col"
                              show-overflow-tooltip
                            />
                          </el-table>
                          <p class="preview-info">显示前100行，共{{ tableData.total }}行</p>
                        </div>
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- SQL查询标签页 -->
          <el-tab-pane label="SQL查询" name="query">
            <div class="sql-query">
              <div class="query-editor">
                <el-input
                  v-model="sqlQuery"
                  type="textarea"
                  :rows="8"
                  placeholder="输入SQL查询语句..."
                />
                <div class="query-actions">
                  <el-button type="primary" @click="executeQuery" :loading="queryLoading">
                    <el-icon><CaretRight /></el-icon>
                    执行查询
                  </el-button>
                  <el-button @click="sqlQuery = ''">
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </div>
              </div>

              <div v-if="queryResult" class="query-result">
                <div class="result-header">
                  <h4>查询结果</h4>
                  <span class="result-info">{{ queryResult.total }} 行</span>
                </div>
                <el-table :data="queryResult.rows" max-height="400">
                  <el-table-column 
                    v-for="col in queryResult.columns" 
                    :key="col"
                    :prop="col"
                    :label="col"
                    show-overflow-tooltip
                  />
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 新建/编辑连接对话框 -->
    <el-dialog
      v-model="showConnectionDialog"
      :title="editingConnection ? '编辑MySQL连接' : '新建MySQL连接'"
      width="600px"
      @close="resetConnectionForm"
    >
      <el-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        label-width="100px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="connectionForm.name" placeholder="请输入连接名称" />
        </el-form-item>
        
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="connectionForm.host" placeholder="请输入主机地址或IP" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="connectionForm.port" :min="1" :max="65535" />
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="connectionForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="connectionForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="默认数据库">
          <el-input v-model="connectionForm.database" placeholder="请输入默认数据库（可选）" />
        </el-form-item>
        
        <el-form-item label="字符集">
          <el-select v-model="connectionForm.charset" placeholder="选择字符集">
            <el-option label="utf8mb4" value="utf8mb4" />
            <el-option label="utf8" value="utf8" />
            <el-option label="latin1" value="latin1" />
            <el-option label="gbk" value="gbk" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="connectionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showConnectionDialog = false">取消</el-button>
        <el-button type="primary" @click="testConnection" :loading="testing">
          测试连接
        </el-button>
        <el-button type="primary" @click="saveConnection" :loading="saving">
          {{ editingConnection ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  Edit,
  Delete,
  Back,
  View,
  CaretRight
} from '@element-plus/icons-vue'
import {
  createMySQLConnection,
  getMySQLConnections,
  updateMySQLConnection,
  deleteMySQLConnection as deleteMySQLConnectionAPI,
  testMySQLConnection,
  getMySQLDatabases,
  getMySQLTables,
  getMySQLColumns,
  executeMySQLQuery,
  type MySQLConnection,
  type MySQLDatabase,
  type MySQLTable,
  type MySQLColumn,
  type MySQLQueryResult
} from '@/api/mysql'

// 响应式数据
const connections = ref<MySQLConnection[]>([])
const activeConnection = ref<MySQLConnection | null>(null)
const databases = ref<MySQLDatabase[]>([])
const tables = ref<MySQLTable[]>([])
const columns = ref<MySQLColumn[]>([])
const selectedDatabase = ref('')
const selectedTable = ref<MySQLTable | null>(null)
const activeTab = ref('overview')
const tableTab = ref('structure')
const sqlQuery = ref('')
const queryResult = ref<MySQLQueryResult | null>(null)
const tableData = ref<MySQLQueryResult | null>(null)
const queryLoading = ref(false)

// 对话框状态
const showConnectionDialog = ref(false)
const editingConnection = ref<MySQLConnection | null>(null)
const saving = ref(false)
const testing = ref(false)
const connectionFormRef = ref()

// 连接表单
const connectionForm = reactive<MySQLConnection>({
  name: '',
  host: '',
  port: 3306,
  username: '',
  password: '',
  database: '',
  charset: 'utf8mb4',
  description: ''
})

// 表单验证规则
const connectionRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 生命周期
onMounted(() => {
  loadConnections()
})

// 方法
const loadConnections = async () => {
  try {
    const response = await getMySQLConnections()
    connections.value = response.data.data || []
  } catch (error: any) {
    ElMessage.error('获取连接列表失败: ' + (error.response?.data?.message || error.message))
  }
}

const selectConnection = async (conn: MySQLConnection) => {
  activeConnection.value = conn
  await loadDatabases()
}

const loadDatabases = async () => {
  if (!activeConnection.value) return

  try {
    const response = await getMySQLDatabases(activeConnection.value.id!)
    databases.value = response.data.data || []
  } catch (error: any) {
    ElMessage.error('获取数据库列表失败: ' + (error.response?.data?.message || error.message))
  }
}

const selectDatabase = (dbName: string) => {
  selectedDatabase.value = dbName
  activeTab.value = 'tables'
  loadTables()
}

const loadTables = async () => {
  if (!activeConnection.value || !selectedDatabase.value) return

  try {
    const response = await getMySQLTables(activeConnection.value.id!, selectedDatabase.value)
    tables.value = response.data.data || []
  } catch (error: any) {
    ElMessage.error('获取表列表失败: ' + (error.response?.data?.message || error.message))
  }
}

const selectTable = async (table: MySQLTable) => {
  selectedTable.value = table
  await loadColumns()
}

const loadColumns = async () => {
  if (!activeConnection.value || !selectedDatabase.value || !selectedTable.value) return

  try {
    const response = await getMySQLColumns(activeConnection.value.id!, selectedDatabase.value, selectedTable.value.name)
    columns.value = response.data.data || []
  } catch (error: any) {
    ElMessage.error('获取表结构失败: ' + (error.response?.data?.message || error.message))
  }
}

const previewTableData = async () => {
  if (!activeConnection.value || !selectedDatabase.value || !selectedTable.value) return

  try {
    const query = `SELECT * FROM \`${selectedDatabase.value}\`.\`${selectedTable.value.name}\` LIMIT 100`
    const response = await executeMySQLQuery(activeConnection.value.id!, query)
    tableData.value = response.data.data
  } catch (error: any) {
    ElMessage.error('预览表数据失败: ' + (error.response?.data?.message || error.message))
  }
}

const executeQuery = async () => {
  if (!activeConnection.value || !sqlQuery.value.trim()) {
    ElMessage.warning('请输入SQL查询语句')
    return
  }

  queryLoading.value = true
  try {
    const response = await executeMySQLQuery(activeConnection.value.id!, sqlQuery.value)
    queryResult.value = response.data.data
    ElMessage.success('查询执行成功')
  } catch (error: any) {
    ElMessage.error('查询执行失败: ' + (error.response?.data?.message || error.message))
  } finally {
    queryLoading.value = false
  }
}

const refreshData = async () => {
  await loadDatabases()
  if (selectedDatabase.value) {
    await loadTables()
  }
}

const editConnection = (conn: MySQLConnection) => {
  editingConnection.value = conn
  Object.assign(connectionForm, conn)
  showConnectionDialog.value = true
}

const deleteConnection = async (conn: MySQLConnection) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除连接 "${conn.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteMySQLConnectionAPI(conn.id!)
    ElMessage.success('连接删除成功')
    await loadConnections()

    if (activeConnection.value?.id === conn.id) {
      activeConnection.value = null
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除连接失败: ' + (error.response?.data?.message || error.message))
    }
  }
}

const saveConnection = async () => {
  if (!connectionFormRef.value) return

  try {
    await connectionFormRef.value.validate()
  } catch (error) {
    return
  }

  saving.value = true
  try {
    if (editingConnection.value) {
      await updateMySQLConnection(editingConnection.value.id!, connectionForm)
      ElMessage.success('连接更新成功')
    } else {
      await createMySQLConnection(connectionForm)
      ElMessage.success('连接创建成功')
    }

    showConnectionDialog.value = false
    await loadConnections()
  } catch (error: any) {
    ElMessage.error('保存连接失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  if (!connectionFormRef.value) return

  try {
    await connectionFormRef.value.validate()
  } catch (error) {
    return
  }

  testing.value = true
  try {
    await testMySQLConnection(connectionForm)
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message))
  } finally {
    testing.value = false
  }
}

const resetConnectionForm = () => {
  editingConnection.value = null
  Object.assign(connectionForm, {
    name: '',
    host: '',
    port: 3306,
    username: '',
    password: '',
    database: '',
    charset: 'utf8mb4',
    description: ''
  })
  if (connectionFormRef.value) {
    connectionFormRef.value.clearValidate()
  }
}
</script>

<style scoped>
.mysql-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.toolbar-left h2 {
  margin: 0;
  color: #303133;
}

.toolbar-right {
  display: flex;
  gap: 8px;
}

.connections-grid {
  flex: 1;
  padding: 24px;
  background: #f5f7fa;
}

.connections-header {
  text-align: center;
  margin-bottom: 32px;
}

.connections-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.connections-header p {
  margin: 0;
  color: #909399;
}

.connections-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.connection-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.connection-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.1);
}

.connection-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.connection-info p {
  margin: 4px 0;
  color: #606266;
  font-size: 14px;
}

.connection-info .description {
  color: #909399;
  font-size: 12px;
}

.connection-actions {
  display: flex;
  gap: 4px;
}

.mysql-client {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.client-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.connection-info h3 {
  margin: 0;
  color: #303133;
}

.connection-details {
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.client-content {
  flex: 1;
  padding: 16px;
}

.overview-content {
  padding: 16px;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.overview-card {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.database-list {
  max-height: 320px;
  overflow-y: auto;
}

.database-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.database-item:hover {
  background-color: #f5f7fa;
}

.database-item:last-child {
  border-bottom: none;
}

.database-info strong {
  display: block;
  color: #303133;
  margin-bottom: 4px;
}

.database-meta {
  color: #909399;
  font-size: 12px;
}

.tables-browser {
  padding: 16px;
}

.browser-toolbar {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.tables-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  height: 600px;
}

.tables-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.table-details {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.sql-query {
  padding: 16px;
}

.query-editor {
  margin-bottom: 16px;
}

.query-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.query-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.result-header h4 {
  margin: 0;
  color: #303133;
}

.result-info {
  color: #909399;
  font-size: 14px;
}

.data-preview {
  padding: 16px;
}

.preview-toolbar {
  margin-bottom: 16px;
}

.preview-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
}

.preview-info {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
  text-align: center;
}
</style>
