import api from './index'

export interface Ticket {
  id: number
  title: string
  description: string
  type: 'requirement' | 'bug' | 'suggestion'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: number
  creator_id: number
  assignee_ids: number[]
  reject_reason?: string
  feedback?: string
  created_at: string
  updated_at: string
  creator?: {
    id: number
    username: string
    email: string
  }
  comments?: Comment[]
  transfers?: TicketTransfer[]
}

export interface Comment {
  id: number
  ticket_id: number
  user_id: number
  content: string
  images?: string
  created_at: string
  user?: {
    id: number
    username: string
  }
}

export interface TicketTransfer {
  id: number
  ticket_id: number
  from_user_id: number
  to_user_id: number
  reason: string
  created_at: string
  from_user?: {
    id: number
    username: string
  }
  to_user?: {
    id: number
    username: string
  }
}

export interface CreateTicketRequest {
  title: string
  description: string
  type: 'requirement' | 'bug'
  // priority 由管理员分配工单时设置，创建时不需要
}

export interface UpdateStatusRequest {
  status: number
  priority?: string
  reject_reason?: string
  feedback?: string
  assignee_ids?: number[]
}

export interface TransferTicketRequest {
  to_user_id: number
  reason: string
}

export interface CreateCommentRequest {
  ticket_id: number
  content: string
  images?: string[]
}

// 创建工单
export const createTicket = (data: CreateTicketRequest) => {
  return api.post('/tickets', data)
}

// 获取工单列表
export const getTickets = (params?: string) => {
  const url = params ? `/tickets?${params}` : '/tickets'
  return api.get(url)
}

// 获取工单详情
export const getTicket = (id: number) => {
  return api.get(`/tickets/${id}`)
}

// 更新工单状态
export const updateTicketStatus = (id: number, data: UpdateStatusRequest) => {
  return api.put(`/tickets/${id}/status`, data)
}

// 更新工单优先级
export const updateTicketPriority = (id: number, priority: string) => {
  return api.put(`/tickets/${id}/priority`, { priority })
}

// 转交工单
export const transferTicket = (id: number, data: TransferTicketRequest) => {
  return api.post(`/tickets/${id}/transfer`, data)
}

// 创建评论
export const createComment = (data: CreateCommentRequest) => {
  return api.post('/comments', data)
}

// 获取工单评论
export const getComments = (ticketId: number) => {
  return api.get(`/comments/ticket/${ticketId}`)
}
