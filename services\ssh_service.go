package services

import (
	"ReqFlow/models"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"sync"
	"time"

	"golang.org/x/crypto/ssh"
	"gorm.io/gorm"
)

// SSHService SSH服务
type SSHService struct {
	db       *gorm.DB
	sessions map[string]*SSHSessionManager
	mutex    sync.RWMutex
}

// SSHSessionManager SSH会话管理器
type SSHSessionManager struct {
	Connection *models.SSHConnection
	SSHClient  *ssh.Client
	SSHSession *ssh.Session
	SessionID  string
	UserID     uint
	StdinPipe  io.WriteCloser
	StdoutPipe io.Reader
	StderrPipe io.Reader
	CreatedAt  time.Time
	LastActive time.Time
}

// NewSSHService 创建SSH服务实例
func NewSSHService(db *gorm.DB) *SSHService {
	return &SSHService{
		db:       db,
		sessions: make(map[string]*SSHSessionManager),
	}
}

// CreateConnection 创建SSH连接配置
func (s *SSHService) CreateConnection(conn *models.SSHConnection) error {
	return s.db.Create(conn).Error
}

// GetConnections 获取用户的SSH连接列表
func (s *SSHService) GetConnections(userID uint) ([]models.SSHConnection, error) {
	var connections []models.SSHConnection
	err := s.db.Where("user_id = ? AND is_active = ?", userID, true).
		Order("created_at DESC").Find(&connections).Error
	return connections, err
}

// GetConnection 获取单个SSH连接
func (s *SSHService) GetConnection(id uint, userID uint) (*models.SSHConnection, error) {
	var connection models.SSHConnection
	err := s.db.Where("id = ? AND user_id = ? AND is_active = ?", id, userID, true).
		First(&connection).Error
	return &connection, err
}

// UpdateConnection 更新SSH连接配置
func (s *SSHService) UpdateConnection(conn *models.SSHConnection) error {
	return s.db.Save(conn).Error
}

// DeleteConnection 删除SSH连接配置
func (s *SSHService) DeleteConnection(id uint, userID uint) error {
	return s.db.Where("id = ? AND user_id = ?", id, userID).
		Update("is_active", false).Error
}

// CreateSSHClient 创建SSH客户端连接
func (s *SSHService) CreateSSHClient(conn *models.SSHConnection) (*ssh.Client, error) {
	config := &ssh.ClientConfig{
		User:            conn.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 注意：生产环境应该验证主机密钥
		Timeout:         10 * time.Second, // 减少超时时间到10秒
	}

	// 检查认证方式
	if conn.Password == "" && conn.PrivateKey == "" {
		return nil, fmt.Errorf("必须提供密码或私钥进行认证")
	}

	// 支持密码认证
	if conn.Password != "" {
		config.Auth = append(config.Auth, ssh.Password(conn.Password))
	}

	// 支持私钥认证
	if conn.PrivateKey != "" {
		signer, err := ssh.ParsePrivateKey([]byte(conn.PrivateKey))
		if err != nil {
			return nil, fmt.Errorf("解析私钥失败: %v", err)
		}
		config.Auth = append(config.Auth, ssh.PublicKeys(signer))
	}

	// 连接SSH服务器
	address := fmt.Sprintf("%s:%d", conn.Host, conn.Port)

	// 添加详细的错误信息
	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		// 根据错误类型提供更详细的信息
		if netErr, ok := err.(interface{ Timeout() bool }); ok && netErr.Timeout() {
			return nil, fmt.Errorf("连接超时: 无法连接到 %s，请检查主机地址和端口是否正确", address)
		}
		return nil, fmt.Errorf("SSH连接失败: %v", err)
	}

	return client, nil
}

// CreateSession 创建SSH会话
func (s *SSHService) CreateSession(connectionID uint, userID uint) (*SSHSessionManager, error) {
	// 获取连接配置
	conn, err := s.GetConnection(connectionID, userID)
	if err != nil {
		return nil, fmt.Errorf("获取连接配置失败: %v", err)
	}

	// 创建SSH客户端
	client, err := s.CreateSSHClient(conn)
	if err != nil {
		return nil, err
	}

	// 创建SSH会话
	session, err := client.NewSession()
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("创建SSH会话失败: %v", err)
	}

	// 设置终端模式
	modes := ssh.TerminalModes{
		ssh.ECHO:          1,     // 启用回显
		ssh.TTY_OP_ISPEED: 14400, // 输入速度
		ssh.TTY_OP_OSPEED: 14400, // 输出速度
	}

	// 请求伪终端
	if err := session.RequestPty("xterm-256color", 80, 24, modes); err != nil {
		session.Close()
		client.Close()
		return nil, fmt.Errorf("请求伪终端失败: %v", err)
	}

	// 获取输入输出管道
	stdinPipe, err := session.StdinPipe()
	if err != nil {
		session.Close()
		client.Close()
		return nil, fmt.Errorf("获取stdin管道失败: %v", err)
	}

	stdoutPipe, err := session.StdoutPipe()
	if err != nil {
		session.Close()
		client.Close()
		return nil, fmt.Errorf("获取stdout管道失败: %v", err)
	}

	stderrPipe, err := session.StderrPipe()
	if err != nil {
		session.Close()
		client.Close()
		return nil, fmt.Errorf("获取stderr管道失败: %v", err)
	}

	// 生成会话ID
	sessionID, err := s.generateSessionID()
	if err != nil {
		session.Close()
		client.Close()
		return nil, fmt.Errorf("生成会话ID失败: %v", err)
	}

	// 创建会话管理器
	manager := &SSHSessionManager{
		Connection: conn,
		SSHClient:  client,
		SSHSession: session,
		SessionID:  sessionID,
		UserID:     userID,
		StdinPipe:  stdinPipe,
		StdoutPipe: stdoutPipe,
		StderrPipe: stderrPipe,
		CreatedAt:  time.Now(),
		LastActive: time.Now(),
	}

	// 保存会话到内存
	s.mutex.Lock()
	s.sessions[sessionID] = manager
	s.mutex.Unlock()

	// 保存会话到数据库
	dbSession := &models.SSHSession{
		ConnectionID: connectionID,
		UserID:       userID,
		SessionID:    sessionID,
		Status:       "connecting",
		StartTime:    time.Now(),
		LastActivity: time.Now(),
	}
	if err := s.db.Create(dbSession).Error; err != nil {
		s.CloseSession(sessionID)
		return nil, fmt.Errorf("保存会话到数据库失败: %v", err)
	}

	return manager, nil
}

// GetSession 获取SSH会话
func (s *SSHService) GetSession(sessionID string) (*SSHSessionManager, bool) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	session, exists := s.sessions[sessionID]
	return session, exists
}

// CloseSession 关闭SSH会话
func (s *SSHService) CloseSession(sessionID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	session, exists := s.sessions[sessionID]
	if !exists {
		return fmt.Errorf("会话不存在")
	}

	// 关闭SSH连接
	if session.SSHSession != nil {
		session.SSHSession.Close()
	}
	if session.SSHClient != nil {
		session.SSHClient.Close()
	}

	// 从内存中删除
	delete(s.sessions, sessionID)

	// 更新数据库状态
	now := time.Now()
	return s.db.Model(&models.SSHSession{}).
		Where("session_id = ?", sessionID).
		Updates(map[string]interface{}{
			"status":   "disconnected",
			"end_time": &now,
		}).Error
}

// UpdateSessionActivity 更新会话活动时间
func (s *SSHService) UpdateSessionActivity(sessionID string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if session, exists := s.sessions[sessionID]; exists {
		session.LastActive = time.Now()
		// 异步更新数据库
		go func() {
			s.db.Model(&models.SSHSession{}).
				Where("session_id = ?", sessionID).
				Update("last_activity", time.Now())
		}()
	}
}

// generateSessionID 生成唯一的会话ID
func (s *SSHService) generateSessionID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// StartShell 启动Shell
func (manager *SSHSessionManager) StartShell() error {
	return manager.SSHSession.Shell()
}

// ResizeTerminal 调整终端大小
func (manager *SSHSessionManager) ResizeTerminal(width, height int) error {
	return manager.SSHSession.WindowChange(height, width)
}
