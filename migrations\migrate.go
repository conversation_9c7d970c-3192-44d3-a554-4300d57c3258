package migrations

import (
	"ReqFlow/models"
	"ReqFlow/utils"
	"log"
	"strings"

	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	// 禁用外键检查
	if db.Dialector.Name() == "mysql" {
		db.Exec("SET FOREIGN_KEY_CHECKS = 0")
		defer db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	}

	// 创建所有表
	err := db.AutoMigrate(
		&models.Team{},
		&models.InviteCode{},
		&models.User{},
		&models.Ticket{},
		&models.Comment{},
		&models.TicketTransfer{},
		&models.SSHConnection{},
		&models.SSHSession{},
		&models.RedisConnection{},
		&models.MySQLConnection{},
	)

	if err != nil {
		// 在正式环境中，只忽略索引重复错误
		if db.Dialector.Name() == "mysql" && strings.Contains(err.Error(), "Duplicate key name") {
			log.Printf("Migration warning (ignored): %v", err)
			log.Println("Database migration completed with warnings")
			return nil
		}
		return err
	}

	// 修复Redis连接表的外键约束问题
	if err := FixRedisConnectionConstraints(db); err != nil {
		log.Printf("Warning: Redis constraints fix failed: %v", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// SeedData 初始化种子数据
func SeedData(db *gorm.DB) error {
	// 禁用外键检查以避免循环依赖
	if db.Dialector.Name() == "mysql" {
		db.Exec("SET FOREIGN_KEY_CHECKS = 0")
		defer db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	}

	// 创建默认管理员团队
	var adminTeamCount int64
	db.Model(&models.Team{}).Where("name = ?", "管理员团队").Count(&adminTeamCount)

	var adminTeam models.Team
	if adminTeamCount == 0 {
		adminTeam = models.Team{
			Name:        "管理员团队",
			Description: "系统管理员默认团队",
			IsActive:    true,
			CreatedBy:   1, // 临时设置，稍后会更新
		}

		if err := db.Create(&adminTeam).Error; err != nil {
			return err
		}

		log.Println("Default admin team created: 管理员团队")
	} else {
		// 获取已存在的管理员团队
		db.Where("name = ?", "管理员团队").First(&adminTeam)
	}

	// 检查是否已有管理员用户
	var adminCount int64
	db.Model(&models.User{}).Where("role = ?", models.RoleAdmin).Count(&adminCount)

	if adminCount == 0 {
		// 创建管理员邀请码
		adminInviteCode := models.InviteCode{
			Code:     "ADMIN2024INIT",
			RoleType: models.RoleAdmin,
			TeamName: "System Admin",
			IsActive: true,
		}

		if err := db.Create(&adminInviteCode).Error; err != nil {
			return err
		}

		// 创建默认管理员用户
		hashedPassword, err := utils.HashPassword("admin123456")
		if err != nil {
			return err
		}

		adminUser := models.User{
			Username:     "admin",
			Name:         "系统管理员",
			Email:        "<EMAIL>",
			Password:     hashedPassword,
			Role:         models.RoleAdmin,
			TeamID:       adminTeam.ID,
			InviteCodeID: adminInviteCode.ID,
			IsActive:     true,
		}

		if err := db.Create(&adminUser).Error; err != nil {
			return err
		}

		// 更新邀请码使用次数
		adminInviteCode.Use()
		db.Save(&adminInviteCode)

		// 更新管理员团队的创建者ID
		adminTeam.CreatedBy = adminUser.ID
		db.Save(&adminTeam)

		log.Println("Default admin user created:")
		log.Println("Username: admin")
		log.Println("Password: admin123456")
		log.Println("Please change the password after first login!")
	}

	// 创建邀请码
	var codeCount int64
	db.Model(&models.InviteCode{}).Count(&codeCount)

	if codeCount == 0 {
		// 管理员邀请码
		adminInviteCode := models.InviteCode{
			Code:     "ADMIN2024",
			RoleType: models.RoleAdmin,
			TeamName: "管理员团队",
			MaxUses:  100,
			IsActive: true,
		}

		// 开发者邀请码
		devInviteCode := models.InviteCode{
			Code:     "DEV2024",
			RoleType: models.RoleDeveloper,
			TeamName: "开发团队",
			MaxUses:  100,
			IsActive: true,
		}

		// 普通用户邀请码
		userInviteCode := models.InviteCode{
			Code:     "USER2024",
			RoleType: models.RoleUser,
			TeamName: "管理员团队", // 普通用户默认分配到管理员团队
			MaxUses:  100,
			IsActive: true,
		}

		if err := db.Create(&adminInviteCode).Error; err != nil {
			return err
		}

		if err := db.Create(&devInviteCode).Error; err != nil {
			return err
		}

		if err := db.Create(&userInviteCode).Error; err != nil {
			return err
		}

		log.Println("Invite codes created:")
		log.Println("Admin: ADMIN2024")
		log.Println("Developer: DEV2024")
		log.Println("User: USER2024")
	}

	// 创建开发团队
	var devTeamCount int64
	db.Model(&models.Team{}).Where("name = ?", "开发团队").Count(&devTeamCount)

	var devTeam models.Team
	if devTeamCount == 0 {
		// 获取管理员用户ID
		var adminUser models.User
		db.Where("role = ?", models.RoleAdmin).First(&adminUser)

		devTeam = models.Team{
			Name:        "开发团队",
			Description: "开发人员默认团队",
			IsActive:    true,
			CreatedBy:   adminUser.ID, // 由管理员创建
		}

		if err := db.Create(&devTeam).Error; err != nil {
			return err
		}

		log.Println("Default dev team created: 开发团队")
	} else {
		db.Where("name = ?", "开发团队").First(&devTeam)
	}

	// 创建示例开发者用户
	var devUserCount int64
	db.Model(&models.User{}).Where("role = ?", models.RoleDeveloper).Count(&devUserCount)

	if devUserCount == 0 {
		// 创建示例开发者
		hashedPassword, err := utils.HashPassword("dev123456")
		if err != nil {
			return err
		}

		// 获取开发者邀请码
		var devInviteCode models.InviteCode
		db.Where("code = ?", "DEV2024").First(&devInviteCode)

		devUser := models.User{
			Username:     "dev01",
			Name:         "张三",
			Email:        "<EMAIL>",
			Password:     hashedPassword,
			Role:         models.RoleDeveloper,
			TeamID:       devTeam.ID,
			InviteCodeID: devInviteCode.ID,
			IsActive:     true,
		}

		if err := db.Create(&devUser).Error; err != nil {
			return err
		}

		// 更新邀请码使用次数
		devInviteCode.Use()
		db.Save(&devInviteCode)

		log.Println("Demo developer user created:")
		log.Println("Username: dev01")
		log.Println("Name: 张三")
		log.Println("Password: dev123456")
	}

	// 将所有现有用户（没有团队ID的）分配到管理员团队
	var usersWithoutTeam []models.User
	db.Where("team_id = 0 OR team_id IS NULL").Find(&usersWithoutTeam)

	if len(usersWithoutTeam) > 0 {
		log.Printf("Found %d users without team assignment", len(usersWithoutTeam))

		// 获取管理员团队
		var adminTeam models.Team
		if err := db.Where("name = ?", "管理员团队").First(&adminTeam).Error; err != nil {
			log.Printf("Warning: Could not find admin team: %v", err)
		} else {
			// 将这些用户分配到管理员团队
			for _, user := range usersWithoutTeam {
				user.TeamID = adminTeam.ID
				if err := db.Save(&user).Error; err != nil {
					log.Printf("Error assigning user %s to admin team: %v", user.Username, err)
				} else {
					log.Printf("Assigned user %s (%s) to admin team", user.Username, user.Name)
				}
			}
			log.Printf("Successfully assigned %d users to admin team", len(usersWithoutTeam))
		}
	}

	// 更新所有老工单的优先级为默认
	var ticketsWithOldPriority []models.Ticket
	// 查找所有不是新格式的优先级（包括空值、null、以及旧的low/medium/high/urgent/P7）
	db.Where("priority = '' OR priority IS NULL OR priority NOT IN ('P0', 'P1', 'P2', 'P3', 'P4', 'P5', 'P6', 'default', 'P7')").Find(&ticketsWithOldPriority)

	if len(ticketsWithOldPriority) > 0 {
		log.Printf("Found %d tickets with old priority format, updating to default", len(ticketsWithOldPriority))

		for _, ticket := range ticketsWithOldPriority {
			oldPriority := ticket.Priority
			ticket.Priority = models.PriorityDefault
			if err := db.Save(&ticket).Error; err != nil {
				log.Printf("Error updating ticket %d priority from '%s' to default: %v", ticket.ID, oldPriority, err)
			} else {
				log.Printf("Updated ticket %d priority from '%s' to default", ticket.ID, oldPriority)
			}
		}

		log.Printf("All %d tickets with old priority format have been updated to default", len(ticketsWithOldPriority))
	} else {
		log.Println("No tickets found with old priority format")
	}

	log.Println("Seed data initialization completed")
	return nil
}

// FixUserTeamAssignments 修复用户团队分配
func FixUserTeamAssignments(db *gorm.DB) error {
	log.Println("Starting user team assignment fix...")

	// 获取管理员团队
	var adminTeam models.Team
	if err := db.Where("name = ?", "管理员团队").First(&adminTeam).Error; err != nil {
		log.Printf("Error: Could not find admin team: %v", err)
		return err
	}

	// 查找所有没有团队分配的用户
	var usersWithoutTeam []models.User
	db.Where("team_id = 0 OR team_id IS NULL").Find(&usersWithoutTeam)

	if len(usersWithoutTeam) == 0 {
		log.Println("All users already have team assignments")
		return nil
	}

	log.Printf("Found %d users without team assignment", len(usersWithoutTeam))

	// 将这些用户分配到管理员团队
	successCount := 0
	for _, user := range usersWithoutTeam {
		user.TeamID = adminTeam.ID
		if err := db.Save(&user).Error; err != nil {
			log.Printf("Error assigning user %s to admin team: %v", user.Username, err)
		} else {
			log.Printf("✅ Assigned user %s (%s) to admin team", user.Username, user.Name)
			successCount++
		}
	}

	log.Printf("Successfully assigned %d/%d users to admin team", successCount, len(usersWithoutTeam))
	log.Println("User team assignment fix completed")
	return nil
}

// FixRedisConnectionConstraints 修复Redis连接表的外键约束问题
func FixRedisConnectionConstraints(db *gorm.DB) error {
	log.Println("Starting Redis connection constraints fix...")

	if db.Dialector.Name() == "mysql" {
		// 检查外键约束是否存在
		var constraintExists int64
		db.Raw("SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE WHERE CONSTRAINT_NAME = 'fk_redis_connections_user' AND TABLE_SCHEMA = DATABASE()").Scan(&constraintExists)

		if constraintExists > 0 {
			log.Println("Dropping existing foreign key constraint: fk_redis_connections_user")
			// 删除外键约束
			if err := db.Exec("ALTER TABLE redis_connections DROP FOREIGN KEY fk_redis_connections_user").Error; err != nil {
				log.Printf("Warning: Could not drop foreign key constraint: %v", err)
			} else {
				log.Println("Successfully dropped foreign key constraint")
			}
		} else {
			log.Println("Foreign key constraint fk_redis_connections_user does not exist")
		}
	}

	log.Println("Redis connection constraints fix completed")
	return nil
}
