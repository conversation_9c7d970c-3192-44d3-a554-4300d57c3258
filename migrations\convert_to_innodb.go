package migrations

import (
	"fmt"
	"log"

	"gorm.io/gorm"
)

// ConvertTablesToInnoDB 将所有表的存储引擎转换为InnoDB
func ConvertTablesToInnoDB(db *gorm.DB) error {
	log.Println("Starting conversion of tables to InnoDB engine...")

	// 获取数据库名称
	var dbName string
	if err := db.Raw("SELECT DATABASE()").Scan(&dbName).Error; err != nil {
		return fmt.Errorf("failed to get database name: %v", err)
	}

	// 查询所有使用MyISAM引擎的表
	var tables []struct {
		TableName string `gorm:"column:TABLE_NAME"`
		Engine    string `gorm:"column:ENGINE"`
	}

	query := `
		SELECT TABLE_NAME, ENGINE 
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? AND ENGINE != 'InnoDB'
	`

	if err := db.Raw(query, dbName).Scan(&tables).Error; err != nil {
		return fmt.Errorf("failed to query table engines: %v", err)
	}

	if len(tables) == 0 {
		log.Println("All tables are already using InnoDB engine")
		return nil
	}

	log.Printf("Found %d tables not using InnoDB engine:", len(tables))
	for _, table := range tables {
		log.Printf("  - %s (current engine: %s)", table.TableName, table.Engine)
	}

	// 转换每个表到InnoDB
	for _, table := range tables {
		log.Printf("Converting table '%s' from %s to InnoDB...", table.TableName, table.Engine)

		alterSQL := fmt.Sprintf("ALTER TABLE `%s` ENGINE=InnoDB", table.TableName)
		if err := db.Exec(alterSQL).Error; err != nil {
			log.Printf("Warning: Failed to convert table '%s' to InnoDB: %v", table.TableName, err)
			continue
		}

		log.Printf("✅ Successfully converted table '%s' to InnoDB", table.TableName)
	}

	log.Println("表引擎转换完成")
	return nil
}

// CheckTableEngines 检查所有表的存储引擎
func CheckTableEngines(db *gorm.DB) error {
	log.Println("检查表存储引擎...")

	// 获取数据库名称
	var dbName string
	if err := db.Raw("SELECT DATABASE()").Scan(&dbName).Error; err != nil {
		return fmt.Errorf("获取数据库名称失败: %v", err)
	}

	// 查询所有表的存储引擎
	var tables []struct {
		TableName string `gorm:"column:TABLE_NAME"`
		Engine    string `gorm:"column:ENGINE"`
	}

	query := `
		SELECT TABLE_NAME, ENGINE 
		FROM information_schema.TABLES 
		WHERE TABLE_SCHEMA = ? 
		ORDER BY TABLE_NAME
	`

	if err := db.Raw(query, dbName).Scan(&tables).Error; err != nil {
		return fmt.Errorf("查询表引擎失败: %v", err)
	}

	log.Printf("数据库 '%s' 表引擎:", dbName)
	log.Println("┌─────────────────────────────────┬──────────────┐")
	log.Println("│ 表名                            │ 引擎         │")
	log.Println("├─────────────────────────────────┼──────────────┤")

	innodbCount := 0
	for _, table := range tables {
		if table.Engine == "InnoDB" {
			innodbCount++
		}
		log.Printf("│ %-31s │ %-12s │", table.TableName, table.Engine)
	}

	log.Println("└─────────────────────────────────┴──────────────┘")
	log.Printf("总表数: %d, InnoDB表数: %d", len(tables), innodbCount)

	if innodbCount == len(tables) {
		log.Println("✅ 所有表都使用InnoDB引擎")
	} else {
		log.Printf("⚠️  %d 个表未使用InnoDB引擎", len(tables)-innodbCount)
	}

	return nil
}

// OptimizeInnoDBSettings 优化InnoDB设置
func OptimizeInnoDBSettings(db *gorm.DB) error {
	log.Println("Applying InnoDB optimization settings...")

	// InnoDB优化设置
	settings := map[string]string{
		"innodb_buffer_pool_size":        "128M", // 根据服务器内存调整
		"innodb_log_file_size":           "64M",  // 日志文件大小
		"innodb_flush_log_at_trx_commit": "2",    // 提高性能，降低安全性
		"innodb_file_per_table":          "ON",   // 每个表独立文件
		"innodb_open_files":              "300",  // 打开文件数
	}

	for setting, value := range settings {
		// 注意：这些设置通常需要在MySQL配置文件中设置，运行时设置可能不会生效
		sql := fmt.Sprintf("SET GLOBAL %s = %s", setting, value)
		if err := db.Exec(sql).Error; err != nil {
			log.Printf("Warning: Failed to set %s = %s: %v", setting, value, err)
		} else {
			log.Printf("✅ Set %s = %s", setting, value)
		}
	}

	log.Println("InnoDB optimization settings applied")
	log.Println("Note: Some settings may require MySQL restart to take effect")
	return nil
}

// CreateInnoDBIndexes 为InnoDB表创建推荐的索引
func CreateInnoDBIndexes(db *gorm.DB) error {
	log.Println("Creating recommended indexes for InnoDB tables...")

	// 定义推荐的索引
	indexes := []struct {
		Table string
		Index string
		SQL   string
	}{
		{
			Table: "users",
			Index: "idx_users_email",
			SQL:   "CREATE INDEX idx_users_email ON users(email)",
		},
		{
			Table: "users",
			Index: "idx_users_team_id",
			SQL:   "CREATE INDEX idx_users_team_id ON users(team_id)",
		},
		{
			Table: "tickets",
			Index: "idx_tickets_status",
			SQL:   "CREATE INDEX idx_tickets_status ON tickets(status)",
		},
		{
			Table: "tickets",
			Index: "idx_tickets_priority",
			SQL:   "CREATE INDEX idx_tickets_priority ON tickets(priority)",
		},
		{
			Table: "tickets",
			Index: "idx_tickets_assigned_to",
			SQL:   "CREATE INDEX idx_tickets_assigned_to ON tickets(assigned_to)",
		},
		{
			Table: "tickets",
			Index: "idx_tickets_created_at",
			SQL:   "CREATE INDEX idx_tickets_created_at ON tickets(created_at)",
		},
		{
			Table: "comments",
			Index: "idx_comments_ticket_id",
			SQL:   "CREATE INDEX idx_comments_ticket_id ON comments(ticket_id)",
		},
		{
			Table: "ssh_connections",
			Index: "idx_ssh_connections_user_id",
			SQL:   "CREATE INDEX idx_ssh_connections_user_id ON ssh_connections(user_id)",
		},
		{
			Table: "ssh_sessions",
			Index: "idx_ssh_sessions_connection_id",
			SQL:   "CREATE INDEX idx_ssh_sessions_connection_id ON ssh_sessions(connection_id)",
		},
	}

	for _, idx := range indexes {
		log.Printf("Creating index %s on table %s...", idx.Index, idx.Table)

		if err := db.Exec(idx.SQL).Error; err != nil {
			// 索引可能已存在，这不是错误
			if fmt.Sprintf("%v", err) != "Error 1061: Duplicate key name '"+idx.Index+"'" {
				log.Printf("Warning: Failed to create index %s: %v", idx.Index, err)
			} else {
				log.Printf("Index %s already exists", idx.Index)
			}
		} else {
			log.Printf("✅ Created index %s", idx.Index)
		}
	}

	log.Println("Index creation completed")
	return nil
}
