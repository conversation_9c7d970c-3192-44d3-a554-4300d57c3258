-- ReqFlow Database Export with InnoDB Engine
-- Generated for converting from MyISAM to InnoDB
-- Date: 2025-01-30

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for comments
-- ----------------------------
DROP TABLE IF EXISTS `comments`;
CREATE TABLE `comments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `ticket_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `content` longtext,
  `is_internal` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_comments_deleted_at` (`deleted_at`),
  <PERSON>EY `idx_comments_ticket_id` (`ticket_id`),
  <PERSON><PERSON>Y `idx_comments_user_id` (`user_id`),
  <PERSON><PERSON><PERSON> `idx_comments_created_at` (`created_at`),
  CONSTRAINT `fk_comments_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for invite_codes
-- ----------------------------
DROP TABLE IF EXISTS `invite_codes`;
CREATE TABLE `invite_codes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `code` varchar(191) NOT NULL,
  `role_type` varchar(191) DEFAULT NULL,
  `team_name` varchar(191) DEFAULT NULL,
  `max_uses` bigint DEFAULT '1',
  `used_count` bigint DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `expires_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_invite_codes_code` (`code`),
  KEY `idx_invite_codes_deleted_at` (`deleted_at`),
  KEY `idx_invite_codes_code` (`code`),
  KEY `idx_invite_codes_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for ssh_connections
-- ----------------------------
DROP TABLE IF EXISTS `ssh_connections`;
CREATE TABLE `ssh_connections` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `name` varchar(191) DEFAULT NULL,
  `host` varchar(191) DEFAULT NULL,
  `port` bigint DEFAULT '22',
  `username` varchar(191) DEFAULT NULL,
  `password` varchar(191) DEFAULT NULL,
  `private_key` longtext,
  `description` longtext,
  PRIMARY KEY (`id`),
  KEY `idx_ssh_connections_deleted_at` (`deleted_at`),
  KEY `idx_ssh_connections_user_id` (`user_id`),
  KEY `idx_ssh_connections_host` (`host`),
  CONSTRAINT `fk_ssh_connections_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for ssh_sessions
-- ----------------------------
DROP TABLE IF EXISTS `ssh_sessions`;
CREATE TABLE `ssh_sessions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `connection_id` bigint unsigned DEFAULT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `session_id` varchar(191) DEFAULT NULL,
  `start_time` datetime(3) DEFAULT NULL,
  `end_time` datetime(3) DEFAULT NULL,
  `status` varchar(191) DEFAULT 'active',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_ssh_sessions_session_id` (`session_id`),
  KEY `idx_ssh_sessions_deleted_at` (`deleted_at`),
  KEY `idx_ssh_sessions_connection_id` (`connection_id`),
  KEY `idx_ssh_sessions_user_id` (`user_id`),
  KEY `idx_ssh_sessions_status` (`status`),
  CONSTRAINT `fk_ssh_sessions_connection` FOREIGN KEY (`connection_id`) REFERENCES `ssh_connections` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ssh_sessions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for teams
-- ----------------------------
DROP TABLE IF EXISTS `teams`;
CREATE TABLE `teams` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `description` longtext,
  `is_active` tinyint(1) DEFAULT '1',
  `created_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_teams_name` (`name`),
  KEY `idx_teams_deleted_at` (`deleted_at`),
  KEY `idx_teams_name` (`name`),
  KEY `idx_teams_is_active` (`is_active`),
  KEY `idx_teams_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for ticket_transfers
-- ----------------------------
DROP TABLE IF EXISTS `ticket_transfers`;
CREATE TABLE `ticket_transfers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `ticket_id` bigint unsigned DEFAULT NULL,
  `from_user_id` bigint unsigned DEFAULT NULL,
  `to_user_id` bigint unsigned DEFAULT NULL,
  `reason` longtext,
  `transferred_by` bigint unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ticket_transfers_deleted_at` (`deleted_at`),
  KEY `idx_ticket_transfers_ticket_id` (`ticket_id`),
  KEY `idx_ticket_transfers_from_user_id` (`from_user_id`),
  KEY `idx_ticket_transfers_to_user_id` (`to_user_id`),
  CONSTRAINT `fk_ticket_transfers_ticket` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_ticket_transfers_from_user` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ticket_transfers_to_user` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ticket_transfers_transferred_by` FOREIGN KEY (`transferred_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for tickets
-- ----------------------------
DROP TABLE IF EXISTS `tickets`;
CREATE TABLE `tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `title` varchar(191) NOT NULL,
  `description` longtext,
  `status` varchar(191) DEFAULT 'open',
  `priority` varchar(191) DEFAULT 'default',
  `created_by` bigint unsigned DEFAULT NULL,
  `assigned_to` bigint unsigned DEFAULT NULL,
  `team_id` bigint unsigned DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `due_date` datetime(3) DEFAULT NULL,
  `resolved_at` datetime(3) DEFAULT NULL,
  `closed_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_tickets_deleted_at` (`deleted_at`),
  KEY `idx_tickets_status` (`status`),
  KEY `idx_tickets_priority` (`priority`),
  KEY `idx_tickets_created_by` (`created_by`),
  KEY `idx_tickets_assigned_to` (`assigned_to`),
  KEY `idx_tickets_team_id` (`team_id`),
  KEY `idx_tickets_created_at` (`created_at`),
  KEY `idx_tickets_due_date` (`due_date`),
  CONSTRAINT `fk_tickets_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_tickets_assigned_to` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_tickets_team` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `username` varchar(191) NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `email` varchar(191) DEFAULT NULL,
  `password` varchar(191) NOT NULL,
  `role` varchar(191) DEFAULT 'user',
  `team_id` bigint unsigned DEFAULT NULL,
  `invite_code_id` bigint unsigned DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_users_username` (`username`),
  UNIQUE KEY `uni_users_email` (`email`),
  KEY `idx_users_deleted_at` (`deleted_at`),
  KEY `idx_users_username` (`username`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_role` (`role`),
  KEY `idx_users_team_id` (`team_id`),
  KEY `idx_users_is_active` (`is_active`),
  CONSTRAINT `fk_users_team` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_users_invite_code` FOREIGN KEY (`invite_code_id`) REFERENCES `invite_codes` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Initial Data for teams
-- ----------------------------
INSERT INTO `teams` (`id`, `created_at`, `updated_at`, `deleted_at`, `name`, `description`, `is_active`, `created_by`) VALUES
(1, NOW(), NOW(), NULL, '管理员团队', '系统管理员默认团队', 1, 1),
(2, NOW(), NOW(), NULL, '开发团队', '开发人员默认团队', 1, 1);

-- ----------------------------
-- Initial Data for invite_codes
-- ----------------------------
INSERT INTO `invite_codes` (`id`, `created_at`, `updated_at`, `deleted_at`, `code`, `role_type`, `team_name`, `max_uses`, `used_count`, `is_active`, `expires_at`) VALUES
(1, NOW(), NOW(), NULL, 'ADMIN2024INIT', 'admin', 'System Admin', 1, 1, 1, NULL),
(2, NOW(), NOW(), NULL, 'ADMIN2024', 'admin', '管理员团队', 100, 0, 1, NULL),
(3, NOW(), NOW(), NULL, 'DEV2024', 'developer', '开发团队', 100, 1, 1, NULL),
(4, NOW(), NOW(), NULL, 'USER2024', 'user', '管理员团队', 100, 0, 1, NULL);

-- ----------------------------
-- Initial Data for users
-- ----------------------------
INSERT INTO `users` (`id`, `created_at`, `updated_at`, `deleted_at`, `username`, `name`, `email`, `password`, `role`, `team_id`, `invite_code_id`, `is_active`, `last_login_at`) VALUES
(1, NOW(), NOW(), NULL, 'admin', '系统管理员', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 1, 1, NULL),
(2, NOW(), NOW(), NULL, 'dev01', '张三', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'developer', 2, 3, 1, NULL);

-- ----------------------------
-- Reset AUTO_INCREMENT values
-- ----------------------------
ALTER TABLE `teams` AUTO_INCREMENT = 3;
ALTER TABLE `invite_codes` AUTO_INCREMENT = 5;
ALTER TABLE `users` AUTO_INCREMENT = 3;
ALTER TABLE `tickets` AUTO_INCREMENT = 1;
ALTER TABLE `comments` AUTO_INCREMENT = 1;
ALTER TABLE `ticket_transfers` AUTO_INCREMENT = 1;
ALTER TABLE `ssh_connections` AUTO_INCREMENT = 1;
ALTER TABLE `ssh_sessions` AUTO_INCREMENT = 1;

SET FOREIGN_KEY_CHECKS = 1;

-- ----------------------------
-- Post-installation notes
-- ----------------------------
-- Default admin user:
--   Username: admin
--   Password: admin123456
--
-- Default developer user:
--   Username: dev01
--   Password: dev123456
--
-- Available invite codes:
--   ADMIN2024    - Admin role
--   DEV2024      - Developer role
--   USER2024     - User role
--
-- All tables are now using InnoDB engine with:
--   - Foreign key constraints
--   - Row-level locking
--   - Transaction support
--   - Better performance for concurrent access
