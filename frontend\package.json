{"name": "reqflow-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tinymce/tinymce-vue": "^6.2.0", "@vueup/vue-quill": "^1.2.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "axios": "^1.11.0", "element-plus": "^2.10.4", "pinia": "^3.0.3", "quill": "^2.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^5.1.4", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "terser": "^5.43.1", "typescript": "~5.8.0", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}