<template>
  <div class="system-management">
    <div class="page-header">
      <h2>系统管理</h2>
      <el-button type="primary" @click="refreshData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <el-row :gutter="20">
      <!-- 系统概览 -->
      <el-col :span="24">
        <el-card class="overview-card">
          <template #header>
            <div class="card-header">
              <span>系统概览</span>
              <el-tag :type="systemHealth?.status === 'healthy' ? 'success' : 'danger'">
                {{ systemHealth?.status === 'healthy' ? '运行正常' : '异常' }}
              </el-tag>
            </div>
          </template>

          <el-row :gutter="20" v-if="systemInfo">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ systemInfo.server_info.hostname }}</div>
                <div class="stat-label">服务器名称</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ formatDuration(systemInfo.server_info.uptime) }}</div>
                <div class="stat-label">运行时间</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ systemInfo.server_info.platform }}</div>
                <div class="stat-label">系统版本</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- CPU信息 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Cpu /></el-icon>
              <span>CPU</span>
            </div>
          </template>

          <div v-if="systemInfo">
            <div class="progress-item">
              <div class="progress-header">
                <span>使用率</span>
                <span>{{ formatPercent(systemInfo.hardware.cpu.usage) }}%</span>
              </div>
              <el-progress
                :percentage="formatPercent(systemInfo.hardware.cpu.usage)"
                :color="getUsageColor(systemInfo.hardware.cpu.usage)"
              />
            </div>

            <div class="info-list">
              <div class="info-item">
                <span>型号:</span>
                <span>{{ systemInfo.hardware.cpu.model_name }}</span>
              </div>
              <div class="info-item">
                <span>核心数:</span>
                <span>{{ systemInfo.hardware.cpu.cores }}</span>
              </div>
              <div class="info-item" v-if="systemInfo.hardware.cpu.load_avg?.length">
                <span>负载:</span>
                <span>{{ systemInfo.hardware.cpu.load_avg.map(v => v.toFixed(2)).join(', ') }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 内存信息 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>内存</span>
            </div>
          </template>

          <div v-if="systemInfo">
            <div class="progress-item">
              <div class="progress-header">
                <span>使用率</span>
                <span>{{ formatPercent(systemInfo.hardware.memory.used_percent) }}%</span>
              </div>
              <el-progress
                :percentage="formatPercent(systemInfo.hardware.memory.used_percent)"
                :color="getUsageColor(systemInfo.hardware.memory.used_percent)"
              />
            </div>

            <div class="info-list">
              <div class="info-item">
                <span>总计:</span>
                <span>{{ formatBytes(systemInfo.hardware.memory.total) }}</span>
              </div>
              <div class="info-item">
                <span>已用:</span>
                <span>{{ formatBytes(systemInfo.hardware.memory.used) }}</span>
              </div>
              <div class="info-item">
                <span>可用:</span>
                <span>{{ formatBytes(systemInfo.hardware.memory.available) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 磁盘信息 -->
      <el-col :span="8">
        <el-card>
          <template #header>
            <div class="card-header">
              <el-icon><FolderOpened /></el-icon>
              <span>磁盘</span>
            </div>
          </template>

          <div v-if="systemInfo">
            <div class="progress-item">
              <div class="progress-header">
                <span>使用率</span>
                <span>{{ formatPercent(systemInfo.hardware.disk.used_percent) }}%</span>
              </div>
              <el-progress
                :percentage="formatPercent(systemInfo.hardware.disk.used_percent)"
                :color="getUsageColor(systemInfo.hardware.disk.used_percent)"
              />
            </div>

            <div class="info-list">
              <div class="info-item">
                <span>路径:</span>
                <span>{{ systemInfo.hardware.disk.path }}</span>
              </div>
              <div class="info-item">
                <span>总计:</span>
                <span>{{ formatBytes(systemInfo.hardware.disk.total) }}</span>
              </div>
              <div class="info-item">
                <span>已用:</span>
                <span>{{ formatBytes(systemInfo.hardware.disk.used) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Cpu, Monitor, FolderOpened } from '@element-plus/icons-vue'
import * as systemApi from '@/api/system'
import type { SystemInfo } from '@/api/system'

// 响应式数据
const loading = ref(false)
const systemInfo = ref<SystemInfo | null>(null)
const systemHealth = ref<any>(null)

// 格式化百分比（保留2位小数）
const formatPercent = (value: number): number => {
  return parseFloat(value.toFixed(2))
}

// 获取使用率颜色
const getUsageColor = (usage: number) => {
  if (usage < 50) return '#67c23a'
  if (usage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 格式化字节数
const formatBytes = systemApi.formatBytes

// 格式化时间间隔
const formatDuration = systemApi.formatDuration

// 加载系统信息
const loadSystemInfo = async () => {
  try {
    const response = await systemApi.getSystemInfo()
    systemInfo.value = response.data
  } catch (error) {
    console.error('获取系统信息失败:', error)
    ElMessage.error('获取系统信息失败')
  }
}

// 加载系统健康状态
const loadSystemHealth = async () => {
  try {
    const response = await systemApi.getSystemHealth()
    systemHealth.value = response.data
  } catch (error) {
    console.error('获取系统健康状态失败:', error)
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSystemInfo(),
      loadSystemHealth()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.system-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.overview-card .stat-item {
  text-align: center;
  padding: 20px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.progress-item {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.progress-header span:first-child {
  color: #606266;
}

.progress-header span:last-child {
  font-weight: 600;
  color: #303133;
}

.info-list {
  margin-top: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item span:first-child {
  color: #909399;
  font-weight: 500;
}

.info-item span:last-child {
  color: #303133;
  font-weight: 600;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-progress-bar__outer) {
  background-color: #f0f2f5;
}
</style>
