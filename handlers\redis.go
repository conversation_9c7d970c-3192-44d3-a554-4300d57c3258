package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"ReqFlow/models"
	"ReqFlow/services"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

var redisService = services.NewRedisService()

// CreateRedisConnection 创建Redis连接
func CreateRedisConnection(c *gin.Context) {
	var conn models.RedisConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}
	conn.UserID = userID.(uint)
	fmt.Printf("DEBUG: 当前用户ID: %d\n", conn.UserID)

	// 验证用户是否存在
	db := c.MustGet("db").(*gorm.DB)
	var user models.User
	if err := db.Where("id = ?", conn.UserID).First(&user).Error; err != nil {
		fmt.Printf("DEBUG: 用户查询失败: %v\n", err)
		// 查询所有用户来调试
		var allUsers []models.User
		db.Find(&allUsers)
		fmt.Printf("DEBUG: 数据库中的所有用户: %+v\n", allUsers)
		utils.ErrorResponse(c, http.StatusBadRequest, "用户不存在", "用户ID: "+fmt.Sprintf("%d", conn.UserID))
		return
	}
	fmt.Printf("DEBUG: 找到用户: %+v\n", user)

	// 测试连接
	if err := redisService.TestConnection(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "连接测试失败", err.Error())
		return
	}

	// 保存到数据库
	if err := db.Create(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "创建连接失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "连接创建成功", conn)
}

// GetRedisConnections 获取Redis连接列表
func GetRedisConnections(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var connections []models.RedisConnection

	if err := db.Where("user_id = ?", userID).Find(&connections).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取连接列表失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取连接列表成功", connections)
}

// UpdateRedisConnection 更新Redis连接
func UpdateRedisConnection(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var conn models.RedisConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)

	// 检查连接是否存在且属于当前用户
	var existingConn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&existingConn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	// 测试新的连接配置
	conn.ID = uint(id)
	if err := redisService.TestConnection(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "连接测试失败", err.Error())
		return
	}

	// 更新连接
	conn.UserID = userID.(uint)
	if err := db.Model(&existingConn).Updates(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "更新连接失败", err.Error())
		return
	}

	// 关闭旧的客户端连接
	redisService.CloseClient(uint(id))

	utils.SuccessResponse(c, "连接更新成功", conn)
}

// DeleteRedisConnection 删除Redis连接
func DeleteRedisConnection(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)

	// 检查连接是否存在且属于当前用户
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	// 删除连接
	if err := db.Delete(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "删除连接失败", err.Error())
		return
	}

	// 关闭客户端连接
	redisService.CloseClient(uint(id))

	utils.SuccessResponse(c, "连接删除成功", nil)
}

// TestRedisConnection 测试Redis连接
func TestRedisConnection(c *gin.Context) {
	var conn models.RedisConnection
	if err := c.ShouldBindJSON(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	if err := redisService.TestConnection(&conn); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "连接测试失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "连接测试成功", nil)
}

// GetRedisInfo 获取Redis服务器信息
func GetRedisInfo(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	info, err := redisService.GetInfo(&conn)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取Redis信息失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取Redis信息成功", info)
}

// GetRedisDatabases 获取Redis数据库列表
func GetRedisDatabases(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	databases, err := redisService.GetDatabases(&conn)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取数据库列表失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取数据库列表成功", databases)
}

// GetRedisKeys 获取Redis键列表
func GetRedisKeys(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	// 获取查询参数
	pattern := c.DefaultQuery("pattern", "*")
	cursor, _ := strconv.ParseUint(c.DefaultQuery("cursor", "0"), 10, 64)
	count, _ := strconv.ParseInt(c.DefaultQuery("count", "100"), 10, 64)

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	keys, nextCursor, err := redisService.GetKeys(&conn, pattern, cursor, count)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取键列表失败", err.Error())
		return
	}

	result := map[string]interface{}{
		"keys":   keys,
		"cursor": nextCursor,
	}

	utils.SuccessResponse(c, "获取键列表成功", result)
}

// GetRedisValue 获取Redis键值
func GetRedisValue(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	key := c.Param("key")
	if key == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "键名不能为空")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	value, err := redisService.GetValue(&conn, key)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "获取键值失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "获取键值成功", value)
}

// SetRedisValue 设置Redis键值
func SetRedisValue(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var req struct {
		Key   string      `json:"key" binding:"required"`
		Type  string      `json:"type" binding:"required"`
		Value interface{} `json:"value" binding:"required"`
		TTL   int64       `json:"ttl"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	var ttl time.Duration
	if req.TTL > 0 {
		ttl = time.Duration(req.TTL) * time.Second
	}

	if err := redisService.SetValue(&conn, req.Key, req.Type, req.Value, ttl); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "设置键值失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "设置键值成功", nil)
}

// DeleteRedisKey 删除Redis键
func DeleteRedisKey(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	key := c.Param("key")
	if key == "" {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "键名不能为空")
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	if err := redisService.DeleteKey(&conn, key); err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "删除键失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "删除键成功", nil)
}

// ExecuteRedisCommand 执行Redis命令
func ExecuteRedisCommand(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var req struct {
		Command []interface{} `json:"command" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	result, err := redisService.ExecuteCommand(&conn, req.Command)
	if err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "执行命令失败", err.Error())
		return
	}

	utils.SuccessResponse(c, "执行命令成功", result)
}

// SwitchRedisDatabase 切换Redis数据库
func SwitchRedisDatabase(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", "无效的连接ID")
		return
	}

	var req struct {
		Database int `json:"database" binding:"min=0,max=15"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ErrorResponse(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "未授权", "无法获取用户信息")
		return
	}

	db := c.MustGet("db").(*gorm.DB)
	var conn models.RedisConnection
	if err := db.Where("id = ? AND user_id = ?", id, userID).First(&conn).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.ErrorResponse(c, http.StatusNotFound, "连接不存在", "")
			return
		}
		utils.ErrorResponse(c, http.StatusInternalServerError, "查询连接失败", err.Error())
		return
	}

	// 更新连接的数据库索引
	conn.Database = req.Database
	if err := db.Save(&conn).Error; err != nil {
		utils.ErrorResponse(c, http.StatusInternalServerError, "更新数据库索引失败", err.Error())
		return
	}

	// 关闭旧的客户端连接，强制重新连接到新数据库
	redisService.CloseClient(uint(id))

	utils.SuccessResponse(c, "切换数据库成功", conn)
}
