import api from './index'

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  invite_code: string
}

export interface User {
  id: number
  username: string
  email: string
  role: string
  avatar?: string
  is_active: boolean
  team_name: string
  created_at: string
}

// 登录
export const login = (data: LoginRequest) => {
  return api.post('/login', data)
}

// 注册
export const register = (data: RegisterRequest) => {
  return api.post('/register', data)
}

// 获取用户信息
export const getProfile = () => {
  return api.get('/profile')
}

// 获取开发者列表
export const getDevelopers = () => {
  return api.get('/developers')
}

// 获取用户列表（管理员）
export const getUsers = () => {
  return api.get('/users')
}

// 创建用户（管理员）
export const createUser = (data: {
  username: string
  email: string
  password: string
  role: string
}) => {
  return api.post('/users', data)
}

// 更新用户（管理员）
export const updateUser = (id: number, data: {
  name: string
  email: string
  password?: string
  role: string
}) => {
  return api.put(`/users/${id}`, data)
}

// 删除用户（管理员）
export const deleteUser = (id: number) => {
  return api.delete(`/users/${id}`)
}
