package models

import (
	"time"

	"gorm.io/gorm"
)

// UserRole 用户角色枚举
type UserRole string

const (
	RoleAdmin     UserRole = "admin"
	RoleDeveloper UserRole = "developer"
	RoleUser      UserRole = "user"
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Username     string         `json:"username" gorm:"uniqueIndex;not null;size:50"`
	Name         string         `json:"name" gorm:"not null;size:100"`
	Email        string         `json:"email" gorm:"uniqueIndex;not null;size:100"`
	Password     string         `json:"-" gorm:"not null"`
	Role         UserRole       `json:"role" gorm:"not null;default:'user'"`
	Avatar       string         `json:"avatar" gorm:"size:255"`
	IsActive     bool           `json:"is_active" gorm:"default:true"`
	TeamID       uint           `json:"team_id" gorm:"not null"`
	InviteCodeID uint           `json:"invite_code_id" gorm:"not null"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Team       *Team       `json:"team,omitempty" gorm:"foreignKey:TeamID"`
	InviteCode *InviteCode `json:"invite_code,omitempty" gorm:"foreignKey:InviteCodeID"`
	Tickets    []Ticket    `json:"tickets,omitempty" gorm:"foreignKey:CreatorID"`
	Comments   []Comment   `json:"comments,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// HasPermission 检查用户是否有特定权限
func (u *User) HasPermission(action string, ticket *Ticket) bool {
	switch action {
	case "create_ticket":
		return true // 所有用户都可以创建工单
	case "view_ticket":
		return u.canViewTicket(ticket)
	case "audit_ticket":
		return u.Role == RoleAdmin
	case "assign_ticket":
		return u.Role == RoleAdmin
	case "update_status":
		return u.canUpdateStatus(ticket)
	case "comment":
		return u.canComment(ticket)
	case "transfer":
		return u.Role == RoleDeveloper && u.isAssignedTo(ticket)
	case "manage_users":
		return u.Role == RoleAdmin
	default:
		return false
	}
}

// canViewTicket 检查是否可以查看工单
func (u *User) canViewTicket(ticket *Ticket) bool {
	if u.Role == RoleAdmin {
		return true // 管理员可以查看所有工单
	}
	if ticket.CreatorID == u.ID {
		return true // 创建者可以查看自己的工单
	}
	if u.Role == RoleDeveloper && u.isAssignedTo(ticket) {
		return true // 开发者可以查看分配给自己的工单
	}
	return false
}

// canUpdateStatus 检查是否可以更新状态
func (u *User) canUpdateStatus(ticket *Ticket) bool {
	if u.Role == RoleAdmin {
		return true // 管理员可以更新任何状态
	}
	if u.Role == RoleDeveloper && u.isAssignedTo(ticket) {
		// 开发者只能更新特定状态
		return ticket.Status == StatusAssigned || ticket.Status == StatusInProgress || ticket.Status == StatusTransferred
	}
	return false
}

// canComment 检查是否可以评论
func (u *User) canComment(ticket *Ticket) bool {
	if u.Role == RoleAdmin {
		return true
	}
	if ticket.CreatorID == u.ID {
		return true // 创建者可以在自己的工单下评论
	}
	if u.Role == RoleDeveloper && u.isAssignedTo(ticket) {
		return true // 分配的开发者可以评论
	}
	return false
}

// isAssignedTo 检查是否被分配到该工单
func (u *User) isAssignedTo(ticket *Ticket) bool {
	for _, assigneeID := range ticket.GetAssigneeIDs() {
		if assigneeID == u.ID {
			return true
		}
	}
	return false
}
