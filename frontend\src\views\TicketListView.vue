<template>
  <div class="ticket-list">
    <div class="page-header">
      <h2>工单列表</h2>
      <el-button type="primary" @click="showCreateDialog = true" :icon="Plus">
        创建工单
      </el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入工单标题"
            clearable
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            style="width: 150px"
          >
            <el-option label="待处理" value="1" />
            <el-option label="已分配" value="2" />
            <el-option label="开发中" value="3" />
            <el-option label="已流转" value="4" />
            <el-option label="已提交" value="5" />
            <el-option label="已退回" value="6" />
            <el-option label="已完成" value="7" />
            <el-option label="已拒绝" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择类型"
            clearable
            style="width: 120px"
          >
            <el-option label="需求" value="requirement" />
            <el-option label="缺陷" value="bug" />
            <el-option label="建议" value="suggestion" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="userStore.user?.role === 'admin' || userStore.user?.role === 'developer'" label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="请选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option label="P0" value="P0" />
            <el-option label="P1" value="P1" />
            <el-option label="P2" value="P2" />
            <el-option label="P3" value="P3" />
            <el-option label="P4" value="P4" />
            <el-option label="P5" value="P5" />
            <el-option label="P6" value="P6" />
            <el-option label="默认" value="default" />
            <el-option label="P7" value="P7" />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="handleReset" :icon="Refresh">
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card>
      <el-table :data="tickets" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)">
              {{ getTypeName(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="userStore.user?.role === 'admin' || userStore.user?.role === 'developer'" prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)">
              {{ getPriorityName(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建者" width="120">
          <template #default="{ row }">
            {{ row.creator?.username }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="$router.push(`/tickets/${row.id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建工单对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建工单"
      width="800px"
      :before-close="handleCloseDialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        label-width="100px"
        size="large"
      >
        <el-form-item
          label="工单标题"
          :error="validationErrors.title"
          :validate-status="validationErrors.title ? 'error' : ''"
        >
          <el-input
            v-model="form.title"
            placeholder="请输入工单标题"
            maxlength="200"
            show-word-limit
            @input="() => validationErrors.title && (validationErrors.title = '')"
          />
        </el-form-item>

        <el-form-item
          label="工单类型"
          :error="validationErrors.type"
          :validate-status="validationErrors.type ? 'error' : ''"
        >
          <el-radio-group
            v-model="form.type"
            @change="() => validationErrors.type && (validationErrors.type = '')"
          >
            <el-radio value="requirement">需求</el-radio>
            <el-radio value="bug">缺陷</el-radio>
            <el-radio value="suggestion">建议</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 优先级由管理员分配工单时设置，创建时不需要选择 -->

        <el-form-item
          label="工单描述"
          :error="validationErrors.description"
          :validate-status="validationErrors.description ? 'error' : ''"
        >
          <div class="editor-container">
            <RichTextEditor
              v-model="form.description"
              placeholder="请输入工单描述..."
              :min-height="200"
              @update:modelValue="() => validationErrors.description && (validationErrors.description = '')"
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseDialog">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            创建工单
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search, Refresh } from '@element-plus/icons-vue'
import { useRoute } from 'vue-router'
import { getTickets, createTicket, type Ticket, type CreateTicketRequest } from '@/api/ticket'
import RichTextEditor from '@/components/RichTextEditor.vue'
import { useUserStore } from '@/stores/user'

const tickets = ref<Ticket[]>([])
const loading = ref(false)
const userStore = useUserStore()

// 创建工单对话框
const showCreateDialog = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const form = ref<CreateTicketRequest>({
  title: '',
  description: '',
  type: 'requirement'
  // priority 由管理员分配时设置
})

// 搜索表单数据
const route = useRoute()
const searchForm = ref({
  title: '',
  status: '',
  type: '',
  priority: '',
  dateRange: null as [string, string] | null
})

// 提取HTML中的纯文本内容
const getTextFromHtml = (html: string): string => {
  const div = document.createElement('div')
  div.innerHTML = html
  return div.textContent || div.innerText || ''
}

// 手动验证状态管理
const validationErrors = ref({
  title: '',
  description: '',
  type: ''
})

onMounted(() => {
  initializeFromRoute()
})

const loadTickets = async (searchParams?: any) => {
  loading.value = true
  try {
    // 构建查询参数
    const params = new URLSearchParams()

    if (searchParams) {
      if (searchParams.title) params.append('title', searchParams.title)
      if (searchParams.status) params.append('status', searchParams.status)
      if (searchParams.type) params.append('type', searchParams.type)
      if (searchParams.priority) params.append('priority', searchParams.priority)
      if (searchParams.dateRange && searchParams.dateRange.length === 2) {
        params.append('start_date', searchParams.dateRange[0])
        params.append('end_date', searchParams.dateRange[1])
      }
    }

    const response = await getTickets(params.toString())
    if (response.code === 200) {
      tickets.value = response.data || []
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
  } finally {
    loading.value = false
  }
}

const getTypeName = (type: string) => {
  const names = {
    requirement: '需求',
    bug: '缺陷',
    suggestion: '建议'
  }
  return names[type] || type
}

const getTypeColor = (type: string) => {
  const colors = {
    requirement: 'primary',
    bug: 'danger',
    suggestion: 'success'
  }
  return colors[type] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getPriorityName = (priority: string) => {
  if (!priority) return '未设置'
  if (priority === 'default') return '默认'
  return priority // P0-P7 直接显示
}

const getPriorityType = (priority: string) => {
  const types = {
    'P0': 'danger',     // 红色 - 最高优先级
    'P1': 'danger',     // 红色
    'P2': 'warning',    // 橙色
    'P3': 'warning',    // 橙色
    'P4': 'primary',    // 蓝色
    'P5': 'primary',    // 蓝色
    'P6': 'info',       // 青色
    'P7': 'info',       // 青色 - 最低优先级
    'default': 'success' // 绿色 - 默认优先级
  }
  return types[priority] || 'primary'
}

const getStatusName = (status: number) => {
  const names = {
    0: '已拒绝',
    1: '待处理',
    2: '已分配',
    3: '开发中',
    4: '已流转',
    5: '已提交',
    6: '已退回',
    7: '已完成'
  }
  return names[status] || '未知'
}

const getStatusType = (status: number) => {
  const types = {
    0: 'danger',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'info',
    5: 'primary',
    6: 'warning',
    7: 'success'
  }
  return types[status] || ''
}

// 清除验证错误
const clearValidationErrors = () => {
  validationErrors.value = {
    title: '',
    description: '',
    type: ''
  }
}

// 手动验证函数
const validateForm = () => {
  let valid = true
  clearValidationErrors()

  // 验证标题
  if (!form.value.title || form.value.title.trim().length < 1) {
    valid = false
    validationErrors.value.title = '请输入工单标题'
  }

  // 验证描述
  if (!form.value.description) {
    valid = false
    validationErrors.value.description = '请输入工单描述'
  } else {
    const textContent = getTextFromHtml(form.value.description)
    if (textContent.length < 1) {
      valid = false
      validationErrors.value.description = '描述内容至少需要 1 个字符'
    }
  }

  // 验证类型
  if (!form.value.type) {
    valid = false
    validationErrors.value.type = '请选择工单类型'
  }

  return { valid }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    // 使用手动验证避免console输出
    const { valid } = validateForm()

    if (!valid) {
      ElMessage.warning('请检查表单输入')
      return
    }

    submitting.value = true

    const response = await createTicket(form.value)
    if (response.code === 200) {
      ElMessage.success('工单创建成功')
      showCreateDialog.value = false
      resetForm()
      await loadTickets() // 重新加载工单列表
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建工单失败:', error)

    // 检查是否是验证错误
    if (error.response && error.response.data && error.response.data.description) {
      const validationErrors = error.response.data.description
      if (Array.isArray(validationErrors) && validationErrors.length > 0) {
        // 处理Error对象或普通对象
        const errorMsg = validationErrors[0].message || validationErrors[0].toString() || '验证失败'
        ElMessage.error(errorMsg)
      } else {
        ElMessage.error('创建工单失败')
      }
    } else {
      ElMessage.error('创建工单失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    title: '',
    description: '',
    type: 'requirement'
    // priority 由管理员分配时设置
  }
  clearValidationErrors()
}

// 搜索方法
const handleSearch = () => {
  loadTickets(searchForm.value)
}

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    title: '',
    status: '',
    type: '',
    priority: '',
    dateRange: null
  }
  loadTickets()
}

// 初始化时检查路由参数
const initializeFromRoute = () => {
  const query = route.query
  if (query.status) {
    searchForm.value.status = query.status as string
    loadTickets(searchForm.value)
  } else {
    loadTickets()
  }
}

// 处理对话框关闭
const handleCloseDialog = async () => {
  if (form.value.title || form.value.description) {
    try {
      await ElMessageBox.confirm(
        '确定要关闭吗？未保存的内容将会丢失。',
        '确认关闭',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
    } catch {
      return // 用户取消关闭
    }
  }

  showCreateDialog.value = false
  resetForm()
}
</script>

<style scoped>
.ticket-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.editor-container {
  width: 100%;
}

.dialog-footer {
  text-align: right;
}
</style>
