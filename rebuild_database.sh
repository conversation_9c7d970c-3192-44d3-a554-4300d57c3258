#!/bin/bash

# ReqFlow Database Rebuild Script with InnoDB Engine
# This script will drop and recreate the database with InnoDB tables

# Database configuration
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="123456"
DB_NAME="req_flow"

echo "🔄 ReqFlow Database Rebuild with InnoDB Engine"
echo "=============================================="
echo ""

# Check if MySQL is running
echo "📋 Checking MySQL connection..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "SELECT 1;" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "❌ Error: Cannot connect to MySQL server"
    echo "Please check your MySQL server and credentials"
    exit 1
fi
echo "✅ MySQL connection successful"
echo ""

# Backup existing database (optional)
echo "💾 Creating backup of existing database..."
mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME > backup_$(date +%Y%m%d_%H%M%S).sql 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Backup created successfully"
else
    echo "⚠️  Warning: Could not create backup (database might not exist)"
fi
echo ""

# Drop existing database
echo "🗑️  Dropping existing database..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "DROP DATABASE IF EXISTS $DB_NAME;"
if [ $? -eq 0 ]; then
    echo "✅ Database dropped successfully"
else
    echo "❌ Error: Failed to drop database"
    exit 1
fi
echo ""

# Create new database
echo "🆕 Creating new database..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
if [ $? -eq 0 ]; then
    echo "✅ Database created successfully"
else
    echo "❌ Error: Failed to create database"
    exit 1
fi
echo ""

# Import InnoDB schema and data
echo "📥 导入InnoDB架构和初始数据..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD $DB_NAME < export_innodb.sql
if [ $? -eq 0 ]; then
    echo "✅ 架构和数据导入成功"
else
    echo "❌ 错误: 导入架构和数据失败"
    exit 1
fi
echo ""

# Verify table engines
echo "🔍 验证表引擎..."
mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASSWORD -e "
USE $DB_NAME;
SELECT 
    TABLE_NAME as 'Table Name',
    ENGINE as 'Engine',
    TABLE_ROWS as 'Rows'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = '$DB_NAME' 
ORDER BY TABLE_NAME;
"

echo ""
echo "🎉 数据库重建完成!"
echo ""
echo "📊 摘要:"
echo "  - 数据库: $DB_NAME"
echo "  - 引擎: InnoDB"
echo "  - 字符集: utf8mb4"
echo "  - 排序规则: utf8mb4_0900_ai_ci"
echo ""
echo "👤 默认用户:"
echo "  管理员:     admin / admin123456"
echo "  开发者: dev01 / dev123456"
echo ""
echo "🎫 邀请码:"
echo "  ADMIN2024  - 管理员角色"
echo "  DEV2024    - 开发者角色"
echo "  USER2024   - 用户角色"
echo ""
echo "✨ InnoDB功能已启用:"
echo "  - 外键约束"
echo "  - 行级锁定"
echo "  - 事务支持"
echo "  - 更好的并发性能"
echo ""
echo "🚀 现在可以启动ReqFlow应用程序了!"
