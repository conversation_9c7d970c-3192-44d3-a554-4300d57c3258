@echo off
setlocal enabledelayedexpansion

REM ReqFlow Database Rebuild Script with InnoDB Engine
REM This script will drop and recreate the database with InnoDB tables

REM Database configuration
set "DB_HOST=localhost"
set "DB_PORT=3306"
set "DB_USER=root"
set "DB_PASSWORD=123456"
set "DB_NAME=req_flow"

echo ReqFlow Database Rebuild with InnoDB Engine
echo ==============================================
echo.

REM Check if MySQL is accessible
echo Checking MySQL connection...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo Error: Cannot connect to MySQL server
    echo Please check your MySQL server and credentials
    pause
    exit /b 1
)
echo MySQL connection successful
echo.

REM Backup existing database (optional)
echo Creating backup of existing database...
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

mysqldump -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% > backup_!timestamp!.sql 2>nul
if errorlevel 1 (
    echo Warning: Could not create backup - database might not exist
) else (
    echo Backup created successfully
)
echo.

REM Drop existing database
echo Dropping existing database...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;"
if errorlevel 1 (
    echo Error: Failed to drop database
    pause
    exit /b 1
)
echo Database dropped successfully
echo.

REM Create new database
echo Creating new database...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
if errorlevel 1 (
    echo 错误: 创建数据库失败
    pause
    exit /b 1
)
echo 数据库创建成功
echo.

REM Import InnoDB schema and data
echo 导入InnoDB架构和初始数据...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < export_innodb.sql
if errorlevel 1 (
    echo 错误: 导入架构和数据失败
    pause
    exit /b 1
)
echo 架构和数据导入成功
echo.

REM Verify table engines
echo 验证表引擎...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%; SELECT TABLE_NAME as 'Table Name', ENGINE as 'Engine', TABLE_ROWS as 'Rows' FROM information_schema.TABLES WHERE TABLE_SCHEMA = '%DB_NAME%' ORDER BY TABLE_NAME;"

echo.
echo 数据库重建完成!
echo.
echo 摘要:
echo   - 数据库: %DB_NAME%
echo   - 引擎: InnoDB
echo   - 字符集: utf8mb4
echo   - 排序规则: utf8mb4_0900_ai_ci
echo.
echo 默认用户:
echo   管理员:     admin / admin123456
echo   开发者: dev01 / dev123456
echo.
echo 邀请码:
echo   ADMIN2024  - 管理员角色
echo   DEV2024    - 开发者角色
echo   USER2024   - 用户角色
echo.
echo InnoDB功能已启用:
echo   - 外键约束
echo   - 行级锁定
echo   - 事务支持
echo   - 更好的并发性能
echo.
echo 现在可以启动ReqFlow应用程序了!
echo.
pause
