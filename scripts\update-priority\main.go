package main

import (
	"ReqFlow/config"
	"ReqFlow/models"
	"log"
)

func main() {
	// 连接数据库
	db := config.InitDatabase()
	if db == nil {
		log.Fatal("数据库连接失败")
	}

	log.Println("开始将P7优先级更新为默认优先级...")

	// 查找所有P7优先级的工单
	var tickets []models.Ticket
	result := db.Where("priority = 'P7'").Find(&tickets)

	if result.Error != nil {
		log.Fatal("查询工单失败:", result.Error)
	}

	if len(tickets) == 0 {
		log.Println("未找到P7优先级的工单")
		return
	}

	log.Printf("找到 %d 个P7优先级的工单需要更新为'默认'优先级", len(tickets))

	// 更新每个工单的优先级
	successCount := 0
	errorCount := 0

	for _, ticket := range tickets {
		// 更新为default
		if err := db.Model(&ticket).Update("priority", models.PriorityDefault).Error; err != nil {
			log.Printf("❌ 更新工单 %d 失败: %v", ticket.ID, err)
			errorCount++
		} else {
			log.Printf("✅ 更新工单 %d: 'P7' -> '默认'", ticket.ID)
			successCount++
		}
	}

	log.Printf("优先级更新完成:")
	log.Printf("  ✅ 成功更新: %d 个工单", successCount)
	log.Printf("  ❌ 更新失败: %d 个工单", errorCount)
	log.Printf("  📊 总计处理: %d 个工单", len(tickets))

	if errorCount == 0 {
		log.Println("🎉 所有P7工单已成功更新为'默认'优先级!")
	}

	// 验证结果
	log.Println("\n验证结果...")

	var defaultCount int64
	db.Model(&models.Ticket{}).Where("priority = 'default'").Count(&defaultCount)
	log.Printf("📊 '默认'优先级工单数: %d", defaultCount)

	var p7Count int64
	db.Model(&models.Ticket{}).Where("priority = 'P7'").Count(&p7Count)
	log.Printf("📊 'P7'优先级工单数: %d", p7Count)
}
