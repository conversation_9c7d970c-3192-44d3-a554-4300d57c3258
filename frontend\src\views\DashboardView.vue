<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>仪表盘</h2>
    </div>

    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <h3>欢迎回来，{{ user?.username }}！</h3>
      <p>您的角色：{{ getRoleName(user?.role) }} | 团队：{{ user?.team_name }}</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ stats.total }}</div>
                <div class="stat-label">总工单数</div>
              </div>
              <el-icon class="stat-icon"><Tickets /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card clickable" @click="navigateToTickets('1')">
              <div class="stat-content">
                <div class="stat-number">{{ stats.pending }}</div>
                <div class="stat-label">待处理</div>
              </div>
              <el-icon class="stat-icon"><Clock /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card clickable" @click="navigateToTickets('3')">
              <div class="stat-content">
                <div class="stat-number">{{ stats.inProgress }}</div>
                <div class="stat-label">开发中</div>
              </div>
              <el-icon class="stat-icon"><Loading /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card clickable" @click="navigateToTickets('7')">
              <div class="stat-content">
                <div class="stat-number">{{ stats.completed }}</div>
                <div class="stat-label">已完成</div>
              </div>
              <el-icon class="stat-icon"><Check /></el-icon>
            </el-card>
          </el-col>
    </el-row>

    <!-- 最近工单 -->
    <el-card class="recent-tickets">
          <template #header>
            <div class="card-header">
              <span>最近工单</span>
              <el-button type="primary" @click="$router.push('/tickets')">
                查看全部
              </el-button>
            </div>
          </template>
          
          <el-table :data="recentTickets" style="width: 100%">
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="type" label="类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getTypeColor(row.type)">
                  {{ getTypeName(row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" width="100">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)">
                  {{ getPriorityName(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusName(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="$router.push(`/tickets/${row.id}`)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Tickets,
  Clock,
  Loading,
  Check
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { getTickets, type Ticket } from '@/api/ticket'

const router = useRouter()
const userStore = useUserStore()

const user = ref(null)
const recentTickets = ref<Ticket[]>([])
const stats = ref({
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0
})

onMounted(async () => {
  await loadUserProfile()
  await loadRecentTickets()
})

// 导航到工单列表页面，带状态筛选
const navigateToTickets = (status: string) => {
  router.push({
    path: '/tickets',
    query: { status }
  })
}

const loadUserProfile = async () => {
  const result = await userStore.fetchProfile()
  if (result.success) {
    user.value = result.data
  }
}

const loadRecentTickets = async () => {
  try {
    const response = await getTickets()
    if (response.code === 200) {
      const tickets = response.data || []
      recentTickets.value = tickets.slice(0, 10) // 只显示最近10条
      
      // 计算统计数据
      stats.value = {
        total: tickets.length,
        pending: tickets.filter((t: Ticket) => t.status === 1).length,
        inProgress: tickets.filter((t: Ticket) => t.status === 3).length,
        completed: tickets.filter((t: Ticket) => t.status === 7).length
      }
    }
  } catch (error) {
    console.error('Failed to load tickets:', error)
  }
}

const handleCommand = (command: string) => {
  if (command === 'logout') {
    userStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } else if (command === 'profile') {
    // TODO: 实现个人信息页面
    ElMessage.info('个人信息功能待实现')
  }
}

const getRoleName = (role: string) => {
  const roleNames = {
    admin: '管理员',
    developer: '开发者',
    user: '普通用户'
  }
  return roleNames[role] || role
}

const getPriorityName = (priority: string) => {
  const names = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  }
  return names[priority] || priority
}

const getPriorityType = (priority: string) => {
  const types = {
    low: 'info',
    medium: '',
    high: 'warning',
    urgent: 'danger'
  }
  return types[priority] || ''
}

const getStatusName = (status: number) => {
  const names = {
    0: '已拒绝',
    1: '审核中',
    2: '已分配',
    3: '开发中',
    4: '已流转',
    5: '已提交',
    6: '已退回',
    7: '已完成'
  }
  return names[status] || '未知'
}

const getStatusType = (status: number) => {
  const types = {
    0: 'danger',
    1: 'warning',
    2: 'primary',
    3: 'success',
    4: 'info',
    5: 'primary',
    6: 'warning',
    7: 'success'
  }
  return types[status] || ''
}

const getTypeName = (type: string) => {
  const names = {
    requirement: '需求',
    bug: '缺陷',
    suggestion: '建议'
  }
  return names[type] || type
}

const getTypeColor = (type: string) => {
  const colors = {
    requirement: 'primary',
    bug: 'danger',
    suggestion: 'success'
  }
  return colors[type] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.welcome-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-section h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.welcome-section p {
  margin: 0;
  color: #606266;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left h1 {
  margin: 0;
  color: #409eff;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 8px;
}

.sidebar {
  background: #f5f5f5;
  border-right: 1px solid #e4e7ed;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.main-content {
  background: #f0f2f5;
  padding: 20px;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-section h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.welcome-section p {
  margin: 0;
  color: #909399;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  color: #e4e7ed;
  z-index: 1;
}

.stat-card.clickable {
  cursor: pointer;
  transition: all 0.3s ease;
}

.stat-card.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recent-tickets {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
