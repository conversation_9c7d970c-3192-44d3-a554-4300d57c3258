# 简化的工单详情测试脚本

Write-Host "=== 工单详情功能测试 ===" -ForegroundColor Green

# 1. 管理员登录
Write-Host "`n1. 管理员登录..." -ForegroundColor Yellow
$loginData = '{"username":"admin","password":"admin123456"}'
$loginResult = Invoke-RestMethod -Uri "http://localhost:8081/api/login" -Method POST -ContentType "application/json" -Body $loginData
$token = $loginResult.data.token
Write-Host "✓ 登录成功" -ForegroundColor Green

# 2. 创建工单
Write-Host "`n2. 创建测试工单..." -ForegroundColor Yellow
$headers = @{"Authorization" = "Bearer $token"; "Content-Type" = "application/json"}
$ticketData = '{"title":"详情页面测试工单","description":"这是一个测试工单，用于验证详情页面功能","type":"requirement","priority":"high"}'
$ticket = Invoke-RestMethod -Uri "http://localhost:8081/api/tickets" -Method POST -Headers $headers -Body $ticketData
$ticketId = $ticket.data.id
Write-Host "✓ 工单创建成功，ID: $ticketId" -ForegroundColor Green

# 3. 添加评论
Write-Host "`n3. 添加评论..." -ForegroundColor Yellow
$commentData = "{`"ticket_id`":$ticketId,`"content`":`"这是一条测试评论`"}"
$comment = Invoke-RestMethod -Uri "http://localhost:8081/api/comments" -Method POST -Headers $headers -Body $commentData
Write-Host "✓ 评论添加成功" -ForegroundColor Green

# 4. 获取工单详情
Write-Host "`n4. 获取工单详情..." -ForegroundColor Yellow
$detail = Invoke-RestMethod -Uri "http://localhost:8081/api/tickets/$ticketId" -Method GET -Headers $headers
Write-Host "✓ 工单详情获取成功" -ForegroundColor Green
Write-Host "  标题: $($detail.data.title)" -ForegroundColor Cyan
Write-Host "  状态: $($detail.data.status)" -ForegroundColor Cyan

# 5. 获取评论
Write-Host "`n5. 获取评论..." -ForegroundColor Yellow
$comments = Invoke-RestMethod -Uri "http://localhost:8081/api/comments/ticket/$ticketId" -Method GET -Headers $headers
Write-Host "✓ 评论获取成功，共 $($comments.data.Count) 条" -ForegroundColor Green

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "访问地址: http://localhost:5173/tickets/$ticketId" -ForegroundColor Cyan
Write-Host "登录账号: admin / admin123456" -ForegroundColor Cyan
