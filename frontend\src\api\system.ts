import api from './index'

// 系统信息接口
export interface SystemInfo {
  server_info: {
    hostname: string
    platform: string
    os: string
    arch: string
    uptime: number
    boot_time: string
    current_time: string
  }
  hardware: {
    cpu: {
      model_name: string
      cores: number
      usage: number
      load_avg: number[]
    }
    memory: {
      total: number
      available: number
      used: number
      used_percent: number
      free: number
      buffers: number
      cached: number
    }
    disk: {
      total: number
      free: number
      used: number
      used_percent: number
      path: string
    }
  }
  software: {
    go_version: string
    gin_version: string
    gorm_version: string
    app_version: string
    build_time: string
    git_commit: string
  }
  runtime: {
    goroutines: number
    mem_stats: {
      alloc: number
      total_alloc: number
      sys: number
      heap_alloc: number
      heap_sys: number
      heap_idle: number
      heap_inuse: number
      heap_released: number
      heap_objects: number
      stack_inuse: number
      stack_sys: number
      next_gc: number
      last_gc: number
    }
    gc_stats: {
      num_gc: number
      pause_total: number
      gc_cpu_fraction: number
    }
    start_time: string
    uptime: number
  }
  database: {
    type: string
    version: string
    host: string
    port: string
    database: string
    max_connections: number
    open_connections: number
    in_use: number
    idle: number
  }
}

// 获取系统信息
export const getSystemInfo = () => {
  return api.get('/system/info')
}

// 获取系统实时统计信息
export const getSystemStats = () => {
  return api.get('/system/stats')
}

// 获取系统健康状态
export const getSystemHealth = () => {
  return api.get('/system/health')
}

// 格式化字节数
export const formatBytes = (bytes: number, decimals = 2): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

// 格式化时间间隔
export const formatDuration = (seconds: number): string => {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  const parts = []
  if (days > 0) parts.push(`${days}天`)
  if (hours > 0) parts.push(`${hours}小时`)
  if (minutes > 0) parts.push(`${minutes}分钟`)
  if (secs > 0 || parts.length === 0) parts.push(`${secs}秒`)

  return parts.join(' ')
}
