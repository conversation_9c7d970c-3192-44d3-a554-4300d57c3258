package config

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type DatabaseConfig struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	Charset  string
}

func NewDatabaseConfig() *DatabaseConfig {
	return &DatabaseConfig{
		Host:     "localhost",
		Port:     "3306",
		User:     "root",
		Password: "123456",
		DBName:   "req_flow",
		Charset:  "utf8mb4",
	}
}

func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local&sql_mode=''",
		c.<PERSON>r, c.Password, c.Host, c.Port, c.DBName, c.Charset)
}

func InitDatabase() *gorm.DB {
	config := NewDatabaseConfig()
	dsn := config.GetDSN()

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 设置默认存储引擎为InnoDB
	db.Exec("SET default_storage_engine=InnoDB")

	// 设置事务隔离级别
	db.Exec("SET SESSION transaction_isolation = 'READ-COMMITTED'")

	log.Println("数据库连接成功，使用InnoDB引擎")
	return db
}
