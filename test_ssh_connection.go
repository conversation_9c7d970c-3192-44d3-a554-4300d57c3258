package main

import (
	"fmt"
	"time"

	"golang.org/x/crypto/ssh"
)

func main() {
	// SSH连接配置
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.Password("Asdty1234"),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	// 连接SSH服务器
	fmt.Println("正在连接到 **************:22...")
	client, err := ssh.Dial("tcp", "**************:22", config)
	if err != nil {
		fmt.Printf("SSH连接失败: %v\n", err)
		return
	}
	defer client.Close()

	fmt.Println("SSH连接成功!")

	// 创建会话
	session, err := client.NewSession()
	if err != nil {
		fmt.Printf("创建会话失败: %v\n", err)
		return
	}
	defer session.Close()

	fmt.Println("会话创建成功!")

	// 执行简单命令
	output, err := session.Output("whoami")
	if err != nil {
		fmt.Printf("执行命令失败: %v\n", err)
		return
	}

	fmt.Printf("命令执行成功，输出: %s\n", string(output))
	fmt.Println("SSH连接测试完成!")
}
