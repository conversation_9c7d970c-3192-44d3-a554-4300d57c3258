# ReqFlow API 测试脚本

$baseUrl = "http://localhost:8081/api"

Write-Host "=== ReqFlow API 测试 ===" -ForegroundColor Green

# 1. 测试健康检查
Write-Host "`n1. 测试健康检查..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8081/health" -Method GET
    Write-Host "✓ 健康检查通过: $($health.message)" -ForegroundColor Green
} catch {
    Write-Host "✗ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 测试管理员登录
Write-Host "`n2. 测试管理员登录..." -ForegroundColor Yellow
try {
    $loginData = @{
        username = "admin"
        password = "admin123456"
    } | ConvertTo-Json

    $loginResult = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body $loginData
    $adminToken = $loginResult.data.token
    Write-Host "✓ 管理员登录成功" -ForegroundColor Green
    Write-Host "  用户ID: $($loginResult.data.user_id)" -ForegroundColor Cyan
    Write-Host "  角色: $($loginResult.data.role)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 管理员登录失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 3. 测试获取用户信息
Write-Host "`n3. 测试获取用户信息..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $adminToken"
        "Content-Type" = "application/json"
    }
    
    $profile = Invoke-RestMethod -Uri "$baseUrl/profile" -Method GET -Headers $headers
    Write-Host "✓ 获取用户信息成功" -ForegroundColor Green
    Write-Host "  用户名: $($profile.data.username)" -ForegroundColor Cyan
    Write-Host "  邮箱: $($profile.data.email)" -ForegroundColor Cyan
    Write-Host "  团队: $($profile.data.team_name)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 获取用户信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试创建工单
Write-Host "`n4. 测试创建工单..." -ForegroundColor Yellow
try {
    $ticketData = @{
        title = "测试工单"
        description = "这是一个测试工单，用于验证系统功能"
        type = "requirement"
        priority = "medium"
    } | ConvertTo-Json

    $ticket = Invoke-RestMethod -Uri "$baseUrl/tickets" -Method POST -Headers $headers -Body $ticketData
    $ticketId = $ticket.data.id
    Write-Host "✓ 创建工单成功" -ForegroundColor Green
    Write-Host "  工单ID: $ticketId" -ForegroundColor Cyan
    Write-Host "  标题: $($ticket.data.title)" -ForegroundColor Cyan
    Write-Host "  状态: $($ticket.data.status)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 创建工单失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 测试获取工单列表
Write-Host "`n5. 测试获取工单列表..." -ForegroundColor Yellow
try {
    $tickets = Invoke-RestMethod -Uri "$baseUrl/tickets" -Method GET -Headers $headers
    Write-Host "✓ 获取工单列表成功" -ForegroundColor Green
    Write-Host "  工单总数: $($tickets.data.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 获取工单列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试用户注册（使用开发者邀请码）
Write-Host "`n6. 测试开发者注册..." -ForegroundColor Yellow
try {
    $registerData = @{
        username = "testdev"
        email = "<EMAIL>"
        password = "testdev123"
        invite_code = "DEV2024DEMO"
    } | ConvertTo-Json

    $registerResult = Invoke-RestMethod -Uri "$baseUrl/register" -Method POST -ContentType "application/json" -Body $registerData
    Write-Host "✓ 开发者注册成功" -ForegroundColor Green
    Write-Host "  用户ID: $($registerResult.data.user_id)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 开发者注册失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. 测试开发者登录
Write-Host "`n7. 测试开发者登录..." -ForegroundColor Yellow
try {
    $devLoginData = @{
        username = "testdev"
        password = "testdev123"
    } | ConvertTo-Json

    $devLoginResult = Invoke-RestMethod -Uri "$baseUrl/login" -Method POST -ContentType "application/json" -Body $devLoginData
    $devToken = $devLoginResult.data.token
    Write-Host "✓ 开发者登录成功" -ForegroundColor Green
    Write-Host "  角色: $($devLoginResult.data.role)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 开发者登录失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 8. 测试管理员功能 - 获取用户列表
Write-Host "`n8. 测试管理员功能 - 获取用户列表..." -ForegroundColor Yellow
try {
    $users = Invoke-RestMethod -Uri "$baseUrl/admin/users" -Method GET -Headers $headers
    Write-Host "✓ 获取用户列表成功" -ForegroundColor Green
    Write-Host "  用户总数: $($users.data.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 获取用户列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 9. 测试管理员功能 - 获取邀请码列表
Write-Host "`n9. 测试管理员功能 - 获取邀请码列表..." -ForegroundColor Yellow
try {
    $inviteCodes = Invoke-RestMethod -Uri "$baseUrl/admin/invite-codes" -Method GET -Headers $headers
    Write-Host "✓ 获取邀请码列表成功" -ForegroundColor Green
    Write-Host "  邀请码总数: $($inviteCodes.data.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 获取邀请码列表失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "系统基本功能正常！" -ForegroundColor Green
Write-Host "`n访问地址:" -ForegroundColor Yellow
Write-Host "  前端: http://localhost:5173" -ForegroundColor Cyan
Write-Host "  后端: http://localhost:8081" -ForegroundColor Cyan
Write-Host "  健康检查: http://localhost:8081/health" -ForegroundColor Cyan
