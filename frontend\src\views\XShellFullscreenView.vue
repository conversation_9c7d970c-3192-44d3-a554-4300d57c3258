<template>
  <div class="xshell-fullscreen">
    <!-- 顶部工具栏 -->
    <div class="fullscreen-header">
      <div class="header-left">
        <h2>XShell 大屏模式</h2>
        <span class="connection-count">{{ connections.length }} 个连接</span>
      </div>
      <div class="header-right">
        <el-button size="small" @click="refreshAll" :loading="refreshing">
          <el-icon><Refresh /></el-icon>
          刷新所有
        </el-button>
        <el-button size="small" @click="toggleMonitor">
          <el-icon><Monitor /></el-icon>
          {{ showMonitor ? '隐藏' : '显示' }}监控
        </el-button>
        <el-button size="small" @click="toggleFilesPanel">
          <el-icon><Folder /></el-icon>
          {{ showFilesPanel ? '隐藏' : '显示' }}文件
        </el-button>
        <el-button size="small" @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          {{ isFullscreen ? '退出全屏' : '全屏模式' }}
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="fullscreen-content">
      <!-- 左侧服务器列表和监控面板 -->
      <div class="left-sidebar">
        <!-- 服务器连接列表 -->
        <div class="connections-section">
          <div class="section-header">
            <h3>服务器列表</h3>
            <el-button size="small" type="primary" @click="goBack">
              <el-icon><Plus /></el-icon>
              管理
            </el-button>
          </div>
          <div class="connections-list">
            <div
              v-for="connection in connections"
              :key="connection.id"
              class="connection-item"
              :class="{ active: activeConnection?.id === connection.id }"
              @click="selectConnection(connection)"
            >
              <div class="connection-info">
                <div class="connection-name">{{ connection.name }}</div>
                <div class="connection-details">{{ connection.username }}@{{ connection.host }}:{{ connection.port }}</div>
              </div>
              <div class="connection-status" :class="getConnectionStatus(connection.id)">
                {{ getConnectionStatusText(connection.id) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 服务器资源监控面板 -->
        <div class="monitor-section" v-if="showMonitor && activeConnection">
          <div class="section-header">
            <h3>{{ activeConnection.name }} 监控</h3>
            <el-button size="small" text @click="refreshServerInfo">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="monitor-content">
            <div class="resource-item">
              <div class="resource-label">CPU使用率</div>
              <el-progress
                :percentage="serverInfo.cpu || 0"
                :color="getProgressColor(serverInfo.cpu || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.cpu || 0).toFixed(2) }}%
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-label">内存使用率</div>
              <el-progress
                :percentage="serverInfo.memory || 0"
                :color="getProgressColor(serverInfo.memory || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.memory || 0).toFixed(2) }}% - {{ (serverInfo.memoryUsed || 0).toFixed(1) }}GB / {{ (serverInfo.memoryTotal || 0).toFixed(1) }}GB
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-label">硬盘使用率</div>
              <el-progress
                :percentage="serverInfo.disk || 0"
                :color="getProgressColor(serverInfo.disk || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.disk || 0).toFixed(2) }}% - {{ (serverInfo.diskUsed || 0).toFixed(1) }}GB / {{ (serverInfo.diskTotal || 0).toFixed(1) }}GB
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间终端区域 -->
      <div class="terminal-area">
        <!-- 连接标签页 -->
        <div class="connection-tabs" v-if="openTabs.length > 0">
          <div
            v-for="tab in openTabs"
            :key="tab.id"
            class="tab-item"
            :class="{ active: activeTabId === tab.id }"
            @click="switchTab(tab.id)"
          >
            <span class="tab-name">{{ tab.connection.name }}</span>
            <span class="tab-status" :class="tab.status">{{ getStatusText(tab.status) }}</span>
            <el-button
              size="small"
              text
              @click.stop="closeTab(tab.id)"
              class="close-btn"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 终端容器 -->
        <div class="terminal-container" v-if="activeTab">
          <div
            class="terminal-wrapper"
            :ref="el => setTerminalRef(el, activeTab.id)"
          ></div>
        </div>

        <!-- 快捷命令栏 -->
        <div class="quick-commands-fullscreen" v-if="activeTab && activeTab.status === 'connected'">
          <div class="quick-commands-header">
            <span>快捷命令:</span>
            <el-button size="small" @click="jumpToFtpPath" v-if="showFilesPanel">
              <el-icon><Files /></el-icon>
              进入XFTP路径
            </el-button>
          </div>
          <div class="quick-command-buttons">
            <el-button
              v-for="cmd in quickCommands"
              :key="cmd.id"
              size="small"
              @click="executeQuickCommand(cmd.command)"
              :title="cmd.description"
            >
              {{ cmd.name }}
            </el-button>
          </div>
        </div>

        <!-- 命令输入区域 -->
        <div class="command-input-fullscreen" v-if="activeTab && activeTab.status === 'connected'">
          <el-input
            v-model="currentCommand"
            placeholder="输入命令..."
            @keydown.enter="executeCommand"
            @keydown.up.prevent="navigateHistory(-1)"
            @keydown.down.prevent="navigateHistory(1)"
            size="large"
          >
            <template #prepend>
              <span class="command-prompt">{{ commandPrompt }}</span>
            </template>
            <template #append>
              <el-button @click="executeCommand" :disabled="!currentCommand.trim()">
                执行
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" v-if="openTabs.length === 0">
          <el-empty description="暂无连接">
            <el-button type="primary" @click="goBack">返回连接列表</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 右侧文件管理面板 -->
      <div class="files-panel" v-if="showFilesPanel && activeConnection">
        <div class="files-header">
          <h3>
            <el-icon><Folder /></el-icon>
            文件管理 - {{ activeConnection.name }}
          </h3>
          <el-button size="small" text @click="showFilesPanel = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>

        <div class="files-container">
          <!-- 面包屑路径 -->
          <div class="breadcrumb-bar">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="(path, index) in breadcrumbPaths"
                :key="index"
                @click="navigateToBreadcrumb(index)"
                class="breadcrumb-item"
              >
                <span v-if="index === 0">根目录</span>
                <span v-else>{{ path.name }}</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <!-- 文件管理工具栏 -->
          <div class="files-toolbar">
            <div class="toolbar-left">
              <el-button size="small" @click="jumpToShellPath">
                <el-icon><Monitor /></el-icon>
                跳转至XShell路径
              </el-button>
            </div>
            <div class="toolbar-right">
              <el-button size="small" @click="refreshFileList" :loading="fileListLoading">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button size="small" @click="showUploadDialog = true">
                <el-icon><Upload /></el-icon>
              </el-button>
              <el-button size="small" @click="showCreateFolderDialog = true">
                <el-icon><FolderAdd /></el-icon>
              </el-button>
            </div>
          </div>

          <!-- 文件列表 -->
          <div class="files-list">
            <el-table 
              :data="fileList" 
              style="width: 100%" 
              @row-dblclick="handleFileDoubleClick"
              :loading="fileListLoading"
              height="100%"
              size="small"
            >
              <el-table-column width="40">
                <template #default="scope">
                  <el-icon v-if="scope.row.isDir" color="#409eff"><Folder /></el-icon>
                  <el-icon v-else color="#909399"><Document /></el-icon>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="文件名" min-width="150">
                <template #default="scope">
                  <span
                    :class="{
                      'file-link': scope.row.isDir,
                      'file-name': true,
                      'is-directory': scope.row.isDir && scope.row.name !== '..',
                      'parent-dir': scope.row.name === '..'
                    }"
                    @click="scope.row.isDir && handleFileClick(scope.row)"
                  >
                    <el-icon v-if="scope.row.isDir && scope.row.name !== '..'" style="margin-right: 4px;">
                      <Folder />
                    </el-icon>
                    <el-icon v-else-if="scope.row.name === '..'" style="margin-right: 4px;">
                      <ArrowUp />
                    </el-icon>
                    <el-icon v-else style="margin-right: 4px;">
                      <Document />
                    </el-icon>
                    {{ scope.row.name }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="大小" width="80">
                <template #default="scope">
                  <span style="color: #909399;">
                    {{ scope.row.isDir ? '-' : formatFileSize(scope.row.size) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button size="small" text @click="downloadFile(scope.row)" v-if="!scope.row.isDir">
                    <el-icon><Download /></el-icon>
                  </el-button>
                  <el-button size="small" text @click="deleteFile(scope.row)" type="danger">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传文件对话框 -->
    <el-dialog v-model="showUploadDialog" title="上传文件" width="500px">
      <el-upload
        ref="uploadRef"
        :action="uploadUrl"
        :headers="uploadHeaders"
        :data="uploadData"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        multiple
        drag
      >
        <el-icon class="el-icon--upload"><Upload /></el-icon>
        <div class="el-upload__text">
          将文件拖到此处，或<em>点击上传</em>
        </div>
      </el-upload>
      <template #footer>
        <el-button @click="showUploadDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog v-model="showCreateFolderDialog" title="新建文件夹" width="400px">
      <el-form>
        <el-form-item label="文件夹名称">
          <el-input v-model="newFolderName" placeholder="请输入文件夹名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">创建</el-button>
      </template>
    </el-dialog>

    <!-- 新建/编辑连接对话框 -->
    <el-dialog
      v-model="showConnectionDialog"
      :title="editingConnection ? '编辑SSH连接' : '新建SSH连接'"
      width="600px"
      @close="resetConnectionForm"
    >
      <el-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        label-width="100px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="connectionForm.name" placeholder="请输入连接名称" />
        </el-form-item>

        <el-form-item label="主机地址" prop="host">
          <el-input v-model="connectionForm.host" placeholder="请输入主机地址或IP" />
        </el-form-item>

        <el-form-item label="端口" prop="port">
          <el-input-number v-model="connectionForm.port" :min="1" :max="65535" />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="connectionForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="认证方式">
          <el-radio-group v-model="authType">
            <el-radio label="password">密码认证</el-radio>
            <el-radio label="key">私钥认证</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="authType === 'password'" label="密码" prop="password">
          <el-input
            v-model="connectionForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item v-if="authType === 'key'" label="私钥" prop="private_key">
          <el-input
            v-model="connectionForm.private_key"
            type="textarea"
            :rows="6"
            placeholder="请粘贴私钥内容"
          />
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="connectionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showConnectionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConnection" :loading="saving">
          {{ editingConnection ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Refresh,
  Monitor,
  Close,
  Setting,
  Plus,
  FullScreen,
  Folder,
  Document,
  Upload,
  Download,
  Delete,
  FolderAdd,
  ArrowUp
} from '@element-plus/icons-vue'

// 导入API和工具
import { getSSHConnections, getServerInfo, SSHWebSocket, createSSHConnection, updateSSHConnection } from '@/api/ssh'
import {
  getSFTPFileList,
  downloadFile as downloadFileAPI,
  createFolder as createFolderAPI,
  deleteFile as deleteFileAPI,
  type FileInfo
} from '@/api/sftp'

// 导入类型
interface SSHConnection {
  id?: number
  name: string
  host: string
  port: number
  username: string
  password?: string
  private_key?: string
  description?: string
  is_active?: boolean
  created_at?: string
  updated_at?: string
  user?: any
}

interface TabInfo {
  id: string
  connection: SSHConnection
  terminal: any
  webSocket: SSHWebSocket | null
  status: 'disconnected' | 'connecting' | 'connected' | 'error'
}

const router = useRouter()

// 响应式数据
const connections = ref<SSHConnection[]>([])
const activeConnection = ref<SSHConnection | null>(null)
const openTabs = ref<TabInfo[]>([])
const activeTabId = ref<string>('')
const terminalRefs = ref<Map<string, HTMLElement>>(new Map())
const showMonitor = ref(true)
const refreshing = ref(false)
const showFilesPanel = ref(true)
const isFullscreen = ref(false)

// 文件管理相关
const fileList = ref<any[]>([])
const fileListLoading = ref(false)
const currentPath = ref('/')
const breadcrumbPaths = ref([{ name: '/', fullPath: '/' }])
const currentShellPath = ref('/') // 当前Shell工作目录
const showUploadDialog = ref(false)
const showCreateFolderDialog = ref(false)
const newFolderName = ref('')
const uploadUrl = ref('')
const uploadHeaders = ref({})
const uploadData = ref({})

// 连接管理相关
const showConnectionDialog = ref(false)
const editingConnection = ref<SSHConnection | null>(null)
const saving = ref(false)
const authType = ref('password')
const connectionFormRef = ref()
const connectionForm = reactive<SSHConnection>({
  name: '',
  host: '',
  port: 22,
  username: '',
  password: '',
  private_key: '',
  description: ''
})

// 表单验证规则
const connectionRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' },
    { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 快捷命令相关
const quickCommands = ref([
  { id: 1, name: 'CPU信息', command: 'cat /proc/cpuinfo | grep "model name" | head -1', description: '查看CPU信息' },
  { id: 2, name: '内存信息', command: 'free -h', description: '查看内存使用情况' },
  { id: 3, name: '硬盘信息', command: 'df -h', description: '查看硬盘使用情况' },
  { id: 4, name: '系统负载', command: 'uptime', description: '查看系统负载' },
  { id: 5, name: '进程列表', command: 'ps aux | head -20', description: '查看进程列表' },
  { id: 6, name: '网络连接', command: 'netstat -tuln', description: '查看网络连接' },
  { id: 7, name: '系统信息', command: 'uname -a', description: '查看系统信息' },
  { id: 8, name: '当前目录', command: 'pwd', description: '显示当前目录' }
])

// 服务器资源监控
const serverInfo = ref({
  cpu: 0,
  memory: 0,
  memoryUsed: 0,
  memoryTotal: 0,
  disk: 0,
  diskUsed: 0,
  diskTotal: 0,
  load1: '0.00',
  load5: '0.00',
  load15: '0.00'
})

// 命令输入相关
const currentCommand = ref('')
const commandHistory = ref<string[]>([])
const historyIndex = ref(-1)
const commandPrompt = ref('$ ')

// 计算属性
const activeTab = computed(() => {
  return openTabs.value.find(tab => tab.id === activeTabId.value) || null
})

// 生命周期钩子
onMounted(async () => {
  await restoreState()
  document.body.classList.add('fullscreen-mode')
})

onUnmounted(() => {
  openTabs.value.forEach(tab => {
    if (tab.webSocket) {
      tab.webSocket.close()
    }
    if (tab.terminal) {
      try {
        tab.terminal.dispose()
      } catch (error) {
        console.warn('Terminal dispose error:', error)
      }
    }
  })
  document.body.classList.remove('fullscreen-mode')
})

// 方法定义
const restoreState = async () => {
  try {
    const savedState = localStorage.getItem('xshell-fullscreen-state')
    if (savedState) {
      const state = JSON.parse(savedState)
      console.log('恢复状态:', state)
      
      if (state.connections) {
        connections.value = state.connections
      }
      
      if (state.serverInfo) {
        serverInfo.value = state.serverInfo
      }
      
      if (state.activeConnection) {
        activeConnection.value = state.activeConnection
      }
      
      if (state.openTabs && state.openTabs.length > 0) {
        openTabs.value = state.openTabs.map(tab => ({
          id: 'restored-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
          connection: tab.connection,
          terminal: null,
          webSocket: null,
          status: 'disconnected'
        }))
        
        if (openTabs.value.length > 0) {
          activeTabId.value = openTabs.value[0].id
          activeConnection.value = openTabs.value[0].connection
        }
      }
      
      localStorage.removeItem('xshell-fullscreen-state')
    }
    
    if (connections.value.length === 0) {
      await loadConnections()
    }

    // 初始化面包屑导航
    updateBreadcrumb(currentPath.value)
  } catch (error) {
    console.error('恢复状态失败:', error)
    await loadConnections()
  }
}

const loadConnections = async () => {
  try {
    const response = await getSSHConnections()
    if (response.code === 200) {
      connections.value = response.data || []
    }
  } catch (error) {
    console.error('加载连接列表失败:', error)
  }
}

const selectConnection = async (connection: SSHConnection) => {
  console.log('选择连接:', connection)

  const existingTab = openTabs.value.find(tab => tab.connection.id === connection.id)

  if (existingTab) {
    await switchTab(existingTab.id)
    return
  }

  const tabId = `tab-${Date.now()}-${connection.id}`
  const newTab: TabInfo = {
    id: tabId,
    connection: connection,
    status: 'disconnected',
    terminal: null,
    webSocket: null
  }

  openTabs.value.push(newTab)
  activeTabId.value = tabId
  activeConnection.value = connection

  await nextTick()
  await initializeAndConnectTab(newTab)
}

const switchTab = async (tabId: string) => {
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    activeTabId.value = tabId
    activeConnection.value = tab.connection
    
    if (tab.status === 'disconnected' && !tab.terminal) {
      await initializeAndConnectTab(tab)
    }
    
    if (tab.status === 'connected') {
      refreshServerInfo()
      if (showFilesPanel.value) {
        refreshFileList()
      }
    }
  }
}

const closeTab = (tabId: string) => {
  const tabIndex = openTabs.value.findIndex(t => t.id === tabId)
  if (tabIndex > -1) {
    const tab = openTabs.value[tabIndex]
    
    if (tab.webSocket) {
      tab.webSocket.close()
    }
    
    if (tab.terminal) {
      try {
        tab.terminal.dispose()
      } catch (error) {
        console.warn('Terminal dispose error:', error)
      }
    }
    
    openTabs.value.splice(tabIndex, 1)
    
    if (activeTabId.value === tabId) {
      if (openTabs.value.length > 0) {
        const newActiveTab = openTabs.value[Math.max(0, tabIndex - 1)]
        switchTab(newActiveTab.id)
      } else {
        activeTabId.value = ''
        activeConnection.value = null
      }
    }
  }
}

const setTerminalRef = (el: HTMLElement | null, tabId: string) => {
  if (el) {
    terminalRefs.value.set(tabId, el)
  }
}

const getStatusText = (status: string) => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '错误'
  }
  return statusMap[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

const refreshAll = async () => {
  refreshing.value = true
  try {
    await loadConnections()
    if (activeConnection.value) {
      await refreshServerInfo()
      if (showFilesPanel.value) {
        await refreshFileList()
      }
    }
    ElMessage.success('刷新完成')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    refreshing.value = false
  }
}

const toggleMonitor = () => {
  showMonitor.value = !showMonitor.value
}

const toggleFilesPanel = () => {
  showFilesPanel.value = !showFilesPanel.value
  if (showFilesPanel.value && activeConnection.value) {
    refreshFileList()
  }
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const goBack = () => {
  // 打开添加服务器对话框
  showConnectionDialog.value = true
}

const getConnectionStatus = (connectionId: number) => {
  const tab = openTabs.value.find(t => t.connection.id === connectionId)
  return tab ? tab.status : 'disconnected'
}

const getConnectionStatusText = (connectionId: number) => {
  const status = getConnectionStatus(connectionId)
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'error': '错误'
  }
  return statusMap[status] || '未连接'
}

const refreshServerInfo = async () => {
  if (!activeConnection.value) return
  
  try {
    const response = await getServerInfo(activeConnection.value.id!)
    if (response.code === 200) {
      serverInfo.value = response.data
    }
  } catch (error) {
    console.error('获取服务器信息失败:', error)
  }
}

// 文件管理方法
const refreshFileList = async () => {
  if (!activeConnection.value) return

  fileListLoading.value = true
  try {
    console.log('Refreshing file list for path:', currentPath.value)
    const response = await getSFTPFileList(activeConnection.value.id!, currentPath.value)
    if (response.code === 200) {
      const files = response.data || []

      // 转换数据格式以匹配模板
      fileList.value = files.map((file: FileInfo) => ({
        name: file.name,
        isDir: file.isDirectory,
        path: currentPath.value === '/'
          ? '/' + file.name
          : currentPath.value + '/' + file.name,
        size: file.size,
        modTime: file.modTime,
        permissions: file.permissions
      }))

      console.log('Generated file list:', fileList.value.map(f => ({ name: f.name, path: f.path })))

      // 如果不是根目录，添加返回上级目录的选项
      if (currentPath.value !== '/') {
        fileList.value.unshift({
          name: '..',
          isDir: true,
          path: getParentPath(currentPath.value),
          size: 0,
          modTime: '',
          permissions: 'drwxr-xr-x'
        })
      }

      // 更新面包屑导航
      updateBreadcrumb(currentPath.value)
    } else {
      ElMessage.error(response.message || '获取文件列表失败')
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error('获取文件列表失败')
  } finally {
    fileListLoading.value = false
  }
}

const navigateToPath = (path: string) => {
  // 确保路径格式正确
  if (path === '..') {
    path = getParentPath(currentPath.value)
  }

  // 规范化路径 - 移除末尾的斜杠（除了根目录）
  if (path !== '/' && path.endsWith('/')) {
    path = path.slice(0, -1)
  }

  // 确保路径以斜杠开头
  if (!path.startsWith('/')) {
    path = '/' + path
  }

  // 移除重复的斜杠
  path = path.replace(/\/+/g, '/')

  console.log('Navigating to path:', path)
  currentPath.value = path
  updateBreadcrumb(path)
  refreshFileList()
}

const updateBreadcrumb = (path: string) => {
  console.log('Updating breadcrumb for path:', path)
  const parts = path.split('/').filter(p => p)

  if (path === '/') {
    // 根目录情况
    breadcrumbPaths.value = [{ name: '/', fullPath: '/' }]
  } else {
    // 非根目录情况
    breadcrumbPaths.value = [{ name: '/', fullPath: '/' }]
    let currentFullPath = ''
    for (let i = 0; i < parts.length; i++) {
      currentFullPath += '/' + parts[i]
      breadcrumbPaths.value.push({
        name: parts[i],
        fullPath: currentFullPath
      })
    }
  }

  console.log('Breadcrumb paths:', breadcrumbPaths.value)
}

const navigateToBreadcrumb = (index: number) => {
  if (index === 0) {
    // 点击根目录
    navigateToPath('/')
  } else {
    // 点击其他路径
    const targetPath = breadcrumbPaths.value[index].fullPath
    navigateToPath(targetPath)
  }
}

const getParentPath = (path: string) => {
  if (path === '/') return '/'
  const parts = path.split('/').filter(p => p)
  parts.pop()
  return '/' + parts.join('/') + (parts.length > 0 ? '/' : '')
}

const handleFileClick = (row: any) => {
  if (row.isDir) {
    if (row.name === '..') {
      // 返回上级目录
      navigateToPath(getParentPath(currentPath.value))
    } else {
      // 进入子目录 - 直接使用row.path，它已经是完整路径
      console.log('Clicking folder:', row.name, 'path:', row.path)
      navigateToPath(row.path)
    }
  }
}

const handleFileDoubleClick = (row: any) => {
  // 双击也执行相同的逻辑
  handleFileClick(row)
}

const formatFileSize = (size: number) => {
  if (size === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (time: string) => {
  if (!time) return '-'
  return new Date(time).toLocaleString()
}

const downloadFile = async (file: any) => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  try {
    const filePath = file.path
    const response = await downloadFileAPI(activeConnection.value.id!, filePath)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

const deleteFile = async (file: any) => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除 ${file.name} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    const filePath = file.path
    const response = await deleteFileAPI(activeConnection.value.id!, filePath, file.isDir)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      await refreshFileList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const createFolder = async () => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  if (!newFolderName.value.trim()) {
    ElMessage.warning('请输入文件夹名称')
    return
  }

  try {
    const response = await createFolderAPI(
      activeConnection.value.id!,
      currentPath.value,
      newFolderName.value.trim()
    )

    if (response.code === 200) {
      ElMessage.success('创建成功')
      showCreateFolderDialog.value = false
      newFolderName.value = ''
      await refreshFileList()
    } else {
      ElMessage.error(response.message || '创建失败')
    }
  } catch (error) {
    console.error('创建文件夹失败:', error)
    ElMessage.error('创建文件夹失败')
  }
}

const beforeUpload = (file: any) => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return false
  }

  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
  uploadUrl.value = `${baseUrl}/api/sftp/upload`
  uploadHeaders.value = {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
  uploadData.value = {
    connection_id: activeConnection.value.id,
    path: currentPath.value
  }
  return true
}

const handleUploadSuccess = (response: any) => {
  if (response.code === 200) {
    ElMessage.success('上传成功')
  } else {
    ElMessage.error(response.message || '上传失败')
  }
  showUploadDialog.value = false
  refreshFileList()
}

const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败')
}

// 路径跳转方法
const jumpToShellPath = () => {
  if (currentShellPath.value && currentShellPath.value !== currentPath.value) {
    navigateToPath(currentShellPath.value)
    ElMessage.success(`已跳转至XShell路径: ${currentShellPath.value}`)
  } else {
    ElMessage.info('当前已在XShell路径')
  }
}

const jumpToFtpPath = () => {
  if (currentPath.value && currentPath.value !== currentShellPath.value) {
    // 发送cd命令到当前活动的终端
    if (activeTab.value && activeTab.value.webSocket) {
      const cdCommand = `cd "${currentPath.value}"\n`
      activeTab.value.webSocket.sendInput(cdCommand)
      currentShellPath.value = currentPath.value
      ElMessage.success(`已切换XShell到路径: ${currentPath.value}`)
    } else {
      ElMessage.error('没有活动的终端连接')
    }
  } else {
    ElMessage.info('当前已在XFTP路径')
  }
}

// 从终端输出中提取当前路径
const extractCurrentPath = (output: string) => {
  // 检测pwd命令的输出（通常是单独一行的路径）
  const lines = output.split('\n')
  for (const line of lines) {
    const trimmedLine = line.trim()
    // 检测是否是路径格式（以/开头，不包含特殊字符）
    if (trimmedLine.startsWith('/') &&
        !trimmedLine.includes(' ') &&
        !trimmedLine.includes('\r') &&
        trimmedLine.length > 1) {
      // 更新当前Shell路径
      if (trimmedLine !== currentShellPath.value) {
        currentShellPath.value = trimmedLine
        console.log('检测到Shell路径变化:', trimmedLine)
      }
      break
    }
  }

  // 也可以从提示符中提取路径（如果提示符包含路径信息）
  const promptMatch = output.match(/[\w@\-]+:([^$#\s]+)[\$#]/g)
  if (promptMatch) {
    const lastPrompt = promptMatch[promptMatch.length - 1]
    const pathMatch = lastPrompt.match(/:([^$#\s]+)/)
    if (pathMatch && pathMatch[1]) {
      let path = pathMatch[1]
      // 处理~符号
      if (path === '~') {
        path = '/home/' + (activeTab.value?.connection?.username || 'user')
      } else if (path.startsWith('~/')) {
        path = '/home/' + (activeTab.value?.connection?.username || 'user') + path.substring(1)
      }

      if (path !== currentShellPath.value && path.startsWith('/')) {
        currentShellPath.value = path
        console.log('从提示符检测到Shell路径:', path)
      }
    }
  }
}

// 连接管理方法
const saveConnection = async () => {
  if (!connectionFormRef.value) return

  try {
    await connectionFormRef.value.validate()
  } catch (error) {
    return
  }

  // 清理认证信息
  if (authType.value === 'password') {
    connectionForm.private_key = ''
  } else {
    connectionForm.password = ''
  }

  saving.value = true
  try {
    if (editingConnection.value) {
      await updateSSHConnection(editingConnection.value.id!, connectionForm)
      ElMessage.success('连接更新成功')
    } else {
      await createSSHConnection(connectionForm)
      ElMessage.success('连接创建成功')
    }

    showConnectionDialog.value = false
    await loadConnections()
  } catch (error: any) {
    ElMessage.error('保存连接失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const resetConnectionForm = () => {
  editingConnection.value = null
  Object.assign(connectionForm, {
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    private_key: '',
    description: ''
  })
  authType.value = 'password'
  if (connectionFormRef.value) {
    connectionFormRef.value.clearValidate()
  }
}

// 终端相关方法
const executeCommand = () => {
  console.log('执行命令:', currentCommand.value)
  console.log('当前活动标签ID:', activeTabId.value)
  console.log('打开的标签页:', openTabs.value)

  const tab = openTabs.value.find(t => t.id === activeTabId.value)
  console.log('找到的标签页:', tab)

  if (!currentCommand.value.trim()) {
    console.log('命令为空')
    return
  }

  if (!tab) {
    console.log('没有找到活动标签页')
    ElMessage.warning('没有活动的标签页')
    return
  }

  if (!tab.webSocket) {
    console.log('WebSocket不存在')
    ElMessage.warning('WebSocket连接不存在')
    return
  }

  if (tab.status !== 'connected') {
    console.log('标签页状态:', tab.status)
    ElMessage.warning('请先连接到SSH服务器')
    return
  }

  const command = currentCommand.value.trim()
  console.log('准备发送命令:', command)

  if (commandHistory.value[commandHistory.value.length - 1] !== command) {
    commandHistory.value.push(command)
    if (commandHistory.value.length > 100) {
      commandHistory.value.shift()
    }
  }
  historyIndex.value = -1

  try {
    tab.webSocket.sendInput(command + '\n')
    console.log('命令已发送')
    currentCommand.value = ''
  } catch (error) {
    console.error('发送命令失败:', error)
    ElMessage.error('发送命令失败')
  }
}

const executeQuickCommand = (command: string) => {
  const tab = openTabs.value.find(t => t.id === activeTabId.value)
  if (!tab || !tab.webSocket || tab.status !== 'connected') {
    ElMessage.warning('请先连接到SSH服务器')
    return
  }
  tab.webSocket.sendInput(command + '\n')
}

const navigateHistory = (direction: number) => {
  if (commandHistory.value.length === 0) return

  if (direction === -1) {
    if (historyIndex.value === -1) {
      historyIndex.value = commandHistory.value.length - 1
    } else if (historyIndex.value > 0) {
      historyIndex.value--
    }
  } else if (direction === 1) {
    if (historyIndex.value < commandHistory.value.length - 1) {
      historyIndex.value++
    } else {
      historyIndex.value = -1
      currentCommand.value = ''
      return
    }
  }

  if (historyIndex.value >= 0) {
    currentCommand.value = commandHistory.value[historyIndex.value]
  }
}

const initializeAndConnectTab = async (tab: TabInfo) => {
  try {
    await nextTick()
    
    const terminalElement = terminalRefs.value.get(tab.id)
    if (terminalElement) {
      const [{ Terminal }, { FitAddon }] = await Promise.all([
        import('xterm'),
        import('xterm-addon-fit'),
        import('xterm/css/xterm.css')
      ])
      
      const terminal = new Terminal({
        theme: {
          background: '#000000',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#ffffff30'
        },
        fontSize: 14,
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        cursorBlink: true,
        allowTransparency: true
      })
      
      const fitAddon = new FitAddon()
      terminal.loadAddon(fitAddon)
      
      terminal.open(terminalElement)
      fitAddon.fit()
      
      tab.terminal = terminal
      
      await connectSSH(tab)
    }
  } catch (error) {
    console.error('初始化标签页失败:', error)
    tab.status = 'error'
    ElMessage.error('初始化终端失败')
  }
}

const connectSSH = async (tab: TabInfo) => {
  if (!tab.terminal || !tab.connection) {
    return
  }

  tab.status = 'connecting'
  tab.terminal.clear()
  tab.terminal.writeln('正在连接到 ' + tab.connection.host + ':' + tab.connection.port + '...')

  try {
    const token = localStorage.getItem('token') || ''
    
    const webSocket = new SSHWebSocket(
      tab.connection.id!,
      token,
      (message) => handleWebSocketMessage(message, tab.id),
      (error) => handleWebSocketError(error, tab.id),
      (event) => handleWebSocketClose(event, tab.id)
    )
    
    tab.webSocket = webSocket

    const connectTimeout = setTimeout(() => {
      if (tab.status === 'connecting') {
        tab.status = 'error'
        tab.terminal?.writeln('\r\n连接超时，请检查网络连接和服务器配置')
        ElMessage.error('连接超时')
        if (tab.webSocket) {
          tab.webSocket.close()
        }
      }
    }, 15000)

    await webSocket.connect()
    clearTimeout(connectTimeout)

    tab.terminal.onData((data) => {
      if (tab.webSocket && tab.status === 'connected') {
        tab.webSocket.sendInput(data)
      }
    })

    tab.terminal.onResize(({ cols, rows }) => {
      if (tab.webSocket && tab.status === 'connected') {
        tab.webSocket.resizeTerminal(cols, rows)
      }
    })

  } catch (error) {
    console.error('SSH连接失败:', error)
    tab.status = 'error'
    tab.terminal.writeln('\r\n连接失败: ' + error)
    ElMessage.error('连接失败: ' + error)
  }
}

const handleWebSocketMessage = (message: any, tabId: string) => {
  const tab = openTabs.value.find(t => t.id === tabId)
  if (!tab || !tab.terminal) return

  switch (message.type) {
    case 'connecting':
      tab.status = 'connecting'
      tab.terminal.writeln(message.data)
      break
    case 'connected':
      tab.status = 'connected'
      tab.terminal.writeln('\r\n' + message.data)
      tab.terminal.writeln('终端已就绪，您可以开始输入命令。\r\n')
      if (activeTabId.value === tabId) {
        setTimeout(() => {
          refreshServerInfo()
          if (showFilesPanel.value) {
            refreshFileList()
          }
        }, 2000)
      }
      break
    case 'output':
      tab.terminal.write(message.data)
      // 尝试从输出中提取当前工作目录
      if (activeTabId.value === tabId) {
        extractCurrentPath(message.data)
      }
      break
    case 'error':
      tab.status = 'error'
      tab.terminal.writeln('\r\n连接失败: ' + message.data)
      ElMessage.error('SSH连接失败: ' + message.data)
      break
    case 'pong':
      break
  }
}

const handleWebSocketError = (error: Event, tabId: string) => {
  console.error('WebSocket error:', error, 'tabId:', tabId)
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    tab.status = 'error'
    if (tab.terminal) {
      tab.terminal.writeln('\r\nWebSocket连接错误')
    }
  }
  ElMessage.error('WebSocket连接错误')
}

const handleWebSocketClose = (event: CloseEvent, tabId: string) => {
  console.log('WebSocket关闭:', event.code, event.reason, 'tabId:', tabId)
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    tab.status = 'disconnected'
    if (tab.terminal) {
      tab.terminal.writeln('\r\n连接已断开')
    }
  }
}
</script>

<style scoped>
/* 全屏模式样式 */
.xshell-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1e1e1e;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  z-index: 9999;
}

/* 顶部工具栏 */
.fullscreen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.connection-count {
  font-size: 14px;
  color: #909399;
}

.header-right {
  display: flex;
  gap: 8px;
}

/* 主要内容区域 */
.fullscreen-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧边栏 */
.left-sidebar {
  width: 320px;
  background: #252525;
  border-right: 1px solid #404040;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

/* 连接列表区域 */
.connections-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.connections-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.connection-item:hover {
  background: #3a3a3a;
  border-color: #409eff;
}

.connection-item.active {
  background: #409eff;
  color: #ffffff;
}

.connection-info {
  flex: 1;
  min-width: 0;
}

.connection-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.connection-details {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.connection-item.active .connection-details {
  color: rgba(255, 255, 255, 0.8);
}

.connection-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  background: #404040;
  color: #909399;
  flex-shrink: 0;
  margin-left: 8px;
}

.connection-status.connected {
  background: #67c23a;
  color: #ffffff;
}

.connection-status.connecting {
  background: #e6a23c;
  color: #ffffff;
}

.connection-status.error {
  background: #f56c6c;
  color: #ffffff;
}

/* 监控面板 */
.monitor-section {
  border-top: 1px solid #404040;
  background: #2a2a2a;
  flex-shrink: 0;
  max-height: 300px;
  display: flex;
  flex-direction: column;
}

.monitor-content {
  padding: 12px;
  flex: 1;
  overflow-y: auto;
}

.resource-item {
  margin-bottom: 16px;
}

.resource-label {
  font-size: 13px;
  color: #e4e7ed;
  margin-bottom: 6px;
  font-weight: 500;
}

.resource-detail {
  font-size: 11px;
  color: #909399;
  margin-top: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 终端区域 */
.terminal-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 连接标签页 */
.connection-tabs {
  display: flex;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  overflow-x: auto;
  flex-shrink: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-right: 1px solid #404040;
  cursor: pointer;
  transition: background-color 0.2s;
  min-width: 200px;
  gap: 8px;
}

.tab-item:hover {
  background: #3a3a3a;
}

.tab-item.active {
  background: #409eff;
  color: #ffffff;
}

.tab-name {
  font-weight: 500;
  flex: 1;
}

.tab-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.tab-status.connected {
  background: #67c23a;
  color: #ffffff;
}

.tab-status.connecting {
  background: #e6a23c;
  color: #ffffff;
}

.tab-status.error {
  background: #f56c6c;
  color: #ffffff;
}

.close-btn {
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

/* 终端容器 */
.terminal-container {
  flex: 1;
  background: #000000;
  overflow: hidden;
}

.terminal-wrapper {
  width: 100%;
  height: 100%;
}

/* 快捷命令栏 */
.quick-commands-fullscreen {
  background: #2d2d2d;
  border-top: 1px solid #404040;
  padding: 12px 16px;
  flex-shrink: 0;
}

.quick-commands-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.quick-commands-header span {
  font-size: 14px;
  color: #e4e7ed;
  font-weight: 500;
}

.quick-command-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 命令输入区域 */
.command-input-fullscreen {
  padding: 16px;
  background: #2d2d2d;
  border-top: 1px solid #404040;
  flex-shrink: 0;
}

.command-prompt {
  color: #67c23a;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 空状态 */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右侧文件管理面板 */
.files-panel {
  width: 400px;
  background: #252525;
  border-left: 1px solid #404040;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

.files-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.files-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 面包屑栏样式 */
.breadcrumb-bar {
  padding: 8px 12px;
  background-color: #252525;
  border-bottom: 1px solid #404040;
  min-height: 36px;
  display: flex;
  align-items: center;
}

.files-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #2d2d2d;
  border-bottom: 1px solid #404040;
  flex-shrink: 0;
}

.toolbar-left {
  flex: 1;
}

.toolbar-right {
  display: flex;
  gap: 6px;
}

.breadcrumb-item {
  cursor: pointer;
  transition: color 0.2s;
}

.breadcrumb-item:hover {
  color: #409eff;
}

.files-list {
  flex: 1;
  overflow: hidden;
  background: #1e1e1e;
}

.file-link {
  cursor: pointer;
  color: #409eff;
  transition: color 0.2s;
  font-weight: 500;
}

.file-link:hover {
  color: #66b1ff;
}

/* 文件名样式 */
.file-name {
  color: #e4e7ed;
  font-weight: 500;
}

.file-name.is-directory {
  color: #67c23a;
  font-weight: 600;
}

.file-name.parent-dir {
  color: #f56c6c;
  font-weight: 600;
}

/* 全局样式覆盖 */
:global(.fullscreen-mode) {
  overflow: hidden;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-button) {
  border-color: #404040;
}

:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-input__wrapper) {
  background-color: #3a3a3a;
  border-color: #404040;
  color: #ffffff;
}

:deep(.el-input__inner) {
  color: #ffffff;
}

:deep(.el-progress-bar__outer) {
  background-color: #404040;
}

:deep(.el-table) {
  background-color: #1e1e1e;
  color: #e4e7ed;
  --el-table-border-color: #404040;
  --el-table-bg-color: #1e1e1e;
  --el-table-tr-bg-color: #1e1e1e;
  --el-table-expanded-cell-bg-color: #252525;
}

:deep(.el-table th) {
  background-color: #2d2d2d !important;
  color: #ffffff !important;
  border-bottom: 1px solid #404040 !important;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #404040 !important;
  color: #e4e7ed !important;
}

:deep(.el-table--border) {
  border: 1px solid #404040 !important;
}

:deep(.el-table--border::after) {
  background-color: #404040 !important;
}

:deep(.el-table__body tr:hover > td) {
  background-color: #3a3a3a !important;
}

:deep(.el-table .cell) {
  color: inherit;
}

/* 面包屑导航样式 */
:deep(.el-breadcrumb) {
  color: #e4e7ed;
}

:deep(.el-breadcrumb__item) {
  color: #e4e7ed;
}

:deep(.el-breadcrumb__inner) {
  color: #e4e7ed;
  font-weight: 500;
}

:deep(.el-breadcrumb__inner:hover) {
  color: #409eff;
}
</style>
