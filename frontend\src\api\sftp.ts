import api from './index'

// 文件信息接口
export interface FileInfo {
  name: string
  size: number
  isDirectory: boolean
  modTime: string
  permissions: string
}

// 获取文件列表
export const getSFTPFileList = (connectionId: number, path: string = '/') => {
  return api.get('/sftp/files', {
    params: {
      connection_id: connectionId,
      path: path
    }
  })
}

// 下载文件
export const downloadFile = (connectionId: number, filePath: string) => {
  return api.get('/sftp/download', {
    params: {
      connection_id: connectionId,
      path: filePath
    },
    responseType: 'blob'
  })
}

// 创建文件夹
export const createFolder = (connectionId: number, path: string, folderName: string) => {
  return api.post('/sftp/folder', {
    connection_id: connectionId,
    path: path,
    folder_name: folderName
  })
}

// 删除文件或文件夹
export const deleteFile = (connectionId: number, filePath: string, isDirectory: boolean = false) => {
  return api.delete('/sftp/delete', {
    data: {
      connection_id: connectionId,
      path: filePath,
      is_directory: isDirectory
    }
  })
}

// 获取上传URL
export const getUploadUrl = (connectionId: number, path: string) => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || ''
  return `${baseUrl}/api/sftp/upload?connection_id=${connectionId}&path=${encodeURIComponent(path)}`
}
