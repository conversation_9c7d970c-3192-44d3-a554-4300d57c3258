<template>
  <div class="xftp-container">
    <!-- 连接列表和文件管理区域 -->
    <div class="main-content">
      <!-- 左侧连接列表 -->
      <div class="connection-sidebar">
        <!-- SSH连接列表 -->
        <div class="connections-section">
          <div class="sidebar-header">
            <h3>FTP 连接</h3>
            <div class="header-actions">
              <el-button size="small" type="primary" @click="showConnectionDialog = true">
                <el-icon><Plus /></el-icon>
                新建
              </el-button>
              <el-button size="small" text @click="loadConnections">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="connection-list">
            <div
              v-for="connection in connections"
              :key="connection.id"
              class="connection-item"
              :class="{ active: activeConnection?.id === connection.id }"
              @click="selectConnection(connection)"
            >
              <div class="connection-info">
                <div class="connection-name">{{ connection.name }}</div>
                <div class="connection-details">{{ connection.username }}@{{ connection.host }}:{{ connection.port }}</div>
              </div>
              <div class="connection-actions">
                <el-dropdown @command="handleConnectionAction">
                  <el-button size="small" text>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${connection.id}`">编辑</el-dropdown-item>
                      <el-dropdown-item :command="`test-${connection.id}`">测试连接</el-dropdown-item>
                      <el-dropdown-item :command="`delete-${connection.id}`" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>

        <!-- 传输任务列表 -->
        <div class="transfer-section" v-if="activeConnection">
          <div class="transfer-header">
            <h3>传输任务</h3>
            <el-button size="small" text @click="clearCompletedTasks">
              <el-icon><Delete /></el-icon>
              清理
            </el-button>
          </div>
          <div class="transfer-list">
            <div
              v-for="task in transferTasks"
              :key="task.id"
              class="transfer-item"
              :class="task.status"
            >
              <div class="transfer-info">
                <div class="transfer-name">{{ task.fileName }}</div>
                <div class="transfer-progress">
                  <el-progress
                    :percentage="task.progress"
                    :status="task.status === 'error' ? 'exception' : task.status === 'completed' ? 'success' : ''"
                    :stroke-width="4"
                    :show-text="false"
                  />
                  <span class="progress-text">{{ task.progress }}%</span>
                </div>
              </div>
              <div class="transfer-actions">
                <el-button
                  v-if="task.status === 'transferring'"
                  size="small"
                  text
                  @click="cancelTransfer(task.id)"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧文件管理区域 -->
      <div class="file-manager-area">
        <div v-if="!activeConnection" class="no-connection">
          <el-empty description="请选择一个连接开始文件管理" />
        </div>
        <div v-else class="file-manager">
          <!-- 工具栏 -->
          <div class="toolbar">
            <div class="path-bar">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item
                  v-for="(path, index) in pathBreadcrumb"
                  :key="index"
                  @click="navigateToPath(index)"
                  class="breadcrumb-item"
                >
                  {{ path }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="toolbar-actions">
              <el-button size="small" @click="refreshFileList">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button size="small" @click="showCreateFolderDialog = true">
                <el-icon><FolderAdd /></el-icon>
                新建文件夹
              </el-button>
              <el-upload
                ref="uploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="uploadData"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :on-progress="handleUploadProgress"
                :show-file-list="false"
                multiple
              >
                <el-button size="small">
                  <el-icon><Upload /></el-icon>
                  上传文件
                </el-button>
              </el-upload>
            </div>
          </div>

          <!-- 文件列表 -->
          <div class="file-list-container">
            <el-table
              :data="fileList"
              v-loading="loading"
              @row-dblclick="handleRowDoubleClick"
              @selection-change="handleSelectionChange"
              height="100%"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="name" label="名称" min-width="200">
                <template #default="{ row }">
                  <div class="file-item" :class="{ 'parent-dir': row.name === '..' }">
                    <el-icon class="file-icon" :class="getFileIconClass(row)">
                      <component :is="getFileIcon(row)" />
                    </el-icon>
                    <span class="file-name">
                      {{ row.name === '..' ? '返回上级目录' : row.name }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="大小" width="120">
                <template #default="{ row }">
                  {{ row.name === '..' ? '' : (row.isDirectory ? '-' : formatFileSize(row.size)) }}
                </template>
              </el-table-column>
              <el-table-column prop="modTime" label="修改时间" width="180">
                <template #default="{ row }">
                  {{ row.name === '..' ? '' : formatDateTime(row.modTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="permissions" label="权限" width="100">
                <template #default="{ row }">
                  {{ row.name === '..' ? '' : row.permissions }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <template v-if="row.name !== '..'">
                    <el-button
                      v-if="!row.isDirectory"
                      size="small"
                      text
                      @click="downloadFile(row)"
                    >
                      <el-icon><Download /></el-icon>
                      下载
                    </el-button>
                    <el-button
                      size="small"
                      text
                      type="danger"
                      @click="deleteFile(row)"
                    >
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑连接对话框 -->
    <el-dialog
      v-model="showConnectionDialog"
      :title="editingConnection ? '编辑FTP连接' : '新建FTP连接'"
      width="600px"
      @close="resetConnectionForm"
    >
      <el-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        label-width="100px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="connectionForm.name" placeholder="请输入连接名称" />
        </el-form-item>
        
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="connectionForm.host" placeholder="请输入主机地址或IP" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="connectionForm.port" :min="1" :max="65535" />
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="connectionForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="connectionForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="connectionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showConnectionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConnection" :loading="saving">
          {{ editingConnection ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog
      v-model="showCreateFolderDialog"
      title="新建文件夹"
      width="400px"
    >
      <el-form>
        <el-form-item label="文件夹名称">
          <el-input v-model="newFolderName" placeholder="请输入文件夹名称" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  MoreFilled,
  Close,
  Delete,
  FolderAdd,
  Upload,
  Download,
  Folder,
  Document,
  ArrowUp
} from '@element-plus/icons-vue'

// 使用SSH连接的API，因为SFTP基于SSH
import {
  type SSHConnection,
  getSSHConnections,
  createSSHConnection,
  updateSSHConnection,
  deleteSSHConnection,
  testSSHConnection
} from '@/api/ssh'

// SFTP相关API
import {
  type FileInfo,
  getSFTPFileList,
  downloadFile as downloadFileAPI,
  createFolder as createFolderAPI,
  deleteFile as deleteFileAPI,
  getUploadUrl
} from '@/api/sftp'

// 文件信息接口
interface FileInfo {
  name: string
  size: number
  isDirectory: boolean
  modTime: string
  permissions: string
}

// 传输任务接口
interface TransferTask {
  id: string
  fileName: string
  progress: number
  status: 'transferring' | 'completed' | 'error'
  type: 'upload' | 'download'
}

// 响应式数据
const connections = ref<SSHConnection[]>([])
const activeConnection = ref<SSHConnection | null>(null)
const showConnectionDialog = ref(false)
const editingConnection = ref<SSHConnection | null>(null)
const saving = ref(false)
const loading = ref(false)

// 文件管理相关
const currentPath = ref('/')
const fileList = ref<FileInfo[]>([])
const selectedFiles = ref<FileInfo[]>([])
const showCreateFolderDialog = ref(false)
const newFolderName = ref('')

// 传输任务
const transferTasks = ref<TransferTask[]>([])

// 表单数据
const connectionForm = reactive<SSHConnection>({
  name: '',
  host: '',
  port: 22,
  username: '',
  password: '',
  description: ''
})

// 表单验证规则
const connectionRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

// 计算属性
const pathBreadcrumb = computed(() => {
  const parts = currentPath.value.split('/').filter(p => p)
  return ['根目录', ...parts]
})

const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
  return `${baseUrl}/api/sftp/upload`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})

const uploadData = computed(() => {
  return {
    connection_id: activeConnection.value?.id,
    path: currentPath.value
  }
})

// 组件挂载
onMounted(() => {
  loadConnections()
})

// 加载连接列表
const loadConnections = async () => {
  try {
    const response = await getSSHConnections()
    if (response.code === 200) {
      connections.value = response.data || []
    }
  } catch (error) {
    console.error('Failed to load connections:', error)
    ElMessage.error('加载连接列表失败')
  }
}

// 选择连接
const selectConnection = async (connection: SSHConnection) => {
  if (activeConnection.value?.id === connection.id) {
    return
  }

  activeConnection.value = connection
  currentPath.value = '/'
  await loadFileList()
}

// 加载文件列表
const loadFileList = async () => {
  if (!activeConnection.value) return

  loading.value = true
  try {
    const response = await getSFTPFileList(activeConnection.value.id!, currentPath.value)
    if (response.code === 200) {
      let files = response.data || []

      // 如果不是根目录，添加返回上级目录的选项
      if (currentPath.value !== '/') {
        files.unshift({
          name: '..',
          size: 0,
          isDirectory: true,
          modTime: new Date().toISOString(),
          permissions: 'drwxr-xr-x'
        })
      }

      fileList.value = files
    } else {
      ElMessage.error('加载文件列表失败: ' + response.message)
    }
  } catch (error: any) {
    console.error('Failed to load file list:', error)
    ElMessage.error('加载文件列表失败: ' + (error.response?.data?.message || error.message))
  } finally {
    loading.value = false
  }
}

// 处理连接操作
const handleConnectionAction = async (command: string) => {
  const [action, idStr] = command.split('-')
  const id = parseInt(idStr)
  const connection = connections.value.find(c => c.id === id)
  
  if (!connection) return

  switch (action) {
    case 'edit':
      editConnection(connection)
      break
    case 'test':
      await testConnection(connection)
      break
    case 'delete':
      await deleteConnection(connection)
      break
  }
}

// 其他方法的实现...
const editConnection = (connection: SSHConnection) => {
  editingConnection.value = connection
  Object.assign(connectionForm, connection)
  showConnectionDialog.value = true
}

const testConnection = async (connection: SSHConnection) => {
  try {
    ElMessage.info('正在测试连接...')
    await testSSHConnection(connection.id!)
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message))
  }
}

const deleteConnection = async (connection: SSHConnection) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除连接 "${connection.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSSHConnection(connection.id!)
    ElMessage.success('连接删除成功')
    await loadConnections()
    
    if (activeConnection.value?.id === connection.id) {
      activeConnection.value = null
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除连接失败')
    }
  }
}

const saveConnection = async () => {
  saving.value = true
  try {
    if (editingConnection.value) {
      await updateSSHConnection(editingConnection.value.id!, connectionForm)
      ElMessage.success('连接更新成功')
    } else {
      await createSSHConnection(connectionForm)
      ElMessage.success('连接创建成功')
    }
    
    showConnectionDialog.value = false
    await loadConnections()
  } catch (error: any) {
    ElMessage.error('保存连接失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

const resetConnectionForm = () => {
  editingConnection.value = null
  Object.assign(connectionForm, {
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    description: ''
  })
}

// 文件操作方法
const refreshFileList = () => {
  loadFileList()
}

const handleRowDoubleClick = (row: FileInfo) => {
  if (row.isDirectory) {
    if (row.name === '..') {
      // 返回上级目录
      const pathParts = currentPath.value.split('/').filter(p => p)
      pathParts.pop()
      currentPath.value = pathParts.length > 0 ? '/' + pathParts.join('/') : '/'
    } else {
      // 进入子目录
      if (currentPath.value === '/') {
        currentPath.value = '/' + row.name
      } else {
        currentPath.value = currentPath.value + '/' + row.name
      }
    }
    loadFileList()
  }
}

const handleSelectionChange = (selection: FileInfo[]) => {
  selectedFiles.value = selection
}

const navigateToPath = (index: number) => {
  if (index === 0) {
    // 点击根目录
    currentPath.value = '/'
  } else {
    // 点击其他路径
    const pathParts = pathBreadcrumb.value.slice(1, index + 1) // 跳过"根目录"
    currentPath.value = '/' + pathParts.join('/')
  }
  loadFileList()
}

const createFolder = async () => {
  if (!newFolderName.value.trim()) {
    ElMessage.warning('请输入文件夹名称')
    return
  }

  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  try {
    const response = await createFolderAPI(
      activeConnection.value.id!,
      currentPath.value,
      newFolderName.value.trim()
    )

    if (response.code === 200) {
      ElMessage.success('文件夹创建成功')
      showCreateFolderDialog.value = false
      newFolderName.value = ''
      await loadFileList()
    } else {
      ElMessage.error('创建文件夹失败: ' + response.message)
    }
  } catch (error: any) {
    console.error('Failed to create folder:', error)
    ElMessage.error('创建文件夹失败: ' + (error.response?.data?.message || error.message))
  }
}

const downloadFile = async (file: FileInfo) => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  try {
    ElMessage.info('开始下载文件...')

    const filePath = currentPath.value.endsWith('/')
      ? currentPath.value + file.name
      : currentPath.value + '/' + file.name

    const response = await downloadFileAPI(activeConnection.value.id!, filePath)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下载成功')
  } catch (error: any) {
    console.error('Failed to download file:', error)
    ElMessage.error('下载文件失败: ' + (error.response?.data?.message || error.message))
  }
}

const deleteFile = async (file: FileInfo) => {
  if (!activeConnection.value) {
    ElMessage.error('请先选择连接')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除 "${file.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const filePath = currentPath.value.endsWith('/')
      ? currentPath.value + file.name
      : currentPath.value + '/' + file.name

    const response = await deleteFileAPI(activeConnection.value.id!, filePath, file.isDirectory)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      await loadFileList()
    } else {
      ElMessage.error('删除失败: ' + response.message)
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Failed to delete file:', error)
      ElMessage.error('删除失败: ' + (error.response?.data?.message || error.message))
    }
  }
}

const clearCompletedTasks = () => {
  transferTasks.value = transferTasks.value.filter(task => task.status === 'transferring')
}

const cancelTransfer = (taskId: string) => {
  const task = transferTasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = 'error'
  }
}

const handleUploadSuccess = (response: any, file: any) => {
  console.log('Upload success:', response, file)
  ElMessage.success(`文件 ${file.name} 上传成功`)
  loadFileList()

  // 移除对应的传输任务
  const taskIndex = transferTasks.value.findIndex(task => task.fileName === file.name)
  if (taskIndex > -1) {
    transferTasks.value[taskIndex].status = 'completed'
    transferTasks.value[taskIndex].progress = 100
  }
}

const handleUploadError = (error: any, file: any) => {
  console.error('Upload error:', error, file)
  ElMessage.error(`文件 ${file.name} 上传失败`)

  // 更新传输任务状态
  const taskIndex = transferTasks.value.findIndex(task => task.fileName === file.name)
  if (taskIndex > -1) {
    transferTasks.value[taskIndex].status = 'error'
  }
}

const handleUploadProgress = (event: any, file: any) => {
  const progress = Math.round((event.loaded / event.total) * 100)

  // 查找或创建传输任务
  let task = transferTasks.value.find(task => task.fileName === file.name)
  if (!task) {
    task = {
      id: Date.now().toString(),
      fileName: file.name,
      progress: 0,
      status: 'transferring',
      type: 'upload'
    }
    transferTasks.value.push(task)
  }

  task.progress = progress
}

// 工具方法
const getFileIcon = (file: FileInfo) => {
  if (file.name === '..') {
    return ArrowUp // 返回上级目录使用向上箭头图标
  }
  return file.isDirectory ? Folder : Document
}

const getFileIconClass = (file: FileInfo) => {
  if (file.name === '..') {
    return 'parent-dir-icon'
  }
  return file.isDirectory ? 'folder-icon' : 'file-icon'
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(1) + ' MB'
  return (size / 1024 / 1024 / 1024).toFixed(1) + ' GB'
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}
</script>

<style scoped>
.xftp-container {
  height: calc(100vh - 60px - 40px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
  overflow: hidden;
  height: 100%;
}

.connection-sidebar {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.connections-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.connection-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.2s;
}

.connection-item:hover {
  background-color: #f5f7fa;
}

.connection-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.connection-info {
  flex: 1;
}

.connection-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  font-size: 13px;
}

.connection-details {
  font-size: 11px;
  color: #909399;
}

.transfer-section {
  border-top: 1px solid #ebeef5;
  background: #f8f9fa;
  flex-shrink: 0;
  max-height: 200px;
  display: flex;
  flex-direction: column;
}

.transfer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.transfer-header h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.transfer-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px;
}

.transfer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.transfer-info {
  flex: 1;
}

.transfer-name {
  font-size: 12px;
  color: #303133;
  margin-bottom: 4px;
}

.transfer-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 11px;
  color: #606266;
  min-width: 35px;
}

.file-manager-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.no-connection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-manager {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

.path-bar {
  flex: 1;
}

.breadcrumb-item {
  cursor: pointer;
}

.breadcrumb-item:hover {
  color: #409eff;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.file-list-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 16px;
}

.folder-icon {
  color: #409eff;
}

.file-icon {
  color: #909399;
}

.file-name {
  font-size: 14px;
}

.parent-dir {
  color: #409eff;
  font-weight: 500;
}

.parent-dir .file-icon {
  color: #409eff;
}

.parent-dir-icon {
  color: #409eff;
}
</style>
