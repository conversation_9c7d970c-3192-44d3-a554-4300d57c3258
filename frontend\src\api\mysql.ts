import { api } from './index'

// MySQL连接接口
export interface MySQLConnection {
  id?: number
  name: string
  host: string
  port: number
  username: string
  password: string
  database: string
  charset: string
  description?: string
  user_id?: number
  created_at?: string
  updated_at?: string
}

// MySQL数据库接口
export interface MySQLDatabase {
  name: string
  charset: string
  collation: string
  size?: string
}

// MySQL表接口
export interface MySQLTable {
  name: string
  engine: string
  rows: number
  data_length: number
  comment: string
}

// MySQL列接口
export interface MySQLColumn {
  field: string
  type: string
  null: string
  key: string
  default: string
  extra: string
  comment: string
}

// MySQL查询结果接口
export interface MySQLQueryResult {
  columns: string[]
  rows: any[][]
  total: number
  message?: string
}

// 创建MySQL连接
export const createMySQLConnection = (connection: MySQLConnection) => {
  return api.post('/mysql/connections', connection)
}

// 获取MySQL连接列表
export const getMySQLConnections = () => {
  return api.get('/mysql/connections')
}

// 更新MySQL连接
export const updateMySQLConnection = (id: number, connection: MySQLConnection) => {
  return api.put(`/mysql/connections/${id}`, connection)
}

// 删除MySQL连接
export const deleteMySQLConnection = (id: number) => {
  return api.delete(`/mysql/connections/${id}`)
}

// 测试MySQL连接
export const testMySQLConnection = (connection: MySQLConnection) => {
  return api.post('/mysql/connections/test', connection)
}

// 获取数据库列表
export const getMySQLDatabases = (id: number) => {
  return api.get(`/mysql/connections/${id}/databases`)
}

// 获取表列表
export const getMySQLTables = (id: number, database: string) => {
  return api.get(`/mysql/connections/${id}/tables`, {
    params: { database }
  })
}

// 获取表结构
export const getMySQLColumns = (id: number, database: string, table: string) => {
  return api.get(`/mysql/connections/${id}/columns`, {
    params: { database, table }
  })
}

// 执行MySQL查询
export const executeMySQLQuery = (id: number, query: string) => {
  return api.post(`/mysql/connections/${id}/query`, { query })
}
