import { defineStore } from 'pinia'
import { ref } from 'vue'
import * as authApi from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 从localStorage初始化用户信息
  const initUser = () => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        return JSON.parse(savedUser)
      } catch {
        return null
      }
    }
    return null
  }

  const user = ref<authApi.User | null>(initUser())
  const token = ref<string | null>(localStorage.getItem('token'))

  // 登录
  const login = async (data: authApi.LoginRequest) => {
    try {
      const response = await authApi.login(data)
      if (response.code === 200) {
        token.value = response.data.token
        user.value = {
          id: response.data.user_id,
          username: response.data.username,
          role: response.data.role,
          email: '',
          is_active: true,
          team_name: '',
          created_at: ''
        }
        localStorage.setItem('token', response.data.token)
        localStorage.setItem('user', JSON.stringify(user.value))
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.response?.data?.message || '登录失败' }
    }
  }

  // 注册
  const register = async (data: authApi.RegisterRequest) => {
    try {
      const response = await authApi.register(data)
      if (response.code === 200) {
        return { success: true, message: response.message }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.response?.data?.message || '注册失败' }
    }
  }

  // 获取用户信息
  const fetchProfile = async () => {
    try {
      const response = await authApi.getProfile()
      if (response.code === 200) {
        user.value = response.data
        return { success: true, data: response.data }
      }
      return { success: false, message: response.message }
    } catch (error: any) {
      return { success: false, message: error.response?.data?.message || '获取用户信息失败' }
    }
  }

  // 登出
  const logout = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }

  // 检查是否已登录
  const isLoggedIn = () => {
    return !!token.value
  }

  // 检查用户角色
  const hasRole = (role: string) => {
    return user.value?.role === role
  }

  // 检查是否为管理员
  const isAdmin = () => {
    return hasRole('admin')
  }

  // 检查是否为开发者
  const isDeveloper = () => {
    return hasRole('developer') || isAdmin()
  }

  return {
    user,
    token,
    login,
    register,
    fetchProfile,
    logout,
    isLoggedIn,
    hasRole,
    isAdmin,
    isDeveloper
  }
})
