package models

import (
	"time"
	"gorm.io/gorm"
)

// SSHConnection SSH连接配置模型
type SSHConnection struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null;size:100" binding:"required"`
	Host        string    `json:"host" gorm:"not null;size:255" binding:"required"`
	Port        int       `json:"port" gorm:"not null;default:22" binding:"required"`
	Username    string    `json:"username" gorm:"not null;size:100" binding:"required"`
	Password    string    `json:"password,omitempty" gorm:"size:255"` // 可选，支持密码认证
	PrivateKey  string    `json:"private_key,omitempty" gorm:"type:text"` // 可选，支持密钥认证
	Description string    `json:"description" gorm:"size:500"`
	UserID      uint      `json:"user_id" gorm:"not null;index"` // 创建者ID
	User        User      `json:"user" gorm:"foreignKey:UserID"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (SSHConnection) TableName() string {
	return "ssh_connections"
}

// SSHSession SSH会话信息
type SSHSession struct {
	ID           uint          `json:"id" gorm:"primaryKey"`
	ConnectionID uint          `json:"connection_id" gorm:"not null;index"`
	Connection   SSHConnection `json:"connection" gorm:"foreignKey:ConnectionID"`
	UserID       uint          `json:"user_id" gorm:"not null;index"`
	User         User          `json:"user" gorm:"foreignKey:UserID"`
	SessionID    string        `json:"session_id" gorm:"not null;unique;size:100"` // WebSocket会话ID
	Status       string        `json:"status" gorm:"not null;default:'connecting'"` // connecting, connected, disconnected, error
	StartTime    time.Time     `json:"start_time"`
	EndTime      *time.Time    `json:"end_time,omitempty"`
	LastActivity time.Time     `json:"last_activity"`
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
}

// TableName 指定表名
func (SSHSession) TableName() string {
	return "ssh_sessions"
}
