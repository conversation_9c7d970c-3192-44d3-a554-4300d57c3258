package handlers

import (
	"ReqFlow/models"
	"ReqFlow/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type UserHandler struct {
	db *gorm.DB
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	return &UserHandler{db: db}
}

// GetUsers 获取用户列表（仅管理员）
func (h *UserHandler) GetUsers(c *gin.Context) {
	var users []models.User
	if err := h.db.Preload("InviteCode").Preload("Team").Find(&users).Error; err != nil {
		utils.InternalServerError(c, "获取用户列表失败")
		return
	}

	// 构造返回数据，隐藏敏感信息
	var result []gin.H
	for _, user := range users {
		teamName := ""
		teamID := uint(0)
		if user.Team != nil {
			teamName = user.Team.Name
			teamID = user.Team.ID
		}

		result = append(result, gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"name":       user.Name,
			"email":      user.Email,
			"role":       user.Role,
			"is_active":  user.IsActive,
			"team_id":    teamID,
			"team_name":  teamName,
			"created_at": user.CreatedAt,
		})
	}

	utils.Success(c, result)
}

// ToggleUserStatus 启用/禁用用户（仅管理员）
func (h *UserHandler) ToggleUserStatus(c *gin.Context) {
	userID := c.Param("id")

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		utils.NotFound(c, "用户不存在")
		return
	}

	// 不能禁用管理员账号
	if user.Role == models.RoleAdmin {
		utils.BadRequest(c, "不能禁用管理员账号")
		return
	}

	// 切换状态
	user.IsActive = !user.IsActive
	if err := h.db.Save(&user).Error; err != nil {
		utils.InternalServerError(c, "更新用户状态失败")
		return
	}

	status := "禁用"
	if user.IsActive {
		status = "启用"
	}

	utils.SuccessWithMessage(c, "用户"+status+"成功", gin.H{
		"id":        user.ID,
		"username":  user.Username,
		"is_active": user.IsActive,
	})
}

// GetDevelopers 获取开发者列表
func (h *UserHandler) GetDevelopers(c *gin.Context) {
	var developers []models.User
	if err := h.db.Where("role = ? AND is_active = ?", models.RoleDeveloper, true).
		Find(&developers).Error; err != nil {
		utils.InternalServerError(c, "获取开发者列表失败")
		return
	}

	// 构造返回数据
	var result []gin.H
	for _, dev := range developers {
		result = append(result, gin.H{
			"id":       dev.ID,
			"username": dev.Username,
			"name":     dev.Name,
			"email":    dev.Email,
		})
	}

	utils.Success(c, result)
}

// CreateUserRequest 创建用户请求结构
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Name     string `json:"name" binding:"required,min=2,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=6"`
	Role     string `json:"role" binding:"required"`
	TeamID   uint   `json:"team_id" binding:"required"`
}

// UpdateUserRequest 更新用户请求结构
type UpdateUserRequest struct {
	Name     string `json:"name" binding:"required,min=2,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password"`
	Role     string `json:"role" binding:"required"`
	TeamID   uint   `json:"team_id" binding:"required"`
}

// CreateUser 创建用户（仅管理员）
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	// 检查用户名和邮箱是否已存在
	var existingUser models.User
	if err := h.db.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		utils.BadRequest(c, "用户名或邮箱已存在")
		return
	}

	// 验证角色
	validRoles := []string{string(models.RoleAdmin), string(models.RoleDeveloper), string(models.RoleUser)}
	roleValid := false
	for _, role := range validRoles {
		if req.Role == role {
			roleValid = true
			break
		}
	}
	if !roleValid {
		utils.BadRequest(c, "无效的角色")
		return
	}

	// 验证团队是否存在
	var team models.Team
	if err := h.db.First(&team, req.TeamID).Error; err != nil {
		utils.BadRequest(c, "无效的团队ID")
		return
	}

	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		utils.InternalServerError(c, "密码加密失败")
		return
	}

	// 获取一个可用的邀请码（用于满足数据库约束）
	var inviteCode models.InviteCode
	if err := h.db.Where("is_active = ?", true).First(&inviteCode).Error; err != nil {
		// 如果没有找到邀请码，创建一个临时的
		inviteCode = models.InviteCode{
			Code:     "ADMIN_CREATE",
			RoleType: models.UserRole(req.Role),
			TeamName: "Admin Created",
			MaxUses:  1000,
			IsActive: true,
		}
		if err := h.db.Create(&inviteCode).Error; err != nil {
			utils.InternalServerError(c, "创建邀请码失败")
			return
		}
	}

	// 创建用户
	user := models.User{
		Username:     req.Username,
		Name:         req.Name,
		Email:        req.Email,
		Password:     hashedPassword,
		Role:         models.UserRole(req.Role),
		TeamID:       req.TeamID,
		InviteCodeID: inviteCode.ID,
		IsActive:     true,
	}

	if err := h.db.Create(&user).Error; err != nil {
		utils.InternalServerError(c, "创建用户失败")
		return
	}

	utils.SuccessWithMessage(c, "用户创建成功", gin.H{
		"id":       user.ID,
		"username": user.Username,
		"name":     user.Name,
		"email":    user.Email,
		"role":     user.Role,
	})
}

// UpdateUser 更新用户（仅管理员）
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID := c.Param("id")

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "请求数据格式错误: "+err.Error())
		return
	}

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		utils.NotFound(c, "用户不存在")
		return
	}

	// 验证角色
	validRoles := []string{string(models.RoleAdmin), string(models.RoleDeveloper), string(models.RoleUser)}
	roleValid := false
	for _, role := range validRoles {
		if req.Role == role {
			roleValid = true
			break
		}
	}
	if !roleValid {
		utils.BadRequest(c, "无效的角色")
		return
	}

	// 验证团队是否存在
	var team models.Team
	if err := h.db.First(&team, req.TeamID).Error; err != nil {
		utils.BadRequest(c, "无效的团队ID")
		return
	}

	// 检查邮箱是否被其他用户使用
	var existingUser models.User
	if err := h.db.Where("email = ? AND id != ?", req.Email, userID).First(&existingUser).Error; err == nil {
		utils.BadRequest(c, "邮箱已存在")
		return
	}

	// 更新用户信息
	user.Name = req.Name
	user.Email = req.Email
	user.Role = models.UserRole(req.Role)
	user.TeamID = req.TeamID

	// 如果提供了密码，则更新密码
	if req.Password != "" {
		hashedPassword, err := utils.HashPassword(req.Password)
		if err != nil {
			utils.InternalServerError(c, "密码加密失败")
			return
		}
		user.Password = hashedPassword
	}

	if err := h.db.Save(&user).Error; err != nil {
		utils.InternalServerError(c, "更新用户失败")
		return
	}

	utils.SuccessWithMessage(c, "用户更新成功", gin.H{
		"id":       user.ID,
		"username": user.Username,
		"name":     user.Name,
		"email":    user.Email,
		"role":     user.Role,
	})
}

// DeleteUser 删除用户（仅管理员）
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")

	var user models.User
	if err := h.db.First(&user, userID).Error; err != nil {
		utils.NotFound(c, "用户不存在")
		return
	}

	// 不能删除管理员账号
	if user.Role == models.RoleAdmin {
		utils.BadRequest(c, "不能删除管理员账号")
		return
	}

	// 获取当前用户ID
	currentUserID, _ := c.Get("user_id")
	if user.ID == currentUserID {
		utils.BadRequest(c, "不能删除自己")
		return
	}

	if err := h.db.Delete(&user).Error; err != nil {
		utils.InternalServerError(c, "删除用户失败")
		return
	}

	utils.SuccessWithMessage(c, "用户删除成功", gin.H{
		"id": user.ID,
	})
}
