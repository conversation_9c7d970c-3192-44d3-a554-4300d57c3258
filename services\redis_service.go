package services

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"ReqFlow/models"

	"github.com/go-redis/redis/v8"
)

// RedisService Redis服务
type RedisService struct {
	clients map[uint]*redis.Client
}

// NewRedisService 创建Redis服务实例
func NewRedisService() *RedisService {
	return &RedisService{
		clients: make(map[uint]*redis.Client),
	}
}

// GetClient 获取Redis客户端
func (rs *RedisService) GetClient(conn *models.RedisConnection) (*redis.Client, error) {
	if client, exists := rs.clients[conn.ID]; exists {
		// 测试连接是否有效
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := client.Ping(ctx).Err(); err == nil {
			return client, nil
		}
		// 连接无效，删除并重新创建
		delete(rs.clients, conn.ID)
	}

	// 创建新连接
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", conn.Host, conn.Port),
		Password: conn.Password,
		DB:       conn.Database,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("连接Redis失败: %v", err)
	}

	rs.clients[conn.ID] = client
	return client, nil
}

// CloseClient 关闭Redis客户端
func (rs *RedisService) CloseClient(connID uint) {
	if client, exists := rs.clients[connID]; exists {
		client.Close()
		delete(rs.clients, connID)
	}
}

// TestConnection 测试Redis连接
func (rs *RedisService) TestConnection(conn *models.RedisConnection) error {
	client := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", conn.Host, conn.Port),
		Password: conn.Password,
		DB:       conn.Database,
	})
	defer client.Close()

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return client.Ping(ctx).Err()
}

// GetInfo 获取Redis服务器信息
func (rs *RedisService) GetInfo(conn *models.RedisConnection) (*models.RedisInfo, error) {
	client, err := rs.GetClient(conn)
	if err != nil {
		return nil, err
	}

	ctx := context.Background()

	// 获取服务器信息
	info, err := client.Info(ctx, "server", "memory", "stats", "replication").Result()
	if err != nil {
		return nil, err
	}

	// 解析信息
	infoMap := parseRedisInfo(info)

	// 获取数据库键数量
	totalKeys := int64(0)
	for i := 0; i < 16; i++ {
		dbSize, _ := client.DBSize(ctx).Result()
		totalKeys += dbSize
	}

	redisInfo := &models.RedisInfo{
		Version:         infoMap["redis_version"],
		Mode:            infoMap["redis_mode"],
		Role:            infoMap["role"],
		UsedMemory:      infoMap["used_memory"],
		UsedMemoryHuman: infoMap["used_memory_human"],
		TotalKeys:       totalKeys,
		Stats:           infoMap,
	}

	// 解析连接数
	if clients, ok := infoMap["connected_clients"]; ok {
		if count, err := strconv.Atoi(clients); err == nil {
			redisInfo.ConnectedClients = count
		}
	}

	// 解析运行时间
	if uptime, ok := infoMap["uptime_in_seconds"]; ok {
		if seconds, err := strconv.ParseInt(uptime, 10, 64); err == nil {
			redisInfo.Uptime = seconds
		}
	}

	return redisInfo, nil
}

// GetDatabases 获取数据库列表
func (rs *RedisService) GetDatabases(conn *models.RedisConnection) ([]models.RedisDatabase, error) {
	_, err := rs.GetClient(conn)
	if err != nil {
		return nil, err
	}

	ctx := context.Background()
	databases := make([]models.RedisDatabase, 0)

	for i := 0; i < 16; i++ {
		// 切换到指定数据库
		tempClient := redis.NewClient(&redis.Options{
			Addr:     fmt.Sprintf("%s:%d", conn.Host, conn.Port),
			Password: conn.Password,
			DB:       i,
		})

		dbSize, err := tempClient.DBSize(ctx).Result()
		tempClient.Close()

		if err != nil {
			continue
		}

		databases = append(databases, models.RedisDatabase{
			Index: i,
			Keys:  dbSize,
		})
	}

	return databases, nil
}

// parseRedisInfo 解析Redis INFO命令返回的信息
func parseRedisInfo(info string) map[string]string {
	result := make(map[string]string)
	lines := strings.Split(info, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, ":", 2)
		if len(parts) == 2 {
			result[parts[0]] = parts[1]
		}
	}

	return result
}

// GetKeys 获取键列表
func (rs *RedisService) GetKeys(conn *models.RedisConnection, pattern string, cursor uint64, count int64) ([]models.RedisKey, uint64, error) {
	client, err := rs.GetClient(conn)
	if err != nil {
		return nil, 0, err
	}

	ctx := context.Background()

	if pattern == "" {
		pattern = "*"
	}

	// 使用SCAN命令获取键
	keys, nextCursor, err := client.Scan(ctx, cursor, pattern, count).Result()
	if err != nil {
		return nil, 0, err
	}

	redisKeys := make([]models.RedisKey, 0, len(keys))

	for _, key := range keys {
		keyType, _ := client.Type(ctx, key).Result()
		ttl, _ := client.TTL(ctx, key).Result()

		var size int64
		switch keyType {
		case "string":
			size, _ = client.StrLen(ctx, key).Result()
		case "list":
			size, _ = client.LLen(ctx, key).Result()
		case "set":
			size, _ = client.SCard(ctx, key).Result()
		case "zset":
			size, _ = client.ZCard(ctx, key).Result()
		case "hash":
			size, _ = client.HLen(ctx, key).Result()
		}

		redisKeys = append(redisKeys, models.RedisKey{
			Key:  key,
			Type: keyType,
			TTL:  int64(ttl.Seconds()),
			Size: size,
		})
	}

	return redisKeys, nextCursor, nil
}

// GetValue 获取键值
func (rs *RedisService) GetValue(conn *models.RedisConnection, key string) (*models.RedisKey, error) {
	client, err := rs.GetClient(conn)
	if err != nil {
		return nil, err
	}

	ctx := context.Background()

	// 检查键是否存在
	exists, err := client.Exists(ctx, key).Result()
	if err != nil {
		return nil, err
	}
	if exists == 0 {
		return nil, fmt.Errorf("键不存在")
	}

	keyType, err := client.Type(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	ttl, _ := client.TTL(ctx, key).Result()

	redisKey := &models.RedisKey{
		Key:  key,
		Type: keyType,
		TTL:  int64(ttl.Seconds()),
	}

	// 根据类型获取值
	switch keyType {
	case "string":
		value, err := client.Get(ctx, key).Result()
		if err != nil {
			return nil, err
		}
		redisKey.Value = value
		redisKey.Size, _ = client.StrLen(ctx, key).Result()

	case "list":
		value, err := client.LRange(ctx, key, 0, -1).Result()
		if err != nil {
			return nil, err
		}
		redisKey.Value = value
		redisKey.Size, _ = client.LLen(ctx, key).Result()

	case "set":
		value, err := client.SMembers(ctx, key).Result()
		if err != nil {
			return nil, err
		}
		redisKey.Value = value
		redisKey.Size, _ = client.SCard(ctx, key).Result()

	case "zset":
		value, err := client.ZRangeWithScores(ctx, key, 0, -1).Result()
		if err != nil {
			return nil, err
		}
		redisKey.Value = value
		redisKey.Size, _ = client.ZCard(ctx, key).Result()

	case "hash":
		value, err := client.HGetAll(ctx, key).Result()
		if err != nil {
			return nil, err
		}
		redisKey.Value = value
		redisKey.Size, _ = client.HLen(ctx, key).Result()

	default:
		return nil, fmt.Errorf("不支持的键类型: %s", keyType)
	}

	return redisKey, nil
}

// SetValue 设置键值
func (rs *RedisService) SetValue(conn *models.RedisConnection, key, keyType string, value interface{}, ttl time.Duration) error {
	client, err := rs.GetClient(conn)
	if err != nil {
		return err
	}

	ctx := context.Background()

	switch keyType {
	case "string":
		if strValue, ok := value.(string); ok {
			return client.Set(ctx, key, strValue, ttl).Err()
		}
		return fmt.Errorf("字符串类型值必须是字符串")

	default:
		return fmt.Errorf("暂不支持设置 %s 类型的值", keyType)
	}
}

// DeleteKey 删除键
func (rs *RedisService) DeleteKey(conn *models.RedisConnection, key string) error {
	client, err := rs.GetClient(conn)
	if err != nil {
		return err
	}

	ctx := context.Background()
	return client.Del(ctx, key).Err()
}

// ExecuteCommand 执行Redis命令
func (rs *RedisService) ExecuteCommand(conn *models.RedisConnection, command []interface{}) (interface{}, error) {
	client, err := rs.GetClient(conn)
	if err != nil {
		return nil, err
	}

	ctx := context.Background()
	return client.Do(ctx, command...).Result()
}
