package models

import (
	"time"

	"gorm.io/gorm"
)

// Comment 评论模型
type Comment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	TicketID  uint           `json:"ticket_id" gorm:"not null"`
	UserID    uint           `json:"user_id" gorm:"not null"`
	Content   string         `json:"content" gorm:"type:longtext;not null"`
	Images    string         `json:"images" gorm:"type:json"` // JSON数组存储图片URL
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Ticket *Ticket `json:"ticket,omitempty" gorm:"foreignKey:TicketID"`
	User   *User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (Comment) TableName() string {
	return "comments"
}
