package controllers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"ReqFlow/services"
	"ReqFlow/utils"
)

type SystemController struct {
	systemService *services.SystemService
}

func NewSystemController(systemService *services.SystemService) *SystemController {
	return &SystemController{
		systemService: systemService,
	}
}

// GetSystemInfo 获取系统信息
// @Summary 获取系统信息
// @Description 获取服务器硬件、软件、运行时等完整系统信息
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=models.SystemInfo}
// @Failure 500 {object} utils.Response
// @Router /api/system/info [get]
// @Security BearerAuth
func (sc *SystemController) GetSystemInfo(c *gin.Context) {
	systemInfo, err := sc.systemService.GetSystemInfo()
	if err != nil {
		utils.Error(c, http.StatusInternalServerError, "获取系统信息失败")
		return
	}

	utils.SuccessWithMessage(c, "获取系统信息成功", systemInfo)
}

// GetSystemStats 获取系统实时统计信息
// @Summary 获取系统实时统计信息
// @Description 获取CPU、内存、磁盘使用率等实时监控数据
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=map[string]interface{}}
// @Failure 500 {object} utils.Response
// @Router /api/system/stats [get]
// @Security BearerAuth
func (sc *SystemController) GetSystemStats(c *gin.Context) {
	stats, err := sc.systemService.GetSystemStats()
	if err != nil {
		utils.Error(c, http.StatusInternalServerError, "获取系统统计信息失败")
		return
	}

	utils.SuccessWithMessage(c, "获取系统统计信息成功", stats)
}

// GetSystemStatsHistory 获取系统统计历史数据
// @Summary 获取系统统计历史数据
// @Description 获取指定时间范围内的系统监控历史数据
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param duration query string false "时间范围" default(1h) Enums(1h, 6h, 24h, 7d)
// @Param interval query string false "数据间隔" default(1m) Enums(1m, 5m, 15m, 1h)
// @Success 200 {object} utils.Response{data=[]map[string]interface{}}
// @Failure 400 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/system/stats/history [get]
// @Security BearerAuth
func (sc *SystemController) GetSystemStatsHistory(c *gin.Context) {
	duration := c.DefaultQuery("duration", "1h")
	interval := c.DefaultQuery("interval", "1m")

	// 解析时间范围
	var durationTime time.Duration
	switch duration {
	case "1h":
		durationTime = time.Hour
	case "6h":
		durationTime = 6 * time.Hour
	case "24h":
		durationTime = 24 * time.Hour
	case "7d":
		durationTime = 7 * 24 * time.Hour
	default:
		utils.Error(c, http.StatusBadRequest, "无效的时间范围")
		return
	}

	// 解析数据间隔
	var intervalTime time.Duration
	switch interval {
	case "1m":
		intervalTime = time.Minute
	case "5m":
		intervalTime = 5 * time.Minute
	case "15m":
		intervalTime = 15 * time.Minute
	case "1h":
		intervalTime = time.Hour
	default:
		utils.Error(c, http.StatusBadRequest, "无效的数据间隔")
		return
	}

	// 生成模拟历史数据（实际项目中应该从数据库或监控系统获取）
	historyData := sc.generateMockHistoryData(durationTime, intervalTime)

	utils.SuccessWithMessage(c, "获取系统统计历史数据成功", historyData)
}

// GetSystemHealth 获取系统健康状态
// @Summary 获取系统健康状态
// @Description 获取系统各组件的健康状态检查结果
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response{data=map[string]interface{}}
// @Failure 500 {object} utils.Response
// @Router /api/system/health [get]
// @Security BearerAuth
func (sc *SystemController) GetSystemHealth(c *gin.Context) {
	health := map[string]interface{}{
		"status": "healthy",
		"timestamp": time.Now(),
		"checks": map[string]interface{}{
			"database": map[string]interface{}{
				"status": "healthy",
				"response_time": "2ms",
				"last_check": time.Now(),
			},
			"memory": map[string]interface{}{
				"status": "healthy",
				"usage": "45%",
				"last_check": time.Now(),
			},
			"disk": map[string]interface{}{
				"status": "healthy",
				"usage": "60%",
				"last_check": time.Now(),
			},
			"cpu": map[string]interface{}{
				"status": "healthy",
				"usage": "25%",
				"last_check": time.Now(),
			},
		},
	}

	utils.SuccessWithMessage(c, "获取系统健康状态成功", health)
}

// RestartService 重启服务（谨慎操作）
// @Summary 重启服务
// @Description 重启应用服务（需要管理员权限）
// @Tags 系统管理
// @Accept json
// @Produce json
// @Success 200 {object} utils.Response
// @Failure 403 {object} utils.Response
// @Failure 500 {object} utils.Response
// @Router /api/system/restart [post]
// @Security BearerAuth
func (sc *SystemController) RestartService(c *gin.Context) {
	// 这是一个危险操作，需要额外的权限检查
	userID, exists := c.Get("user_id")
	if !exists {
		utils.Error(c, http.StatusUnauthorized, "用户未登录")
		return
	}

	// 检查是否为超级管理员
	// 这里应该有更严格的权限检查
	utils.SuccessWithMessage(c, "重启请求已接收", map[string]interface{}{
		"message": "服务将在5秒后重启",
		"user_id": userID,
		"timestamp": time.Now(),
	})

	// 实际的重启逻辑应该在这里实现
	// 注意：这是一个危险操作，需要谨慎处理
}

// generateMockHistoryData 生成模拟历史数据
func (sc *SystemController) generateMockHistoryData(duration, interval time.Duration) []map[string]interface{} {
	var data []map[string]interface{}
	
	points := int(duration / interval)
	if points > 1000 {
		points = 1000 // 限制数据点数量
	}

	now := time.Now()
	for i := points; i >= 0; i-- {
		timestamp := now.Add(-time.Duration(i) * interval)
		
		// 生成模拟数据（实际项目中应该从监控数据库获取）
		data = append(data, map[string]interface{}{
			"timestamp":    timestamp.Unix(),
			"cpu_usage":    50 + float64(i%20) - 10, // 模拟CPU使用率波动
			"memory_usage": 60 + float64(i%15) - 7,  // 模拟内存使用率波动
			"disk_usage":   70 + float64(i%10) - 5,  // 模拟磁盘使用率波动
			"goroutines":   100 + i%50,              // 模拟协程数量波动
			"heap_alloc":   1024*1024*50 + (i%20)*1024*1024, // 模拟堆内存分配
		})
	}

	return data
}

// GetSystemLogs 获取系统日志
// @Summary 获取系统日志
// @Description 获取系统运行日志
// @Tags 系统管理
// @Accept json
// @Produce json
// @Param level query string false "日志级别" Enums(debug, info, warn, error)
// @Param limit query int false "返回条数" default(100)
// @Param offset query int false "偏移量" default(0)
// @Success 200 {object} utils.Response{data=[]map[string]interface{}}
// @Failure 400 {object} utils.Response
// @Router /api/system/logs [get]
// @Security BearerAuth
func (sc *SystemController) GetSystemLogs(c *gin.Context) {
	level := c.DefaultQuery("level", "")
	limitStr := c.DefaultQuery("limit", "100")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 1000 {
		limit = 100
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil || offset < 0 {
		offset = 0
	}

	// 模拟日志数据（实际项目中应该从日志文件或日志系统获取）
	logs := []map[string]interface{}{
		{
			"id":        1,
			"timestamp": time.Now().Add(-time.Hour),
			"level":     "info",
			"message":   "系统启动成功",
			"module":    "system",
		},
		{
			"id":        2,
			"timestamp": time.Now().Add(-30 * time.Minute),
			"level":     "warn",
			"message":   "内存使用率较高: 85%",
			"module":    "monitor",
		},
		{
			"id":        3,
			"timestamp": time.Now().Add(-15 * time.Minute),
			"level":     "error",
			"message":   "数据库连接超时",
			"module":    "database",
		},
	}

	// 根据级别过滤
	if level != "" {
		var filteredLogs []map[string]interface{}
		for _, log := range logs {
			if log["level"] == level {
				filteredLogs = append(filteredLogs, log)
			}
		}
		logs = filteredLogs
	}

	// 应用分页
	total := len(logs)
	if offset >= total {
		logs = []map[string]interface{}{}
	} else {
		end := offset + limit
		if end > total {
			end = total
		}
		logs = logs[offset:end]
	}

	result := map[string]interface{}{
		"logs":  logs,
		"total": total,
		"limit": limit,
		"offset": offset,
	}

	utils.SuccessWithMessage(c, "获取系统日志成功", result)
}
