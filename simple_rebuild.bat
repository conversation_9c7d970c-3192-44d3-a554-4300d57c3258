@echo off
echo ReqFlow Database Rebuild Script
echo ================================

REM Database configuration
set DB_HOST=localhost
set DB_PORT=3306
set DB_USER=root
set DB_PASSWORD=123456
set DB_NAME=req_flow

echo.
echo Step 1: Testing MySQL connection...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "SELECT 1;" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Cannot connect to MySQL
    echo Please check:
    echo - MySQL server is running
    echo - Username: %DB_USER%
    echo - Password: %DB_PASSWORD%
    echo - Host: %DB_HOST%:%DB_PORT%
    pause
    exit /b 1
)
echo MySQL connection OK

echo.
echo Step 2: Dropping existing database...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "DROP DATABASE IF EXISTS %DB_NAME%;"
if errorlevel 1 (
    echo ERROR: Failed to drop database
    pause
    exit /b 1
)
echo Database dropped

echo.
echo Step 3: Creating new database...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "CREATE DATABASE %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
if errorlevel 1 (
    echo ERROR: Failed to create database
    pause
    exit /b 1
)
echo Database created

echo.
echo Step 4: Importing schema and data...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% %DB_NAME% < export_innodb.sql
if errorlevel 1 (
    echo ERROR: Failed to import data
    echo Make sure export_innodb.sql exists in current directory
    pause
    exit /b 1
)
echo Data imported successfully

echo.
echo Step 5: Verifying tables...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASSWORD% -e "USE %DB_NAME%; SHOW TABLES;"

echo.
echo SUCCESS: Database rebuild completed!
echo.
echo Database: %DB_NAME%
echo Engine: InnoDB
echo.
echo Default login:
echo Username: admin
echo Password: admin123456
echo.
pause
