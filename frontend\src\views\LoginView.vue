<template>
  <div class="login-container">
    <div class="login-wrapper">
      <!-- 左侧装饰区域 -->
      <div class="login-decoration">
        <div class="decoration-content">
          <div class="logo-section">
            <div class="logo-icon">
              <el-icon :size="48"><Document /></el-icon>
            </div>
            <h1 class="logo-title">ReqFlow</h1>
            <p class="logo-subtitle">工单管理系统</p>
          </div>
          <div class="feature-list">
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>高效的工单管理</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>团队协作无缝对接</span>
            </div>
            <div class="feature-item">
              <el-icon><Check /></el-icon>
              <span>实时状态跟踪</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表单区域 -->
      <div class="login-form-section">
        <div class="form-container">
          <div class="form-header">
            <h2>{{ isLogin ? '欢迎回来' : '创建账户' }}</h2>
            <p>{{ isLogin ? '登录您的账户以继续' : '填写信息创建新账户' }}</p>
          </div>
      
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            class="login-form"
            size="large"
          >
            <el-form-item prop="username">
              <el-input
                v-model="form.username"
                :prefix-icon="User"
                placeholder="用户名"
                class="form-input"
              />
            </el-form-item>

            <el-form-item v-if="!isLogin" prop="name">
              <el-input
                v-model="form.name"
                :prefix-icon="User"
                placeholder="真实姓名"
                class="form-input"
              />
            </el-form-item>

            <el-form-item v-if="!isLogin" prop="email">
              <el-input
                v-model="form.email"
                :prefix-icon="Message"
                placeholder="邮箱地址"
                class="form-input"
              />
            </el-form-item>
        
            <el-form-item prop="password">
              <el-input
                v-model="form.password"
                type="password"
                :prefix-icon="Lock"
                placeholder="密码"
                show-password
                class="form-input"
              />
            </el-form-item>

            <el-form-item v-if="!isLogin" prop="confirmPassword">
              <el-input
                v-model="form.confirmPassword"
                type="password"
                :prefix-icon="Lock"
                placeholder="确认密码"
                show-password
                class="form-input"
              />
            </el-form-item>

            <el-form-item v-if="!isLogin" prop="inviteCode">
              <el-input
                v-model="form.inviteCode"
                placeholder="邀请码"
                class="form-input"
              />
            </el-form-item>
        
            <el-form-item class="submit-item">
              <el-button
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                class="submit-btn"
                size="large"
              >
                {{ isLogin ? '登录' : '注册' }}
              </el-button>
            </el-form-item>

            <div class="form-footer">
              <el-button
                link
                @click="toggleMode"
                class="toggle-btn"
              >
                {{ isLogin ? '没有账号？立即注册' : '已有账号？立即登录' }}
              </el-button>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Message, Document, Check } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

const isLogin = ref(true)
const loading = ref(false)
const formRef = ref<FormInstance>()

const form = reactive({
  username: '',
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  inviteCode: ''
})

const validateConfirmPassword = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== form.password) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

const rules = reactive<FormRules>({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 50, message: '姓名长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于 6 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, validator: validateConfirmPassword, trigger: 'blur' }
  ],
  inviteCode: [
    { required: true, message: '请输入邀请码', trigger: 'blur' }
  ]
})

const toggleMode = () => {
  isLogin.value = !isLogin.value
  resetForm()
}

const resetForm = () => {
  form.username = ''
  form.name = ''
  form.email = ''
  form.password = ''
  form.confirmPassword = ''
  form.inviteCode = ''
  formRef.value?.clearValidate()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      
      try {
        if (isLogin.value) {
          // 登录
          const result = await userStore.login({
            username: form.username,
            password: form.password
          })
          
          if (result.success) {
            ElMessage.success(result.message)
            router.push('/dashboard')
          } else {
            ElMessage.error(result.message)
          }
        } else {
          // 注册
          const result = await userStore.register({
            username: form.username,
            name: form.name,
            email: form.email,
            password: form.password,
            invite_code: form.inviteCode
          })
          
          if (result.success) {
            ElMessage.success(result.message)
            isLogin.value = true
            resetForm()
          } else {
            ElMessage.error(result.message)
          }
        }
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  overflow: auto; /* 登录页面允许滚动 */
  box-sizing: border-box;
}

.login-wrapper {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 左侧装饰区域 */
.login-decoration {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  position: relative;
}

.login-decoration::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.decoration-content {
  text-align: center;
  color: white;
  z-index: 1;
  position: relative;
}

.logo-section {
  margin-bottom: 40px;
}

.logo-icon {
  margin-bottom: 20px;
}

.logo-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  font-weight: 300;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  opacity: 0.9;
}

.feature-item .el-icon {
  font-size: 20px;
  color: #4ade80;
}

/* 右侧表单区域 */
.login-form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  background: #fafafa;
}

.form-container {
  width: 100%;
  max-width: 360px;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-header h2 {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.form-header p {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

.login-form {
  width: 100%;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.form-input {
  height: 50px;
  border-radius: 12px;
}

.form-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  box-shadow: 0 0 0 1px #e5e7eb;
  transition: all 0.3s ease;
}

.form-input :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #667eea;
}

.form-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px #667eea;
}

.submit-item {
  margin-bottom: 20px;
}

.submit-btn {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.form-footer {
  text-align: center;
}

.toggle-btn {
  color: #667eea;
  font-size: 14px;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  color: #764ba2;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column;
    max-width: 400px;
    min-height: auto;
  }

  .login-decoration {
    padding: 40px 20px;
  }

  .logo-title {
    font-size: 36px;
  }

  .feature-list {
    display: none;
  }

  .login-form-section {
    padding: 40px 20px;
  }

  .form-header h2 {
    font-size: 28px;
  }
}
</style>
