<template>
  <div class="xshell-container">
    <!-- 连接列表和终端区域 -->
    <div class="main-content">
      <!-- 左侧连接列表和监控面板 -->
      <div class="connection-sidebar">
        <!-- SSH连接列表 -->
        <div class="connections-section">
          <div class="sidebar-header">
            <h3>SSH 连接</h3>
            <div class="header-actions">
              <el-button size="small" type="primary" @click="showConnectionDialog = true">
                <el-icon><Plus /></el-icon>
                新建
              </el-button>
              <el-button size="small" text @click="loadConnections">
                <el-icon><Refresh /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="connection-list">
            <div
              v-for="connection in connections"
              :key="connection.id"
              class="connection-item"
              :class="{ active: activeConnection?.id === connection.id }"
              @click="selectConnection(connection)"
            >
              <div class="connection-info">
                <div class="connection-name">{{ connection.name }}</div>
                <div class="connection-details">{{ connection.username }}@{{ connection.host }}:{{ connection.port }}</div>
              </div>
              <div class="connection-actions">
                <el-dropdown @command="handleConnectionAction">
                  <el-button size="small" text>
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="`edit-${connection.id}`">编辑</el-dropdown-item>
                      <el-dropdown-item :command="`test-${connection.id}`">测试连接</el-dropdown-item>
                      <el-dropdown-item :command="`delete-${connection.id}`" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>

        <!-- 服务器资源监控面板 -->
        <div class="monitor-section" v-if="activeTab && activeTab.status === 'connected' && activeConnection">
          <div class="monitor-header">
            <h3>服务器监控</h3>
            <el-button size="small" text @click="refreshServerInfo">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
          <div class="monitor-content">
            <div class="resource-item">
              <div class="resource-label">CPU使用率</div>
              <el-progress
                :percentage="serverInfo.cpu || 0"
                :color="getProgressColor(serverInfo.cpu || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.cpu || 0).toFixed(2) }}%
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-label">内存使用率</div>
              <el-progress
                :percentage="serverInfo.memory || 0"
                :color="getProgressColor(serverInfo.memory || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.memory || 0).toFixed(2) }}% - {{ (serverInfo.memoryUsed || 0).toFixed(1) }}GB / {{ (serverInfo.memoryTotal || 0).toFixed(1) }}GB
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-label">硬盘使用率</div>
              <el-progress
                :percentage="serverInfo.disk || 0"
                :color="getProgressColor(serverInfo.disk || 0)"
                :show-text="false"
                :stroke-width="6"
              />
              <div class="resource-detail">
                {{ (serverInfo.disk || 0).toFixed(2) }}% - {{ (serverInfo.diskUsed || 0).toFixed(1) }}GB / {{ (serverInfo.diskTotal || 0).toFixed(1) }}GB
              </div>
            </div>
            <div class="resource-item">
              <div class="resource-label">系统负载</div>
              <div class="load-info">
                <div class="load-item">
                  <span class="load-label">1分钟</span>
                  <span class="load-value">{{ serverInfo.load1 || '0.00' }}</span>
                </div>
                <div class="load-item">
                  <span class="load-label">5分钟</span>
                  <span class="load-value">{{ serverInfo.load5 || '0.00' }}</span>
                </div>
                <div class="load-item">
                  <span class="load-label">15分钟</span>
                  <span class="load-value">{{ serverInfo.load15 || '0.00' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧终端和文件管理区域 -->
      <div class="right-panel">
        <!-- 页面操作按钮 -->
        <div class="page-actions">
          <el-button size="small" @click="openInNewTab" title="在新标签页打开">
            <el-icon><Link /></el-icon>
            新标签页
          </el-button>
        </div>

        <!-- 终端和文件管理区域 -->
        <div class="terminal-and-file">
          <!-- 终端区域 -->
          <div class="terminal-area">
          <div v-if="openTabs.length === 0" class="no-connection">
            <el-empty description="请选择一个SSH连接开始使用终端" />
          </div>
          <div v-else class="terminal-container">
          <!-- 终端标签页 -->
          <div class="terminal-tabs">
            <div
              v-for="tab in openTabs"
              :key="tab.id"
              class="tab-item"
              :class="{ active: activeTabId === tab.id }"
              @click="switchTab(tab.id)"
            >
              <span>{{ tab.connection.name }}</span>
              <el-button size="small" text @click.stop="closeTab(tab.id)">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>
          
          <!-- 终端内容 -->
          <div
            v-for="tab in openTabs"
            :key="`terminal-${tab.id}`"
            class="terminal-content"
            :class="{ active: activeTabId === tab.id }"
            :ref="el => setTerminalRef(el, tab.id)"
          ></div>

          <!-- 命令输入区域 -->
          <div class="command-input-area" v-if="activeTab && activeTab.status === 'connected'">
            <!-- 快捷命令栏 -->
            <div class="quick-commands">
              <div class="quick-commands-header">
                <span>快捷命令:</span>
                <el-button size="small" text @click="showQuickCommandDialog = true">
                  <el-icon><Setting /></el-icon>
                  管理
                </el-button>
              </div>
              <div class="quick-command-buttons">
                <el-button
                  v-for="cmd in quickCommands"
                  :key="cmd.id"
                  size="small"
                  @click="executeQuickCommand(cmd.command)"
                  :title="cmd.description"
                >
                  {{ cmd.name }}
                </el-button>
              </div>
            </div>

            <!-- 命令输入框 -->
            <div class="command-input">
              <el-input
                v-model="currentCommand"
                placeholder="输入命令..."
                @keyup.enter="executeCommand"
                @keyup.up="navigateHistory(-1)"
                @keyup.down="navigateHistory(1)"
                @keyup.tab.prevent="autoComplete"
                ref="commandInput"
              >
                <template #prepend>
                  <span class="command-prompt">{{ commandPrompt }}</span>
                </template>
                <template #append>
                  <el-button @click="executeCommand" :disabled="!currentCommand.trim()">
                    执行
                  </el-button>
                </template>
              </el-input>
            </div>
          </div>

          <!-- 连接状态 -->
          <div class="terminal-status" v-if="activeTab">
            <span class="status-indicator" :class="activeTab.status">{{ getStatusText(activeTab.status) }}</span>
            <span class="connection-info">{{ activeTab.connection.username }}@{{ activeTab.connection.host }}:{{ activeTab.connection.port }}</span>
          </div>
          </div>
        </div>

        <!-- 文件管理区域 -->
        <div class="file-manager-area" v-if="activeTab">
          <div class="file-manager-header">
            <div class="header-left">
              <h3>文件管理</h3>
              <el-breadcrumb separator="/" class="path-breadcrumb">
                <el-breadcrumb-item
                  v-for="(path, index) in pathBreadcrumb"
                  :key="index"
                  @click="navigateToPath(index)"
                  class="breadcrumb-item"
                >
                  {{ path }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            <div class="header-actions">
              <el-button size="small" @click="refreshFileList">
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button size="small" @click="showCreateFolderDialog = true">
                <el-icon><FolderAdd /></el-icon>
              </el-button>
              <el-upload
                ref="uploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="uploadData"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :show-file-list="false"
                multiple
              >
                <el-button size="small">
                  <el-icon><Upload /></el-icon>
                </el-button>
              </el-upload>
            </div>
          </div>

          <div class="file-list-container">
            <el-table
              :data="fileList"
              v-loading="fileLoading"
              @row-dblclick="handleFileDoubleClick"
              size="small"
              height="100%"
            >
              <el-table-column prop="name" label="文件名" min-width="150">
                <template #default="{ row }">
                  <div class="file-item" :class="{ 'parent-dir': row.name === '..' }">
                    <el-icon class="file-icon" :class="getFileIconClass(row)">
                      <component :is="getFileIcon(row)" />
                    </el-icon>
                    <span class="file-name">
                      {{ row.name === '..' ? '返回上级' : row.name }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="size" label="大小" width="80">
                <template #default="{ row }">
                  {{ row.name === '..' ? '' : (row.isDirectory ? '-' : formatFileSize(row.size)) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <template v-if="row.name !== '..'">
                    <el-button
                      v-if="!row.isDirectory"
                      size="small"
                      text
                      @click="downloadFile(row)"
                      title="下载"
                    >
                      <el-icon><Download /></el-icon>
                    </el-button>
                    <el-button
                      size="small"
                      text
                      type="danger"
                      @click="deleteFile(row)"
                      title="删除"
                    >
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑连接对话框 -->
    <el-dialog
      v-model="showConnectionDialog"
      :title="editingConnection ? '编辑SSH连接' : '新建SSH连接'"
      width="600px"
      @close="resetConnectionForm"
    >
      <el-form
        ref="connectionFormRef"
        :model="connectionForm"
        :rules="connectionRules"
        label-width="100px"
      >
        <el-form-item label="连接名称" prop="name">
          <el-input v-model="connectionForm.name" placeholder="请输入连接名称" />
        </el-form-item>
        
        <el-form-item label="主机地址" prop="host">
          <el-input v-model="connectionForm.host" placeholder="请输入主机地址或IP" />
        </el-form-item>
        
        <el-form-item label="端口" prop="port">
          <el-input-number v-model="connectionForm.port" :min="1" :max="65535" />
        </el-form-item>
        
        <el-form-item label="用户名" prop="username">
          <el-input v-model="connectionForm.username" placeholder="请输入用户名" />
        </el-form-item>
        
        <el-form-item label="认证方式">
          <el-radio-group v-model="authType">
            <el-radio label="password">密码认证</el-radio>
            <el-radio label="key">私钥认证</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item v-if="authType === 'password'" label="密码" prop="password">
          <el-input
            v-model="connectionForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
        
        <el-form-item v-if="authType === 'key'" label="私钥" prop="private_key">
          <el-input
            v-model="connectionForm.private_key"
            type="textarea"
            :rows="6"
            placeholder="请粘贴私钥内容"
          />
        </el-form-item>
        
        <el-form-item label="描述">
          <el-input
            v-model="connectionForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入连接描述（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showConnectionDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConnection" :loading="saving">
          {{ editingConnection ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 快捷命令管理对话框 -->
    <el-dialog
      v-model="showQuickCommandDialog"
      title="快捷命令管理"
      width="800px"
      @close="resetQuickCommandForm"
    >
      <div class="quick-command-manager">
        <div class="manager-header">
          <el-button type="primary" @click="addQuickCommand">
            <el-icon><Plus /></el-icon>
            添加命令
          </el-button>
        </div>

        <el-table :data="quickCommands" style="width: 100%">
          <el-table-column prop="name" label="名称" width="120" />
          <el-table-column prop="command" label="命令" />
          <el-table-column prop="description" label="描述" width="150" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" @click="editQuickCommand(scope.row)">编辑</el-button>
              <el-button size="small" type="danger" @click="deleteQuickCommand(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <el-button @click="showQuickCommandDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 快捷命令编辑对话框 -->
    <el-dialog
      v-model="showQuickCommandEditDialog"
      :title="editingQuickCommand ? '编辑快捷命令' : '添加快捷命令'"
      width="500px"
    >
      <el-form
        ref="quickCommandFormRef"
        :model="quickCommandForm"
        :rules="quickCommandRules"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="quickCommandForm.name" placeholder="请输入命令名称" />
        </el-form-item>

        <el-form-item label="命令" prop="command">
          <el-input v-model="quickCommandForm.command" placeholder="请输入命令内容" />
        </el-form-item>

        <el-form-item label="描述">
          <el-input v-model="quickCommandForm.description" placeholder="请输入命令描述（可选）" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showQuickCommandEditDialog = false">取消</el-button>
        <el-button type="primary" @click="saveQuickCommand">保存</el-button>
      </template>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog
      v-model="showCreateFolderDialog"
      title="新建文件夹"
      width="400px"
    >
      <el-form>
        <el-form-item label="文件夹名称">
          <el-input v-model="newFolderName" placeholder="请输入文件夹名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateFolderDialog = false">取消</el-button>
        <el-button type="primary" @click="createFolder">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Refresh,
  MoreFilled,
  Close,
  Setting,
  FolderAdd,
  Upload,
  Download,
  Delete,
  Folder,
  Document,
  ArrowUp,
  Link
} from '@element-plus/icons-vue'
import { Terminal } from '@xterm/xterm'
import { FitAddon } from '@xterm/addon-fit'
import { WebLinksAddon } from '@xterm/addon-web-links'
import '@xterm/xterm/css/xterm.css'

import {
  type SSHConnection,
  getSSHConnections,
  createSSHConnection,
  updateSSHConnection,
  deleteSSHConnection,
  testSSHConnection,
  getServerInfo,
  SSHWebSocket
} from '@/api/ssh'

// SFTP相关API
import {
  type FileInfo,
  getSFTPFileList,
  downloadFile as downloadFileAPI,
  createFolder as createFolderAPI,
  deleteFile as deleteFileAPI
} from '@/api/sftp'

// 标签页接口
interface TabInfo {
  id: string
  connection: SSHConnection
  status: 'disconnected' | 'connecting' | 'connected' | 'error'
  terminal: any
  fitAddon: any
  webSocket: any
}

// 响应式数据
const connections = ref<SSHConnection[]>([])
const activeConnection = ref<SSHConnection | null>(null)
const showConnectionDialog = ref(false)
const editingConnection = ref<SSHConnection | null>(null)
const saving = ref(false)

// 多标签页相关
const openTabs = ref<TabInfo[]>([])
const activeTabId = ref<string>('')
const terminalRefs = ref<Map<string, HTMLElement>>(new Map())
const authType = ref('password')
const connectionStatus = ref('disconnected')

// 命令输入相关
const currentCommand = ref('')
const commandHistory = ref<string[]>([])
const historyIndex = ref(-1)
const commandPrompt = ref('$ ')

// 快捷命令相关
const showQuickCommandDialog = ref(false)
const showQuickCommandEditDialog = ref(false)
const editingQuickCommand = ref<any>(null)
const quickCommands = ref([
  { id: 1, name: 'CPU信息', command: 'cat /proc/cpuinfo | grep "model name" | head -1', description: '查看CPU信息' },
  { id: 2, name: '内存信息', command: 'free -h', description: '查看内存使用情况' },
  { id: 3, name: '硬盘信息', command: 'df -h', description: '查看硬盘使用情况' },
  { id: 4, name: '系统负载', command: 'uptime', description: '查看系统负载' },
  { id: 5, name: '进程列表', command: 'ps aux | head -20', description: '查看进程列表' },
  { id: 6, name: '网络连接', command: 'netstat -tuln', description: '查看网络连接' },
  { id: 7, name: '系统信息', command: 'uname -a', description: '查看系统信息' },
  { id: 8, name: '当前目录', command: 'pwd', description: '显示当前目录' }
])

// 服务器资源监控
const serverInfo = ref({
  cpu: 0,
  memory: 0,
  memoryUsed: 0,
  memoryTotal: 0,
  disk: 0,
  diskUsed: 0,
  diskTotal: 0,
  load1: '0.00',
  load5: '0.00',
  load15: '0.00'
})

// 文件管理相关
const currentPath = ref('/')
const fileList = ref<FileInfo[]>([])
const fileLoading = ref(false)
const showCreateFolderDialog = ref(false)
const newFolderName = ref('')

// 终端相关
const terminalContainer = ref<HTMLElement>()
const terminal = ref<Terminal | null>(null)
const fitAddon = ref<FitAddon | null>(null)
const sshWebSocket = ref<SSHWebSocket | null>(null)

// 表单数据
const connectionForm = reactive<SSHConnection>({
  name: '',
  host: '',
  port: 22,
  username: '',
  password: '',
  private_key: '',
  description: ''
})

// 快捷命令表单数据
const quickCommandForm = reactive({
  name: '',
  command: '',
  description: ''
})

// 计算属性
const activeTab = computed(() => {
  return openTabs.value.find(tab => tab.id === activeTabId.value) || null
})

const pathBreadcrumb = computed(() => {
  const parts = currentPath.value.split('/').filter(p => p)
  return ['根目录', ...parts]
})

const uploadUrl = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081'
  return `${baseUrl}/sftp/upload`
})

const uploadHeaders = computed(() => {
  return {
    'Authorization': `Bearer ${localStorage.getItem('token')}`
  }
})

const uploadData = computed(() => {
  return {
    connection_id: activeTab.value?.connection.id,
    path: currentPath.value
  }
})

// 表单验证规则
const connectionRules = {
  name: [
    { required: true, message: '请输入连接名称', trigger: 'blur' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' }
  ],
  port: [
    { required: true, message: '请输入端口号', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ]
}

const quickCommandRules = {
  name: [
    { required: true, message: '请输入命令名称', trigger: 'blur' }
  ],
  command: [
    { required: true, message: '请输入命令内容', trigger: 'blur' }
  ]
}

// 组件挂载
onMounted(() => {
  loadConnections()
  // 动态调整容器高度
  adjustContainerHeight()
  window.addEventListener('resize', adjustContainerHeight)

  // 为content-wrapper添加no-scroll类
  const contentWrapper = document.querySelector('.content-wrapper')
  if (contentWrapper) {
    contentWrapper.classList.add('no-scroll')
  }
})

// 动态调整容器高度
const adjustContainerHeight = () => {
  nextTick(() => {
    const container = document.querySelector('.xshell-container') as HTMLElement
    if (container) {
      // 计算可用高度：
      // 100vh - el-header(60px) - content-wrapper padding(40px)
      // 现在移除了page-header，可以使用更多空间
      const availableHeight = window.innerHeight - 60 - 40
      container.style.height = `${availableHeight}px`
      console.log('Container height set to:', availableHeight + 'px')
    }
  })
}

// 组件卸载
onUnmounted(() => {
  // 关闭所有标签页的WebSocket连接
  openTabs.value.forEach(tab => {
    if (tab.webSocket) {
      tab.webSocket.close()
    }
    if (tab.terminal) {
      try {
        tab.terminal.dispose()
      } catch (error) {
        console.warn('Terminal dispose error on unmount:', error)
      }
    }
  })

  // 清理全局终端（如果存在）
  if (terminal.value) {
    try {
      terminal.value.dispose()
    } catch (error) {
      console.warn('Terminal dispose error on unmount:', error)
    }
  }

  window.removeEventListener('resize', handleResize)
  window.removeEventListener('resize', adjustContainerHeight)

  // 移除content-wrapper的no-scroll类
  const contentWrapper = document.querySelector('.content-wrapper')
  if (contentWrapper) {
    contentWrapper.classList.remove('no-scroll')
  }
})

// 加载连接列表
const loadConnections = async () => {
  try {
    console.log('开始加载SSH连接列表...')
    console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL)
    console.log('Token:', localStorage.getItem('token'))

    const response = await getSSHConnections()
    console.log('SSH连接列表响应:', response)

    if (response.code === 200) {
      connections.value = response.data || []
      console.log('成功加载连接列表，数量:', connections.value.length)
    } else {
      console.warn('响应码不是200:', response.code)
    }
  } catch (error) {
    console.error('加载SSH连接列表失败:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error('加载SSH连接列表失败: ' + (error.response?.data?.message || error.message))
  }
}

// 选择连接 - 创建新标签页或切换到已存在的标签页
const selectConnection = async (connection: SSHConnection) => {
  console.log('选择连接:', connection)

  // 检查是否已经有这个连接的标签页
  const existingTab = openTabs.value.find(tab => tab.connection.id === connection.id)

  if (existingTab) {
    // 切换到已存在的标签页
    console.log('切换到已存在的标签页:', existingTab.id)
    switchTab(existingTab.id)
    return
  }

  // 创建新标签页
  const tabId = `tab-${Date.now()}-${connection.id}`
  const newTab: TabInfo = {
    id: tabId,
    connection: connection,
    status: 'disconnected',
    terminal: null,
    fitAddon: null,
    webSocket: null
  }

  openTabs.value.push(newTab)
  activeTabId.value = tabId
  activeConnection.value = connection

  // 等待DOM更新后初始化终端
  await nextTick()
  await initializeTerminal(tabId)
  await connectToSSH(tabId)
}

// 切换标签页
const switchTab = (tabId: string) => {
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    activeTabId.value = tabId
    activeConnection.value = tab.connection
    currentPath.value = '/'

    // 如果标签页已连接，加载文件列表
    if (tab.status === 'connected') {
      loadFileList()
    }
  }
}

// 关闭标签页
const closeTab = (tabId: string) => {
  const tabIndex = openTabs.value.findIndex(tab => tab.id === tabId)
  if (tabIndex === -1) return

  const tab = openTabs.value[tabIndex]

  // 清理资源
  if (tab.webSocket) {
    tab.webSocket.close()
  }
  if (tab.terminal) {
    try {
      tab.terminal.dispose()
    } catch (error) {
      console.warn('Terminal dispose error:', error)
    }
  }

  // 移除标签页
  openTabs.value.splice(tabIndex, 1)
  terminalRefs.value.delete(tabId)

  // 如果关闭的是活动标签页，切换到其他标签页
  if (activeTabId.value === tabId) {
    if (openTabs.value.length > 0) {
      const newActiveTab = openTabs.value[Math.max(0, tabIndex - 1)]
      switchTab(newActiveTab.id)
    } else {
      activeTabId.value = ''
      activeConnection.value = null
    }
  }
}

// 在新标签页打开大屏模式
const openInNewTab = () => {
  // 只保存已连接的标签页状态
  const connectedTabs = openTabs.value.filter(tab => tab.status === 'connected')

  // 保存当前状态到localStorage，供大屏页面使用
  const state = {
    connections: connections.value,
    openTabs: connectedTabs.map(tab => ({
      id: tab.id,
      connection: tab.connection,
      status: tab.status
    })),
    activeTabId: connectedTabs.length > 0 ? (connectedTabs.find(t => t.id === activeTabId.value)?.id || connectedTabs[0].id) : '',
    activeConnection: connectedTabs.length > 0 ? (connectedTabs.find(t => t.id === activeTabId.value)?.connection || connectedTabs[0].connection) : null,
    serverInfo: serverInfo.value,
    quickCommands: quickCommands.value,
    timestamp: Date.now()
  }

  localStorage.setItem('xshell-fullscreen-state', JSON.stringify(state))

  // 打开大屏页面
  const fullscreenUrl = window.location.origin + '/xshell-fullscreen'
  window.open(fullscreenUrl, '_blank')

  // 提示用户
  if (connectedTabs.length === 0) {
    ElMessage.info('当前没有已连接的标签页，大屏模式将显示服务器列表')
  } else {
    ElMessage.success(`已将 ${connectedTabs.length} 个连接传递到大屏模式`)
  }
}

// 设置终端容器引用
const setTerminalRef = (el: HTMLElement | null, tabId: string) => {
  if (el) {
    terminalRefs.value.set(tabId, el)
  }
}

// 初始化终端
const initializeTerminal = async (tabId: string) => {
  await nextTick()

  const tab = openTabs.value.find(t => t.id === tabId)
  if (!tab) return

  const terminalContainer = terminalRefs.value.get(tabId)
  if (!terminalContainer) {
    console.warn('Terminal container not found for tab:', tabId)
    return
  }

  // 清理现有终端
  if (tab.terminal) {
    try {
      tab.terminal.dispose()
    } catch (error) {
      console.warn('Terminal dispose error:', error)
    }
  }

  // 创建新终端
  const newTerminal = new Terminal({
    cursorBlink: true,
    fontSize: 14,
    fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
    theme: {
      background: '#1e1e1e',
      foreground: '#ffffff',
      cursor: '#ffffff',
      selection: '#3a3a3a'
    },
    scrollback: 1000,
    convertEol: true
  })

  // 添加插件
  const newFitAddon = new FitAddon()
  newTerminal.loadAddon(newFitAddon)
  newTerminal.loadAddon(new WebLinksAddon())

  // 更新标签页信息
  tab.terminal = newTerminal
  tab.fitAddon = newFitAddon

  // 挂载到DOM
  newTerminal.open(terminalContainer)

  // 等待DOM更新后调整大小
  await nextTick()
  setTimeout(() => {
    if (newFitAddon && terminalContainer) {
      const rect = terminalContainer.getBoundingClientRect()
      if (rect.width > 0 && rect.height > 0) {
        newFitAddon.fit()
      }
    }
  }, 200)

  // 监听窗口大小变化
  window.addEventListener('resize', () => handleResize(tabId))
}

// 连接SSH
const connectToSSH = async (tabId: string) => {
  const tab = openTabs.value.find(t => t.id === tabId)
  if (!tab || !tab.terminal || !tab.connection) {
    console.warn('Tab, terminal or connection not found for tabId:', tabId)
    return
  }

  console.log('开始连接SSH，tabId:', tabId, 'connection:', tab.connection)

  tab.status = 'connecting'
  tab.terminal.clear()
  tab.terminal.writeln('正在连接到 ' + tab.connection.host + ':' + tab.connection.port + '...')

  try {
    const token = localStorage.getItem('token') || ''
    console.log('Token:', token ? 'Present' : 'Missing')
    console.log('Connection ID:', tab.connection.id)

    const webSocket = new SSHWebSocket(
      tab.connection.id!,
      token,
      (message) => handleWebSocketMessage(message, tabId),
      (error) => handleWebSocketError(error, tabId),
      (event) => handleWebSocketClose(event, tabId)
    )

    tab.webSocket = webSocket

    // 设置连接超时
    const connectTimeout = setTimeout(() => {
      if (tab.status === 'connecting') {
        tab.status = 'error'
        tab.terminal?.writeln('\r\n连接超时，请检查网络连接和服务器配置')
        ElMessage.error('连接超时')
        if (tab.webSocket) {
          tab.webSocket.close()
        }
      }
    }, 15000) // 15秒超时

    await webSocket.connect()
    clearTimeout(connectTimeout)

    // 监听终端输入
    tab.terminal.onData((data) => {
      if (tab.webSocket && tab.status === 'connected') {
        tab.webSocket.sendInput(data)
      }
    })

    // 监听终端大小变化
    tab.terminal.onResize(({ cols, rows }) => {
      if (tab.webSocket && tab.status === 'connected') {
        tab.webSocket.resizeTerminal(cols, rows)
      }
    })

  } catch (error) {
    console.error('SSH connection failed:', error)
    tab.status = 'error'
    tab.terminal.writeln('\r\n连接失败: ' + error)
    ElMessage.error('连接失败: ' + error)
  }
}

// 处理WebSocket消息
const handleWebSocketMessage = (message: any, tabId: string) => {
  const tab = openTabs.value.find(t => t.id === tabId)
  if (!tab || !tab.terminal) return

  console.log('WebSocket消息:', message.type, tabId)

  switch (message.type) {
    case 'connecting':
      tab.status = 'connecting'
      tab.terminal.writeln(message.data)
      break
    case 'connected':
      tab.status = 'connected'
      tab.terminal.writeln('\r\n' + message.data)
      tab.terminal.writeln('终端已就绪，您可以开始输入命令。\r\n')
      // 连接成功后自动刷新服务器信息和文件列表
      if (activeTabId.value === tabId) {
        setTimeout(() => {
          refreshServerInfo()
          loadFileList()
        }, 2000)
      }
      break
    case 'output':
      tab.terminal.write(message.data)
      break
    case 'error':
      tab.status = 'error'
      tab.terminal.writeln('\r\n连接失败: ' + message.data)
      ElMessage.error('SSH连接失败: ' + message.data)
      break
    case 'pong':
      // 心跳响应
      break
  }
}

// 处理WebSocket错误
const handleWebSocketError = (error: Event, tabId: string) => {
  console.error('WebSocket error:', error, 'tabId:', tabId)
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    tab.status = 'error'
    if (tab.terminal) {
      tab.terminal.writeln('\r\nWebSocket连接错误')
    }
  }
  ElMessage.error('WebSocket连接错误')
}

// 处理WebSocket关闭
const handleWebSocketClose = (event: CloseEvent, tabId: string) => {
  console.log('WebSocket关闭:', event.code, event.reason, 'tabId:', tabId)
  const tab = openTabs.value.find(t => t.id === tabId)
  if (tab) {
    tab.status = 'disconnected'
    if (tab.terminal) {
      tab.terminal.writeln('\r\n连接已断开')
    }
  }
}

// 断开终端连接
const disconnectTerminal = () => {
  // 关闭所有标签页的WebSocket连接
  openTabs.value.forEach(tab => {
    if (tab.webSocket) {
      tab.webSocket.close()
    }
  })

  // 清空所有标签页
  openTabs.value = []
  activeTabId.value = ''
  activeConnection.value = null
  connectionStatus.value = 'disconnected'
}

// 防抖处理窗口大小变化
let resizeTimeout: NodeJS.Timeout | null = null
const handleResize = (tabId?: string) => {
  if (resizeTimeout) {
    clearTimeout(resizeTimeout)
  }

  resizeTimeout = setTimeout(() => {
    const tabsToResize = tabId ? [openTabs.value.find(t => t.id === tabId)].filter(Boolean) : openTabs.value

    tabsToResize.forEach(tab => {
      if (tab && tab.fitAddon && tab.terminal && tab.status === 'connected') {
        try {
          const terminalContainer = terminalRefs.value.get(tab.id)
          if (terminalContainer) {
            const rect = terminalContainer.getBoundingClientRect()
            if (rect.width > 0 && rect.height > 0) {
              tab.fitAddon.fit()
            }
          }
        } catch (error) {
          console.warn('Terminal resize failed:', error)
        }
      }
    })
  }, 200)
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'connecting':
      return '连接中...'
    case 'connected':
      return '已连接'
    case 'disconnected':
      return '已断开'
    case 'error':
      return '连接错误'
    default:
      return '未知状态'
  }
}

// 处理连接操作
const handleConnectionAction = async (command: string) => {
  const [action, idStr] = command.split('-')
  const id = parseInt(idStr)
  const connection = connections.value.find(c => c.id === id)
  
  if (!connection) return

  switch (action) {
    case 'edit':
      editConnection(connection)
      break
    case 'test':
      await testConnection(connection)
      break
    case 'delete':
      await deleteConnection(connection)
      break
  }
}

// 编辑连接
const editConnection = (connection: SSHConnection) => {
  editingConnection.value = connection
  Object.assign(connectionForm, connection)
  authType.value = connection.password ? 'password' : 'key'
  showConnectionDialog.value = true
}

// 测试连接
const testConnection = async (connection: SSHConnection) => {
  try {
    ElMessage.info('正在测试连接...')
    await testSSHConnection(connection.id!)
    ElMessage.success('连接测试成功')
  } catch (error: any) {
    ElMessage.error('连接测试失败: ' + (error.response?.data?.message || error.message))
  }
}

// 删除连接
const deleteConnection = async (connection: SSHConnection) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除连接 "${connection.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteSSHConnection(connection.id!)
    ElMessage.success('连接删除成功')
    await loadConnections()
    
    // 如果删除的是当前活动连接，断开连接
    if (activeConnection.value?.id === connection.id) {
      disconnectTerminal()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除连接失败')
    }
  }
}

// 保存连接
const saveConnection = async () => {
  // 清理认证信息
  if (authType.value === 'password') {
    connectionForm.private_key = ''
  } else {
    connectionForm.password = ''
  }

  saving.value = true
  try {
    if (editingConnection.value) {
      await updateSSHConnection(editingConnection.value.id!, connectionForm)
      ElMessage.success('连接更新成功')
    } else {
      await createSSHConnection(connectionForm)
      ElMessage.success('连接创建成功')
    }
    
    showConnectionDialog.value = false
    await loadConnections()
  } catch (error: any) {
    ElMessage.error('保存连接失败: ' + (error.response?.data?.message || error.message))
  } finally {
    saving.value = false
  }
}

// 执行命令
const executeCommand = () => {
  const tab = openTabs.value.find(t => t.id === activeTabId.value)
  if (!currentCommand.value.trim() || !tab || !tab.webSocket || tab.status !== 'connected') {
    if (!tab || !tab.webSocket || tab.status !== 'connected') {
      ElMessage.warning('请先连接到SSH服务器')
    }
    return
  }

  const command = currentCommand.value.trim()

  // 添加到历史记录
  if (commandHistory.value[commandHistory.value.length - 1] !== command) {
    commandHistory.value.push(command)
    if (commandHistory.value.length > 100) {
      commandHistory.value.shift()
    }
  }
  historyIndex.value = -1

  // 发送命令
  console.log('执行命令:', command)
  tab.webSocket.sendInput(command + '\n')
  currentCommand.value = ''
}

// 执行快捷命令
const executeQuickCommand = (command: string) => {
  const tab = openTabs.value.find(t => t.id === activeTabId.value)
  if (!tab || !tab.webSocket || tab.status !== 'connected') {
    ElMessage.warning('请先连接到SSH服务器')
    return
  }
  console.log('执行快捷命令:', command)
  tab.webSocket.sendInput(command + '\n')
}

// 命令历史导航
const navigateHistory = (direction: number) => {
  if (commandHistory.value.length === 0) return

  if (direction === -1) { // 上箭头
    if (historyIndex.value === -1) {
      historyIndex.value = commandHistory.value.length - 1
    } else if (historyIndex.value > 0) {
      historyIndex.value--
    }
  } else { // 下箭头
    if (historyIndex.value === -1) return
    if (historyIndex.value < commandHistory.value.length - 1) {
      historyIndex.value++
    } else {
      historyIndex.value = -1
      currentCommand.value = ''
      return
    }
  }

  if (historyIndex.value >= 0) {
    currentCommand.value = commandHistory.value[historyIndex.value]
  }
}

// 自动补全（简单实现）
const autoComplete = () => {
  // 这里可以实现更复杂的自动补全逻辑
  console.log('Auto complete triggered')
}

// 添加快捷命令
const addQuickCommand = () => {
  editingQuickCommand.value = null
  Object.assign(quickCommandForm, {
    name: '',
    command: '',
    description: ''
  })
  showQuickCommandEditDialog.value = true
}

// 编辑快捷命令
const editQuickCommand = (command: any) => {
  editingQuickCommand.value = command
  Object.assign(quickCommandForm, command)
  showQuickCommandEditDialog.value = true
}

// 删除快捷命令
const deleteQuickCommand = (command: any) => {
  const index = quickCommands.value.findIndex(cmd => cmd.id === command.id)
  if (index > -1) {
    quickCommands.value.splice(index, 1)
    ElMessage.success('快捷命令删除成功')
  }
}

// 保存快捷命令
const saveQuickCommand = () => {
  if (editingQuickCommand.value) {
    // 更新现有命令
    Object.assign(editingQuickCommand.value, quickCommandForm)
    ElMessage.success('快捷命令更新成功')
  } else {
    // 添加新命令
    const newCommand = {
      id: Date.now(),
      ...quickCommandForm
    }
    quickCommands.value.push(newCommand)
    ElMessage.success('快捷命令添加成功')
  }
  showQuickCommandEditDialog.value = false
}

// 重置快捷命令表单
const resetQuickCommandForm = () => {
  editingQuickCommand.value = null
  Object.assign(quickCommandForm, {
    name: '',
    command: '',
    description: ''
  })
}

// 刷新服务器信息
const refreshServerInfo = async () => {
  if (!activeConnection.value) {
    console.log('No active connection, skipping server info refresh')
    return
  }

  console.log('Refreshing server info for connection:', activeConnection.value.id)

  try {
    const response = await getServerInfo(activeConnection.value.id!)
    console.log('Server info API response:', response)

    if (response.code === 200) {
      console.log('Server info data:', response.data)
      serverInfo.value = response.data
      console.log('Updated serverInfo.value:', serverInfo.value)
    } else {
      console.error('API returned non-200 code:', response.code, response.message)
    }
  } catch (error: any) {
    console.error('Failed to get server info:', error)
    console.error('Error details:', error.response?.data)

    // 如果API调用失败，使用模拟数据
    console.log('Using fallback mock data')
    serverInfo.value = {
      cpu: Math.floor(Math.random() * 100),
      memory: Math.floor(Math.random() * 100),
      memoryUsed: Math.floor(Math.random() * 8),
      memoryTotal: 8,
      disk: Math.floor(Math.random() * 100),
      diskUsed: Math.floor(Math.random() * 50),
      diskTotal: 100,
      load1: (Math.random() * 2).toFixed(2),
      load5: (Math.random() * 2).toFixed(2),
      load15: (Math.random() * 2).toFixed(2)
    }
  }
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 文件管理方法
const loadFileList = async () => {
  if (!activeConnection.value) return

  fileLoading.value = true
  try {
    const response = await getSFTPFileList(activeConnection.value.id!, currentPath.value)
    if (response.code === 200) {
      let files = response.data || []

      // 如果不是根目录，添加返回上级目录的选项
      if (currentPath.value !== '/') {
        files.unshift({
          name: '..',
          size: 0,
          isDirectory: true,
          modTime: new Date().toISOString(),
          permissions: 'drwxr-xr-x'
        })
      }

      fileList.value = files
    }
  } catch (error: any) {
    console.error('Failed to load file list:', error)
  } finally {
    fileLoading.value = false
  }
}

const refreshFileList = () => {
  loadFileList()
}

const handleFileDoubleClick = (row: FileInfo) => {
  if (row.isDirectory) {
    if (row.name === '..') {
      // 返回上级目录
      const pathParts = currentPath.value.split('/').filter(p => p)
      pathParts.pop()
      currentPath.value = pathParts.length > 0 ? '/' + pathParts.join('/') : '/'
    } else {
      // 进入子目录
      if (currentPath.value === '/') {
        currentPath.value = '/' + row.name
      } else {
        currentPath.value = currentPath.value + '/' + row.name
      }
    }
    loadFileList()
  }
}

const navigateToPath = (index: number) => {
  if (index === 0) {
    // 点击根目录
    currentPath.value = '/'
  } else {
    // 点击其他路径
    const pathParts = pathBreadcrumb.value.slice(1, index + 1) // 跳过"根目录"
    currentPath.value = '/' + pathParts.join('/')
  }
  loadFileList()
}

const createFolder = async () => {
  if (!newFolderName.value.trim() || !activeConnection.value) return

  try {
    const response = await createFolderAPI(
      activeConnection.value.id!,
      currentPath.value,
      newFolderName.value.trim()
    )

    if (response.code === 200) {
      ElMessage.success('文件夹创建成功')
      showCreateFolderDialog.value = false
      newFolderName.value = ''
      await loadFileList()
    }
  } catch (error: any) {
    ElMessage.error('创建文件夹失败')
  }
}

const downloadFile = async (file: FileInfo) => {
  if (!activeConnection.value) return

  try {
    const filePath = currentPath.value === '/'
      ? '/' + file.name
      : currentPath.value + '/' + file.name

    const response = await downloadFileAPI(activeConnection.value.id!, filePath)

    // 创建下载链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = file.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下载成功')
  } catch (error: any) {
    ElMessage.error('下载文件失败')
  }
}

const deleteFile = async (file: FileInfo) => {
  if (!activeConnection.value) return

  try {
    await ElMessageBox.confirm(`确定要删除 "${file.name}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const filePath = currentPath.value === '/'
      ? '/' + file.name
      : currentPath.value + '/' + file.name

    const response = await deleteFileAPI(activeConnection.value.id!, filePath, file.isDirectory)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      await loadFileList()
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleUploadSuccess = () => {
  ElMessage.success('文件上传成功')
  loadFileList()
}

const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 文件图标和工具方法
const getFileIcon = (file: FileInfo) => {
  if (file.name === '..') return ArrowUp
  return file.isDirectory ? Folder : Document
}

const getFileIconClass = (file: FileInfo) => {
  if (file.name === '..') return 'parent-dir-icon'
  return file.isDirectory ? 'folder-icon' : 'file-icon'
}

const formatFileSize = (size: number) => {
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  if (size < 1024 * 1024 * 1024) return (size / 1024 / 1024).toFixed(1) + ' MB'
  return (size / 1024 / 1024 / 1024).toFixed(1) + ' GB'
}

// 重置连接表单
const resetConnectionForm = () => {
  editingConnection.value = null
  Object.assign(connectionForm, {
    name: '',
    host: '',
    port: 22,
    username: '',
    password: '',
    private_key: '',
    description: ''
  })
  authType.value = 'password'
}
</script>

<style scoped>
.xshell-container {
  height: calc(100vh - 60px - 40px); /* 100vh - el-header - content-wrapper padding，移除page-header后有更多空间 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}



.main-content {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
  overflow: hidden;
  height: 100%; /* 使用全部可用高度 */
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
  overflow: hidden;
}

.page-actions {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;
  flex-shrink: 0;
}

.terminal-and-file {
  flex: 1;
  display: flex;
  gap: 20px;
  min-height: 0;
  overflow: hidden;
}

.connection-sidebar {
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 连接列表区域 */
.connections-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.connection-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.connection-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background-color 0.2s;
}

.connection-item:hover {
  background-color: #f5f7fa;
}

.connection-item.active {
  background-color: #ecf5ff;
  border-left: 3px solid #409eff;
}

.connection-info {
  flex: 1;
}

.connection-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
  font-size: 13px;
}

.connection-details {
  font-size: 11px;
  color: #909399;
}

/* 监控面板区域 */
.monitor-section {
  border-top: 1px solid #ebeef5;
  background: #f8f9fa;
  flex-shrink: 0;
}

.monitor-section .monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.monitor-section .monitor-header h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.monitor-content {
  padding: 12px 16px;
  max-height: 300px;
  overflow-y: auto;
}

.resource-item {
  margin-bottom: 16px;
}

.resource-item:last-child {
  margin-bottom: 0;
}

.resource-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 6px;
  font-weight: 500;
}

.resource-detail {
  font-size: 11px;
  color: #606266;
  margin-top: 6px;
  text-align: center;
  font-weight: 500;
}

.load-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.load-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: white;
  border-radius: 4px;
  font-size: 11px;
}

.load-label {
  color: #606266;
}

.load-value {
  color: #303133;
  font-weight: 500;
}

.terminal-area {
  flex: 2;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.file-manager-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.file-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-left h3 {
  margin: 0;
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.path-breadcrumb {
  font-size: 12px;
}

.breadcrumb-item {
  cursor: pointer;
}

.breadcrumb-item:hover {
  color: #409eff;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.file-list-container {
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 14px;
}

.folder-icon {
  color: #409eff;
}

.file-icon {
  color: #909399;
}

.parent-dir {
  color: #409eff;
  font-weight: 500;
}

.parent-dir-icon {
  color: #409eff;
}

.file-name {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-connection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.terminal-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.terminal-tabs {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
  flex-shrink: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border-right: 1px solid #ebeef5;
  font-size: 14px;
}

.tab-item.active {
  background: #409eff;
  color: white;
}

.terminal-content {
  flex: 1;
  min-height: 0;
  max-height: 100%;
  overflow: hidden;
  position: relative;
  display: none;
}

.terminal-content.active {
  display: block;
}

.terminal-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f5f7fa;
  border-top: 1px solid #ebeef5;
  font-size: 12px;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.status-indicator.connecting {
  background: #e6f7ff;
  color: #1890ff;
}

.status-indicator.connected {
  background: #f6ffed;
  color: #52c41a;
}

.status-indicator.disconnected {
  background: #fff2e8;
  color: #fa8c16;
}

.status-indicator.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.connection-info {
  color: #909399;
}



/* 命令输入区域样式 */
.command-input-area {
  border-top: 1px solid #ebeef5;
  background: #f8f9fa;
  flex-shrink: 0;
}

.quick-commands {
  padding: 8px 16px;
  border-bottom: 1px solid #ebeef5;
}

.quick-commands-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
}

.quick-command-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.quick-command-buttons .el-button {
  font-size: 11px;
  padding: 3px 6px;
  height: auto;
  min-height: 24px;
}

.command-input {
  padding: 8px 16px;
}

.command-prompt {
  color: #67c23a;
  font-weight: 500;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 快捷命令管理样式 */
.quick-command-manager {
  max-height: 400px;
  overflow-y: auto;
}

.manager-header {
  margin-bottom: 16px;
}
</style>


